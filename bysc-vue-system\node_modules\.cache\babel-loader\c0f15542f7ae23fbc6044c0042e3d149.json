{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\copy.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\copy.js", "mtime": 1745205562782}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["// 用法是在组件上加上v-copy=\"对应的变量\"，这是类似v-show v-if\nimport { Message } from 'view-design';\nvar copy = {\n  bind: function bind(el, _ref) {\n    var value = _ref.value;\n    el.$value = value;\n    el.handler = function () {\n      if (!el.$value) {\n        // 值为空的时候，给出提示。可根据项目UI仔细设计\n        console.log('无复制内容');\n        Message.error('无复制内容');\n        return;\n      }\n      // 动态创建 textarea 标签\n      var textarea = document.createElement('textarea');\n      // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      // 将要 copy 的值赋给 textarea 标签的 value 属性\n      textarea.value = el.$value;\n      // 将 textarea 插入到 body 中\n      document.body.appendChild(textarea);\n      // 选中值并复制\n      textarea.select();\n      var result = document.execCommand('Copy');\n      if (result) {\n        Message.success('复制成功');\n      }\n      document.body.removeChild(textarea);\n    };\n    // 绑定点击事件，就是所谓的一键 copy 啦\n    el.addEventListener('click', el.handler);\n  },\n  // 当传进来的值更新的时候触发\n  componentUpdated: function componentUpdated(el, _ref2) {\n    var value = _ref2.value;\n    el.$value = value;\n  },\n  // 指令与元素解绑的时候，移除事件绑定\n  unbind: function unbind(el) {\n    el.removeEventListener('click', el.handler);\n  }\n};\nexport default copy;", {"version": 3, "names": ["Message", "copy", "bind", "el", "_ref", "value", "$value", "handler", "console", "log", "error", "textarea", "document", "createElement", "readOnly", "style", "position", "left", "body", "append<PERSON><PERSON><PERSON>", "select", "result", "execCommand", "success", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "componentUpdated", "_ref2", "unbind", "removeEventListener"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/copy.js"], "sourcesContent": ["// 用法是在组件上加上v-copy=\"对应的变量\"，这是类似v-show v-if\r\nimport {Message} from 'view-design';\r\nconst copy = {\r\n  bind(el, {value}) {\r\n    el.$value = value;\r\n    el.handler = () => {\r\n      if (!el.$value) {\r\n        // 值为空的时候，给出提示。可根据项目UI仔细设计\r\n        console.log('无复制内容');\r\n        Message.error('无复制内容');\r\n        return;\r\n      }\r\n      // 动态创建 textarea 标签\r\n      const textarea = document.createElement('textarea');\r\n      // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域\r\n      textarea.readOnly = 'readonly';\r\n      textarea.style.position = 'absolute';\r\n      textarea.style.left = '-9999px';\r\n      // 将要 copy 的值赋给 textarea 标签的 value 属性\r\n      textarea.value = el.$value;\r\n      // 将 textarea 插入到 body 中\r\n      document.body.appendChild(textarea);\r\n      // 选中值并复制\r\n      textarea.select();\r\n      const result = document.execCommand('Copy');\r\n      if (result) {\r\n        Message.success('复制成功');\r\n      }\r\n      document.body.removeChild(textarea);\r\n    };\r\n    // 绑定点击事件，就是所谓的一键 copy 啦\r\n    el.addEventListener('click', el.handler);\r\n  },\r\n  // 当传进来的值更新的时候触发\r\n  componentUpdated(el, {value}) {\r\n    el.$value = value;\r\n  },\r\n  // 指令与元素解绑的时候，移除事件绑定\r\n  unbind(el) {\r\n    el.removeEventListener('click', el.handler);\r\n  },\r\n};\r\n\r\nexport default copy;"], "mappings": "AAAA;AACA,SAAQA,OAAO,QAAO,aAAa;AACnC,IAAMC,IAAI,GAAG;EACXC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAAC,IAAA,EAAW;IAAA,IAARC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACbF,EAAE,CAACG,MAAM,GAAGD,KAAK;IACjBF,EAAE,CAACI,OAAO,GAAG,YAAM;MACjB,IAAI,CAACJ,EAAE,CAACG,MAAM,EAAE;QACd;QACAE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;QACpBT,OAAO,CAACU,KAAK,CAAC,OAAO,CAAC;QACtB;MACF;MACA;MACA,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnD;MACAF,QAAQ,CAACG,QAAQ,GAAG,UAAU;MAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;MACpCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,SAAS;MAC/B;MACAN,QAAQ,CAACN,KAAK,GAAGF,EAAE,CAACG,MAAM;MAC1B;MACAM,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;MACnC;MACAA,QAAQ,CAACS,MAAM,CAAC,CAAC;MACjB,IAAMC,MAAM,GAAGT,QAAQ,CAACU,WAAW,CAAC,MAAM,CAAC;MAC3C,IAAID,MAAM,EAAE;QACVrB,OAAO,CAACuB,OAAO,CAAC,MAAM,CAAC;MACzB;MACAX,QAAQ,CAACM,IAAI,CAACM,WAAW,CAACb,QAAQ,CAAC;IACrC,CAAC;IACD;IACAR,EAAE,CAACsB,gBAAgB,CAAC,OAAO,EAAEtB,EAAE,CAACI,OAAO,CAAC;EAC1C,CAAC;EACD;EACAmB,gBAAgB,WAAhBA,gBAAgBA,CAACvB,EAAE,EAAAwB,KAAA,EAAW;IAAA,IAARtB,KAAK,GAAAsB,KAAA,CAALtB,KAAK;IACzBF,EAAE,CAACG,MAAM,GAAGD,KAAK;EACnB,CAAC;EACD;EACAuB,MAAM,WAANA,MAAMA,CAACzB,EAAE,EAAE;IACTA,EAAE,CAAC0B,mBAAmB,CAAC,OAAO,EAAE1B,EAAE,CAACI,OAAO,CAAC;EAC7C;AACF,CAAC;AAED,eAAeN,IAAI", "ignoreList": []}]}
<!--
 * @Author: czw
 * @Date: 2022-02-25 14:53:58
 * @LastEditors: czw
 * @LastEditTime: 2022-04-26 09:10:43
 * @FilePath: \kdsp_vue\src\views\404.vue
 * @Description:
 *
 * Copyright (c)  by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <img src="./404.png" alt="404" style="width: 20%; margin-left: 40%; margin-top: 160px" />
    <div class="textStyle">糟糕！页面找不到了~~</div>
    <div class="btn" @click="goToHome"><a href="#" style="color: #fff">返回首页</a></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      setInterval: null
    };
  },

  components: {},

  computed: {},

  mounted() {},
  beforeDestroy() {
    clearInterval(this.setInterval);
    this.setInterval = null;
  },
  methods: {
    goToHome() {
      let permissions = this.$localCache.getLocal('routepermission').split(',');
      let _this = this;
      let num = 0;
      this.setInterval = setInterval(() => {
        _this.$router.push({
          path: permissions[num]
        });
        console.log(permissions[num]);
        num += 1;
      }, 1000);
    }
  }
};
</script>
<style lang="less" scoped>
.textStyle {
  text-align: center;
  margin-top: 35px;
  font-size: 18px;
  font-weight: bold;
  color: #cccccc;
}
.btn {
  width: 8%;
  margin-left: 46%;
  margin-top: 80px;
  height: 32px;
  color: #fff;
  background: #1a6fd1;
  opacity: 0.68;
  line-height: 32px;
  border-radius: 5px;
  text-align: center;
}
</style>

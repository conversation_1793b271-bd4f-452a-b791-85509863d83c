{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\const\\app.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\const\\app.js", "mtime": 1745205562805}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["export default [{\n  name: 'PERMISS<PERSON>',\n  value: {\n    'ADMIN': 1,\n    'SPACE': 2\n  }\n}];", {"version": 3, "names": ["name", "value"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/const/app.js"], "sourcesContent": ["export default [{\r\n  name: 'PERMISS<PERSON>',\r\n  value: {\r\n    'ADMIN': 1,\r\n    'SPACE': 2\r\n  }\r\n}];\r\n"], "mappings": "AAAA,eAAe,CAAC;EACdA,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACL,OAAO,EAAE,CAAC;IACV,OAAO,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}]}
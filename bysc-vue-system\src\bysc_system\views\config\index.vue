<template>
  <div>
    <el-card>
      <el-form ref="formValidate" :model="sysconfig" :rules="ruleValidate" label-width="110px">
        <el-row>
          <el-col :span="24">
            <div style="width: 100%;margin:0 0 10px 0;">
              <el-alert show-icon :closable="false" title="密码设置"></el-alert>
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="密码复杂度" prop="complexDictId">
              <el-select clearable filterable size="small" v-model.trim="sysconfig.complexDictId" style="width: 220px;">
                <el-option v-for="item in complexList" :value="item.value" :label="item.label" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="密码长度" prop="minlength">
              <el-input-number
                size="small"
                v-model.trim="sysconfig.minlength"
                :min="6"
                :max="16"
                placeholder="请输入密码长度"
                :step="1"
                :precision="0"
                :controls="false"
                style="width: 220px;">
              </el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="密码时限" prop="pwdValid">
              <el-input   style="width: 220px;" size="small" type="number" placeholder="请输入密码时限" v-model="sysconfig.pwdValid">
              <template slot="append">天</template>
            </el-input>
            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="超时退出" prop="accessTokenValidity">
              <el-input   style="width: 220px;" size="small" type="number" placeholder="请输入超时退出" v-model.number="sysconfig.accessTokenValidity">
              <template slot="append">分钟</template>
            </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <div style="float: right">
              <el-button v-permission="'config_save'" size="small" :loading="okLoading" @click="ok" type="primary">保存</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  inject: ['reload'],
  components: {
  },
  data() {
    return {
      okLoading: false,
      ruleValidate: {
        complexDictId: [
          {
            required: true,
            validator: (rule, value, callBack) => {
              if (!value) {
                callBack('请选择密码复杂度');
              } else {
                callBack();
              }
            }
          }
        ],
        minlength: [
          {
            required: true,
            validator: (rule, value, callBack) => {
              if (!value || value == 0) {
                callBack('密码长度必须大于0');
              } else {
                callBack();
              }
            }
          }
        ],
        pwdValid: [
          {
            required: true,
            validator: (rule, value, callBack) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callBack('密码时限必须大于0的正整数');
              } else {
                callBack();
              }
            }
          }
        ],

        accessTokenValidity: [
          {
            required: true,
            validator: (rule, value, callBack) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callBack('超时退出时间必须大于0的正整数');
              } else {
                callBack();
              }
            }
          }
        ],
      },
      sysconfig: {
        complexDictId: null,
        minlength: null,
        pwdValid: 7,
        accessTokenValidity: 0
      },
      complexList: []
    };
  },
  created() {
    this.getComplexList();
    this.getSysDetail();
  },
  methods: {
    getSysDetail() {
      this.$api['config/baseSystemSetting-get']({id: 1}).then(data => {
        this.sysconfig = Object.assign(this.sysconfig, data);
      });
    },
    getComplexList() {
      this.$api["sysDict/getParam"]({id: "PASSWORD_COMPLEXITY"}).then(data => {
        this.complexList = [];
        data.forEach(e => {
          this.complexList.push({
            label: e.dictName,
            value: e.dictCode,
          });
        });
      });
    },
    ok() {
      let that = this;
      console.log("=================");
      this.$refs.formValidate.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.sysconfig.complexName = this.getCnName(this.complexList, [this.sysconfig.complexDictId], 'paramValue', 'displayCnValue');
          this.$api['config/baseSystemSetting-save'](this.sysconfig).then(() => {
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.getSysDetail();
            this.okLoading = false;
          }).catch(() => {
            that.okLoading = false;
          });
        }
      });
    },
    getCnName(allarr, selectedarr, showkey, showLabel) {
      let names = [];
      allarr.forEach(row => {
        let issome = selectedarr.some(e => {
          return e == row[showkey];
        });
        if (issome) {
          names.push(row[showLabel]);
        }
      });
      return names.join(',');
    },
  },
};
</script>
<style scoped lang="less">
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\api.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\api.js", "mtime": 1745205562793}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es7.object.values\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _classCallCheck from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.split\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport axios from \"./axios\";\nimport qs from 'qs';\nimport CryptoJS from 'crypto-js';\nimport { API_DEFAULT_CONFIG } from '@/config';\n// 引入系统API MAP\nimport { API_CONFIG } from '@/global/module-config';\nimport { assert } from '@/utils';\nimport { assign, isEmpty } from 'lodash';\nimport localCache from '@/utils/storage';\n\n// 解密函数 - 用于解密密钥\nfunction decryptKey(encryptedKey) {\n  try {\n    return atob(encryptedKey);\n  } catch (error) {\n    console.error('Key decryption failed:', error);\n    return '';\n  }\n}\n\n// 加密函数\nfunction encryptVud(username) {\n  var timestamp = new Date().getTime();\n  var random = Math.floor(Math.random() * 1000000); // 添加6位随机数\n  var str = \"\".concat(username, \"+\").concat(timestamp).concat(random);\n\n  // 使用正确的加密后的key\n  var encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\n  var cryptoKey = decryptKey(encryptedKey);\n  try {\n    var key = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var iv = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var strUtf8 = CryptoJS.enc.Utf8.parse(str);\n    var encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\n      iv: iv,\n      mode: CryptoJS.mode.CBC,\n      padding: CryptoJS.pad.Pkcs7\n    });\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\n  } catch (error) {\n    console.error('Vud encryption failed:', error);\n    return '';\n  }\n}\n\n// 添加 JWT token 解析函数\nfunction parseJwt(token) {\n  try {\n    if (!token) {\n      return null;\n    }\n    var base64Url = token.split('.')[1];\n    var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    var jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Token parsing failed:', error);\n    return null;\n  }\n}\nvar MakeApi = /*#__PURE__*/function () {\n  function MakeApi(options) {\n    _classCallCheck(this, MakeApi);\n    this.api = {};\n    this.apiBuilder(options);\n  }\n  return _createClass(MakeApi, [{\n    key: \"apiBuilder\",\n    value: function apiBuilder(_ref) {\n      var _this = this;\n      var _ref$sep = _ref.sep,\n        sep = _ref$sep === void 0 ? '|' : _ref$sep,\n        _ref$debug = _ref.debug,\n        debug = _ref$debug === void 0 ? false : _ref$debug,\n        _ref$config = _ref.config,\n        config = _ref$config === void 0 ? {} : _ref$config;\n      Object.keys(config).map(function (namespace) {\n        _this._apiSingleBuilder({\n          namespace: namespace,\n          sep: sep,\n          debug: debug,\n          config: config[namespace]\n        });\n      });\n    }\n  }, {\n    key: \"_apiSingleBuilder\",\n    value: function _apiSingleBuilder(_ref2) {\n      var _this2 = this;\n      var namespace = _ref2.namespace,\n        sep = _ref2.sep,\n        debug = _ref2.debug,\n        config = _ref2.config;\n      config.forEach(function (api) {\n        var name = api.name,\n          params = api.params,\n          method = api.method,\n          path = api.path,\n          noCheck = api.noCheck;\n        var apiName = \"\".concat(namespace).concat(sep).concat(name);\n        var apiUrl = path;\n        debug && assert(name, \"\".concat(apiUrl, \": \\u63A5\\u53E3name\\u5C5E\\u6027\\u4E0D\\u80FD\\u4E3A\\u7A7A\"));\n        debug && assert(apiUrl.indexOf('/') === 0, \"\".concat(apiUrl, \": \\u63A5\\u53E3\\u8DEF\\u5F84path\\uFF0C\\u9996\\u5B57\\u7B26\\u5E94\\u4E3A/\"));\n        Object.defineProperty(_this2.api, apiName, {\n          value: function value(outerParams, outerOptions) {\n            var _params;\n            if (Array.isArray(outerParams)) {\n              _params = outerParams;\n            } else {\n              _params = isEmpty(outerParams) ? params : assign({}, params, outerParams);\n            }\n            var url = _replaceURLparams('/api' + apiUrl, _params);\n            var reg = /<[^>]+>/g; // 违规字符检测正则\n            var count = 0; // 参数中含有违规标签个数\n            _params && Object.keys(_params).forEach(function (e) {\n              if (reg.test(_params[e])) {\n                count += 1; // 如果检测到则数量加1\n              }\n            });\n            // 如果存在不合规的标签则阻止请求,如果noCheck为true\n            if (count && !noCheck) {\n              // 如果这个接口noCheck为false就代表需要检测，且有参数中存在违规字符，则将接口地址变掉\n              url = '/app/ics/check-param-fail';\n              method = 'POST';\n            }\n            return axios(_normoalize(assign({\n              url: url,\n              method: method\n            }, outerOptions), _params)).catch(function (error) {\n              // 全局捕获异常，后续继续then，如果去掉本段代码，则后续不继续then\n              switch (error.response ? error.response.status : 500) {\n                case 400:\n                case 404:\n                case 405:\n                case 500:\n                  throw error;\n                default:\n                  break;\n              }\n            });\n          }\n        });\n      });\n    }\n  }]);\n}();\nfunction _replaceURLparams(url, data) {\n  return url.replace(/:([\\w\\d]+)/ig, function (reg, key) {\n    return data[key];\n  });\n}\nfunction _normoalize(options, data) {\n  var token = $cookies.get('userToken');\n  // 从 token 中解析出 account\n  var tokenData = parseJwt(token);\n  var username = tokenData ? tokenData.account : '';\n  // 如果没有从token中获取到username，尝试从其他地方获取\n  var finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\n  var Vud = encryptVud(finalUsername);\n  var method = options.method.toUpperCase();\n\n  // 基础请求头\n  var baseHeaders = {\n    'Authorization': 'Bearer ' + token\n  };\n\n  // 添加 Vud 到请求头，即使是微服务跳转的情况\n  baseHeaders.Vud = Vud;\n  if (['POST', 'PUT', 'PATCH'].indexOf(method) > -1) {\n    options.data = data;\n    options.headers = baseHeaders;\n  } else if (['POST_FORM'].indexOf(method) > -1) {\n    options.data = qs.stringify(data);\n    if (options.url !== '/ics/account/login' && options.url !== '/ics/account/update-password-for-expired') {\n      options.headers = _objectSpread(_objectSpread({}, baseHeaders), {}, {\n        'X-Requested-With': 'XMLHttpRequest',\n        'content-type': 'application/x-www-form-urlencoded'\n      });\n    } else {\n      options.headers = {\n        'X-Requested-With': 'XMLHttpRequest',\n        'content-type': 'application/x-www-form-urlencoded',\n        'Vud': Vud\n      };\n    }\n    options.method = 'POST';\n  } else if (['POST_TOKEN'].indexOf(method) > -1) {\n    options.data = qs.stringify(data);\n    options.method = 'post';\n    options.headers = {\n      'Authorization': \"Basic \".concat(process.env.VUE_APP_CLIENT_SECRET),\n      'content-type': 'application/x-www-form-urlencoded',\n      'Vud': Vud\n    };\n  } else if (['OTHER'].indexOf(method) > -1) {\n    options.headers = baseHeaders;\n    options.url += \"/\".concat(Object.values(data).join('/'));\n    options.method = 'post';\n  } else if (['OTHERGET'].indexOf(method) > -1) {\n    options.headers = baseHeaders;\n    options.url += \"/\".concat(Object.values(data).join('/'));\n    options.method = 'get';\n  } else if (['DELETE'].indexOf(method) > -1) {\n    options.headers = baseHeaders;\n    options.url += \"/\".concat(Object.values(data).join('/'));\n    options.method = 'post';\n  } else {\n    options.params = data;\n    options.headers = baseHeaders;\n  }\n  return options;\n}\nexport default new MakeApi(_objectSpread({\n  config: API_CONFIG\n}, API_DEFAULT_CONFIG)).api;", {"version": 3, "names": ["axios", "qs", "CryptoJS", "API_DEFAULT_CONFIG", "API_CONFIG", "assert", "assign", "isEmpty", "localCache", "decrypt<PERSON>ey", "encrypted<PERSON>ey", "atob", "error", "console", "encryptVud", "username", "timestamp", "Date", "getTime", "random", "Math", "floor", "str", "concat", "cryptoKey", "key", "enc", "Utf8", "parse", "iv", "strUtf8", "encrypted", "AES", "encrypt", "mode", "CBC", "padding", "pad", "Pkcs7", "Base64", "stringify", "ciphertext", "parseJwt", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "map", "c", "charCodeAt", "toString", "slice", "join", "JSON", "MakeApi", "options", "_classCallCheck", "api", "apiBuilder", "_createClass", "value", "_ref", "_this", "_ref$sep", "sep", "_ref$debug", "debug", "_ref$config", "config", "Object", "keys", "namespace", "_apiSingleBuilder", "_ref2", "_this2", "for<PERSON>ach", "name", "params", "method", "path", "<PERSON><PERSON><PERSON><PERSON>", "apiName", "apiUrl", "indexOf", "defineProperty", "outerParams", "outerOptions", "_params", "Array", "isArray", "url", "_replaceURLparams", "reg", "count", "e", "test", "_normoalize", "catch", "response", "status", "data", "$cookies", "get", "tokenData", "account", "finalUsername", "getLocal", "<PERSON><PERSON>", "toUpperCase", "baseHeaders", "headers", "_objectSpread", "process", "env", "VUE_APP_CLIENT_SECRET", "values"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/api.js"], "sourcesContent": ["import axios from './axios';\r\nimport qs from 'qs';\r\nimport CryptoJS from 'crypto-js';\r\nimport {\r\n  API_DEFAULT_CONFIG\r\n} from '@/config';\r\n// 引入系统API MAP\r\nimport {\r\n  API_CONFIG\r\n} from '@/global/module-config';\r\nimport {\r\n  assert\r\n} from '@/utils';\r\nimport {\r\n  assign,\r\n  isEmpty\r\n} from 'lodash';\r\nimport localCache from '@/utils/storage';\r\n\r\n// 解密函数 - 用于解密密钥\r\nfunction decryptKey(encryptedKey) {\r\n  try {\r\n    return atob(encryptedKey);\r\n  } catch (error) {\r\n    console.error('Key decryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 加密函数\r\nfunction encryptVud(username) {\r\n  const timestamp = new Date().getTime();\r\n  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数\r\n  const str = `${username}+${timestamp}${random}`;\r\n\r\n  // 使用正确的加密后的key\r\n  const encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\r\n  const cryptoKey = decryptKey(encryptedKey);\r\n\r\n  try {\r\n    const key = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const strUtf8 = CryptoJS.enc.Utf8.parse(str);\r\n    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\r\n      iv: iv,\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7\r\n    });\r\n\r\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\r\n  } catch (error) {\r\n    console.error('Vud encryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 添加 JWT token 解析函数\r\nfunction parseJwt(token) {\r\n  try {\r\n    if (!token) {\r\n      return null;\r\n    }\r\n    const base64Url = token.split('.')[1];\r\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {\r\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\r\n    }).join(''));\r\n    return JSON.parse(jsonPayload);\r\n  } catch (error) {\r\n    console.error('Token parsing failed:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nclass MakeApi {\r\n  constructor(options) {\r\n    this.api = {};\r\n    this.apiBuilder(options);\r\n  }\r\n\r\n  apiBuilder({\r\n    sep = '|',\r\n    debug = false,\r\n    config = {}\r\n  }) {\r\n    Object.keys(config).map(namespace => {\r\n      this._apiSingleBuilder({\r\n        namespace,\r\n        sep,\r\n        debug,\r\n        config: config[namespace]\r\n      });\r\n    });\r\n  }\r\n\r\n  _apiSingleBuilder({\r\n    namespace,\r\n    sep,\r\n    debug,\r\n    config\r\n  }) {\r\n    config.forEach(api => {\r\n      let {\r\n        name,\r\n        params,\r\n        method,\r\n        path,\r\n        noCheck\r\n      } = api;\r\n      const apiName = `${namespace}${sep}${name}`;\r\n      const apiUrl = path;\r\n\r\n      debug && assert(name, `${apiUrl}: 接口name属性不能为空`);\r\n      debug && assert(apiUrl.indexOf('/') === 0, `${apiUrl}: 接口路径path，首字符应为/`);\r\n      Object.defineProperty(this.api, apiName, {\r\n        value(outerParams, outerOptions) {\r\n          let _params;\r\n          if (Array.isArray(outerParams)) {\r\n            _params = outerParams;\r\n          } else {\r\n            _params = isEmpty(outerParams) ? params : assign({}, params, outerParams);\r\n          }\r\n          let url = _replaceURLparams('/api' + apiUrl, _params);\r\n          let reg = /<[^>]+>/g; // 违规字符检测正则\r\n          let count = 0; // 参数中含有违规标签个数\r\n          _params && Object.keys(_params).forEach(e => {\r\n            if (reg.test(_params[e])) {\r\n              count += 1; // 如果检测到则数量加1\r\n            }\r\n          });\r\n          // 如果存在不合规的标签则阻止请求,如果noCheck为true\r\n          if (count && !noCheck) {\r\n            // 如果这个接口noCheck为false就代表需要检测，且有参数中存在违规字符，则将接口地址变掉\r\n            url = '/app/ics/check-param-fail';\r\n            method = 'POST';\r\n          }\r\n          return axios(_normoalize(assign({\r\n            url,\r\n            method\r\n          }, outerOptions), _params)).catch(error => { // 全局捕获异常，后续继续then，如果去掉本段代码，则后续不继续then\r\n            switch (error.response ? error.response.status : 500) {\r\n              case 400:\r\n              case 404:\r\n              case 405:\r\n              case 500:\r\n                throw error;\r\n              default:\r\n                break;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nfunction _replaceURLparams(url, data) {\r\n  return url.replace(/:([\\w\\d]+)/ig, (reg, key) => {\r\n    return data[key];\r\n  });\r\n}\r\n\r\nfunction _normoalize(options, data) {\r\n  let token = $cookies.get('userToken');\r\n  // 从 token 中解析出 account\r\n  const tokenData = parseJwt(token);\r\n  const username = tokenData ? tokenData.account : '';\r\n  // 如果没有从token中获取到username，尝试从其他地方获取\r\n  const finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\r\n\r\n  const Vud = encryptVud(finalUsername);\r\n  const method = options.method.toUpperCase();\r\n\r\n  // 基础请求头\r\n  const baseHeaders = {\r\n    'Authorization': 'Bearer ' + token\r\n  };\r\n\r\n  // 添加 Vud 到请求头，即使是微服务跳转的情况\r\n  baseHeaders.Vud = Vud;\r\n\r\n  if (['POST', 'PUT', 'PATCH'].indexOf(method) > -1) {\r\n    options.data = data;\r\n    options.headers = baseHeaders;\r\n  } else if (['POST_FORM'].indexOf(method) > -1) {\r\n    options.data = qs.stringify(data);\r\n    if (options.url !== '/ics/account/login' && options.url !== '/ics/account/update-password-for-expired') {\r\n      options.headers = {\r\n        ...baseHeaders,\r\n        'X-Requested-With': 'XMLHttpRequest',\r\n        'content-type': 'application/x-www-form-urlencoded'\r\n      };\r\n    } else {\r\n      options.headers = {\r\n        'X-Requested-With': 'XMLHttpRequest',\r\n        'content-type': 'application/x-www-form-urlencoded',\r\n        'Vud': Vud\r\n      };\r\n    }\r\n    options.method = 'POST';\r\n  } else if (['POST_TOKEN'].indexOf(method) > -1) {\r\n    options.data = qs.stringify(data);\r\n    options.method = 'post';\r\n    options.headers = {\r\n      'Authorization': `Basic ${process.env.VUE_APP_CLIENT_SECRET}`,\r\n      'content-type': 'application/x-www-form-urlencoded',\r\n      'Vud': Vud\r\n    };\r\n  } else if (['OTHER'].indexOf(method) > -1) {\r\n    options.headers = baseHeaders;\r\n    options.url += `/${Object.values(data).join('/')}`;\r\n    options.method = 'post';\r\n  } else if (['OTHERGET'].indexOf(method) > -1) {\r\n    options.headers = baseHeaders;\r\n    options.url += `/${Object.values(data).join('/')}`;\r\n    options.method = 'get';\r\n  } else if (['DELETE'].indexOf(method) > -1) {\r\n    options.headers = baseHeaders;\r\n    options.url += `/${Object.values(data).join('/')}`;\r\n    options.method = 'post';\r\n  } else {\r\n    options.params = data;\r\n    options.headers = baseHeaders;\r\n  }\r\n  return options;\r\n}\r\n\r\nexport default new MakeApi({\r\n  config: API_CONFIG,\r\n  ...API_DEFAULT_CONFIG\r\n}).api;"], "mappings": ";;;;;;;;;;;;;AAAA,OAAOA,KAAK;AACZ,OAAOC,EAAE,MAAM,IAAI;AACnB,OAAOC,QAAQ,MAAM,WAAW;AAChC,SACEC,kBAAkB,QACb,UAAU;AACjB;AACA,SACEC,UAAU,QACL,wBAAwB;AAC/B,SACEC,MAAM,QACD,SAAS;AAChB,SACEC,MAAM,EACNC,OAAO,QACF,QAAQ;AACf,OAAOC,UAAU,MAAM,iBAAiB;;AAExC;AACA,SAASC,UAAUA,CAACC,YAAY,EAAE;EAChC,IAAI;IACF,OAAOC,IAAI,CAACD,YAAY,CAAC;EAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,SAASE,UAAUA,CAACC,QAAQ,EAAE;EAC5B,IAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACtC,IAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EACpD,IAAMG,GAAG,MAAAC,MAAA,CAAMR,QAAQ,OAAAQ,MAAA,CAAIP,SAAS,EAAAO,MAAA,CAAGJ,MAAM,CAAE;;EAE/C;EACA,IAAMT,YAAY,GAAG,0BAA0B;EAC/C,IAAMc,SAAS,GAAGf,UAAU,CAACC,YAAY,CAAC;EAE1C,IAAI;IACF,IAAMe,GAAG,GAAGvB,QAAQ,CAACwB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC9C,IAAMK,EAAE,GAAG3B,QAAQ,CAACwB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC7C,IAAMM,OAAO,GAAG5B,QAAQ,CAACwB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;IAC5C,IAAMS,SAAS,GAAG7B,QAAQ,CAAC8B,GAAG,CAACC,OAAO,CAACH,OAAO,EAAEL,GAAG,EAAE;MACnDI,EAAE,EAAEA,EAAE;MACNK,IAAI,EAAEhC,QAAQ,CAACgC,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAElC,QAAQ,CAACmC,GAAG,CAACC;IACxB,CAAC,CAAC;IAEF,OAAOpC,QAAQ,CAACwB,GAAG,CAACa,MAAM,CAACC,SAAS,CAACT,SAAS,CAACU,UAAU,CAAC;EAC5D,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,SAAS8B,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACF,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;IACb;IACA,IAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,IAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC9D,IAAMC,WAAW,GAAGC,kBAAkB,CAACtC,IAAI,CAACmC,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACK,GAAG,CAAC,UAAUC,CAAC,EAAE;MAC7E,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;IACZ,OAAOC,IAAI,CAAC5B,KAAK,CAACoB,WAAW,CAAC;EAChC,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,IAAI;EACb;AACF;AAAC,IAEK6C,OAAO;EACX,SAAAA,QAAYC,OAAO,EAAE;IAAAC,eAAA,OAAAF,OAAA;IACnB,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAACC,UAAU,CAACH,OAAO,CAAC;EAC1B;EAAC,OAAAI,YAAA,CAAAL,OAAA;IAAAhC,GAAA;IAAAsC,KAAA,EAED,SAAAF,UAAUA,CAAAG,IAAA,EAIP;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAF,IAAA,CAHDG,GAAG;QAAHA,GAAG,GAAAD,QAAA,cAAG,GAAG,GAAAA,QAAA;QAAAE,UAAA,GAAAJ,IAAA,CACTK,KAAK;QAALA,KAAK,GAAAD,UAAA,cAAG,KAAK,GAAAA,UAAA;QAAAE,WAAA,GAAAN,IAAA,CACbO,MAAM;QAANA,MAAM,GAAAD,WAAA,cAAG,CAAC,CAAC,GAAAA,WAAA;MAEXE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACrB,GAAG,CAAC,UAAAwB,SAAS,EAAI;QACnCT,KAAI,CAACU,iBAAiB,CAAC;UACrBD,SAAS,EAATA,SAAS;UACTP,GAAG,EAAHA,GAAG;UACHE,KAAK,EAALA,KAAK;UACLE,MAAM,EAAEA,MAAM,CAACG,SAAS;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;IAAAjD,GAAA;IAAAsC,KAAA,EAED,SAAAY,iBAAiBA,CAAAC,KAAA,EAKd;MAAA,IAAAC,MAAA;MAAA,IAJDH,SAAS,GAAAE,KAAA,CAATF,SAAS;QACTP,GAAG,GAAAS,KAAA,CAAHT,GAAG;QACHE,KAAK,GAAAO,KAAA,CAALP,KAAK;QACLE,MAAM,GAAAK,KAAA,CAANL,MAAM;MAENA,MAAM,CAACO,OAAO,CAAC,UAAAlB,GAAG,EAAI;QACpB,IACEmB,IAAI,GAKFnB,GAAG,CALLmB,IAAI;UACJC,MAAM,GAIJpB,GAAG,CAJLoB,MAAM;UACNC,MAAM,GAGJrB,GAAG,CAHLqB,MAAM;UACNC,IAAI,GAEFtB,GAAG,CAFLsB,IAAI;UACJC,OAAO,GACLvB,GAAG,CADLuB,OAAO;QAET,IAAMC,OAAO,MAAA7D,MAAA,CAAMmD,SAAS,EAAAnD,MAAA,CAAG4C,GAAG,EAAA5C,MAAA,CAAGwD,IAAI,CAAE;QAC3C,IAAMM,MAAM,GAAGH,IAAI;QAEnBb,KAAK,IAAIhE,MAAM,CAAC0E,IAAI,KAAAxD,MAAA,CAAK8D,MAAM,2DAAgB,CAAC;QAChDhB,KAAK,IAAIhE,MAAM,CAACgF,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAA/D,MAAA,CAAK8D,MAAM,wEAAmB,CAAC;QACxEb,MAAM,CAACe,cAAc,CAACV,MAAI,CAACjB,GAAG,EAAEwB,OAAO,EAAE;UACvCrB,KAAK,WAALA,KAAKA,CAACyB,WAAW,EAAEC,YAAY,EAAE;YAC/B,IAAIC,OAAO;YACX,IAAIC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;cAC9BE,OAAO,GAAGF,WAAW;YACvB,CAAC,MAAM;cACLE,OAAO,GAAGnF,OAAO,CAACiF,WAAW,CAAC,GAAGR,MAAM,GAAG1E,MAAM,CAAC,CAAC,CAAC,EAAE0E,MAAM,EAAEQ,WAAW,CAAC;YAC3E;YACA,IAAIK,GAAG,GAAGC,iBAAiB,CAAC,MAAM,GAAGT,MAAM,EAAEK,OAAO,CAAC;YACrD,IAAIK,GAAG,GAAG,UAAU,CAAC,CAAC;YACtB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;YACfN,OAAO,IAAIlB,MAAM,CAACC,IAAI,CAACiB,OAAO,CAAC,CAACZ,OAAO,CAAC,UAAAmB,CAAC,EAAI;cAC3C,IAAIF,GAAG,CAACG,IAAI,CAACR,OAAO,CAACO,CAAC,CAAC,CAAC,EAAE;gBACxBD,KAAK,IAAI,CAAC,CAAC,CAAC;cACd;YACF,CAAC,CAAC;YACF;YACA,IAAIA,KAAK,IAAI,CAACb,OAAO,EAAE;cACrB;cACAU,GAAG,GAAG,2BAA2B;cACjCZ,MAAM,GAAG,MAAM;YACjB;YACA,OAAOjF,KAAK,CAACmG,WAAW,CAAC7F,MAAM,CAAC;cAC9BuF,GAAG,EAAHA,GAAG;cACHZ,MAAM,EAANA;YACF,CAAC,EAAEQ,YAAY,CAAC,EAAEC,OAAO,CAAC,CAAC,CAACU,KAAK,CAAC,UAAAxF,KAAK,EAAI;cAAE;cAC3C,QAAQA,KAAK,CAACyF,QAAQ,GAAGzF,KAAK,CAACyF,QAAQ,CAACC,MAAM,GAAG,GAAG;gBAClD,KAAK,GAAG;gBACR,KAAK,GAAG;gBACR,KAAK,GAAG;gBACR,KAAK,GAAG;kBACN,MAAM1F,KAAK;gBACb;kBACE;cACJ;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;AAAA;AAGH,SAASkF,iBAAiBA,CAACD,GAAG,EAAEU,IAAI,EAAE;EACpC,OAAOV,GAAG,CAAC9C,OAAO,CAAC,cAAc,EAAE,UAACgD,GAAG,EAAEtE,GAAG,EAAK;IAC/C,OAAO8E,IAAI,CAAC9E,GAAG,CAAC;EAClB,CAAC,CAAC;AACJ;AAEA,SAAS0E,WAAWA,CAACzC,OAAO,EAAE6C,IAAI,EAAE;EAClC,IAAI5D,KAAK,GAAG6D,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;EACrC;EACA,IAAMC,SAAS,GAAGhE,QAAQ,CAACC,KAAK,CAAC;EACjC,IAAM5B,QAAQ,GAAG2F,SAAS,GAAGA,SAAS,CAACC,OAAO,GAAG,EAAE;EACnD;EACA,IAAMC,aAAa,GAAG7F,QAAQ,IAAIyF,QAAQ,CAACC,GAAG,CAAC,aAAa,CAAC,IAAIjG,UAAU,CAACqG,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;EAEzG,IAAMC,GAAG,GAAGhG,UAAU,CAAC8F,aAAa,CAAC;EACrC,IAAM3B,MAAM,GAAGvB,OAAO,CAACuB,MAAM,CAAC8B,WAAW,CAAC,CAAC;;EAE3C;EACA,IAAMC,WAAW,GAAG;IAClB,eAAe,EAAE,SAAS,GAAGrE;EAC/B,CAAC;;EAED;EACAqE,WAAW,CAACF,GAAG,GAAGA,GAAG;EAErB,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAACxB,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACjDvB,OAAO,CAAC6C,IAAI,GAAGA,IAAI;IACnB7C,OAAO,CAACuD,OAAO,GAAGD,WAAW;EAC/B,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC1B,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7CvB,OAAO,CAAC6C,IAAI,GAAGtG,EAAE,CAACuC,SAAS,CAAC+D,IAAI,CAAC;IACjC,IAAI7C,OAAO,CAACmC,GAAG,KAAK,oBAAoB,IAAInC,OAAO,CAACmC,GAAG,KAAK,0CAA0C,EAAE;MACtGnC,OAAO,CAACuD,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACVF,WAAW;QACd,kBAAkB,EAAE,gBAAgB;QACpC,cAAc,EAAE;MAAmC,EACpD;IACH,CAAC,MAAM;MACLtD,OAAO,CAACuD,OAAO,GAAG;QAChB,kBAAkB,EAAE,gBAAgB;QACpC,cAAc,EAAE,mCAAmC;QACnD,KAAK,EAAEH;MACT,CAAC;IACH;IACApD,OAAO,CAACuB,MAAM,GAAG,MAAM;EACzB,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,CAACK,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9CvB,OAAO,CAAC6C,IAAI,GAAGtG,EAAE,CAACuC,SAAS,CAAC+D,IAAI,CAAC;IACjC7C,OAAO,CAACuB,MAAM,GAAG,MAAM;IACvBvB,OAAO,CAACuD,OAAO,GAAG;MAChB,eAAe,WAAA1F,MAAA,CAAW4F,OAAO,CAACC,GAAG,CAACC,qBAAqB,CAAE;MAC7D,cAAc,EAAE,mCAAmC;MACnD,KAAK,EAAEP;IACT,CAAC;EACH,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,CAACxB,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACzCvB,OAAO,CAACuD,OAAO,GAAGD,WAAW;IAC7BtD,OAAO,CAACmC,GAAG,QAAAtE,MAAA,CAAQiD,MAAM,CAAC8C,MAAM,CAACf,IAAI,CAAC,CAAChD,IAAI,CAAC,GAAG,CAAC,CAAE;IAClDG,OAAO,CAACuB,MAAM,GAAG,MAAM;EACzB,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,CAACK,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5CvB,OAAO,CAACuD,OAAO,GAAGD,WAAW;IAC7BtD,OAAO,CAACmC,GAAG,QAAAtE,MAAA,CAAQiD,MAAM,CAAC8C,MAAM,CAACf,IAAI,CAAC,CAAChD,IAAI,CAAC,GAAG,CAAC,CAAE;IAClDG,OAAO,CAACuB,MAAM,GAAG,KAAK;EACxB,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,CAACK,OAAO,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1CvB,OAAO,CAACuD,OAAO,GAAGD,WAAW;IAC7BtD,OAAO,CAACmC,GAAG,QAAAtE,MAAA,CAAQiD,MAAM,CAAC8C,MAAM,CAACf,IAAI,CAAC,CAAChD,IAAI,CAAC,GAAG,CAAC,CAAE;IAClDG,OAAO,CAACuB,MAAM,GAAG,MAAM;EACzB,CAAC,MAAM;IACLvB,OAAO,CAACsB,MAAM,GAAGuB,IAAI;IACrB7C,OAAO,CAACuD,OAAO,GAAGD,WAAW;EAC/B;EACA,OAAOtD,OAAO;AAChB;AAEA,eAAe,IAAID,OAAO,CAAAyD,aAAA;EACxB3C,MAAM,EAAEnE;AAAU,GACfD,kBAAkB,CACtB,CAAC,CAACyD,GAAG", "ignoreList": []}]}
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-input
          style="width: 200px; margin: 0 10px 0 0"
          v-model.trim="searchForm.tableNameLike"
          size="small"
          placeholder="请输入表名"
        ></el-input>
        <el-button
          size="small"
          type="primary"
          style="margin: 0 0 0 10px"
          @click="handleSearch"
          >搜索</el-button
        >
      </el-col>
      <el-col :span="24">
        <el-button
          v-permission="'codeGeneration_generate'"
          size="small"
          type="primary"
          style="margin: 20px 0 0 0;"
          @click="handleGenerate"
          >生成</el-button
        >
        <el-button
          v-permission="'codeGeneration_download'"
          size="small"
          type="primary"
          style="margin: 20px 0 0 10px"
          @click="handleDownload"
          >打包下载</el-button
        >
      </el-col>
    </el-row>
    <el-table slot="table" :data="tableData" @selection-change="selectionChange" stripe style="width: 100%">
      <el-table-column
        fixed="left"
        :align="'center'"
        type="selection"
        width="55"
      >
      </el-table-column>
      <el-table-column
        fixed="left"
        :align="'center'"
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <template v-for="(item, index) in columns">
        <el-table-column
          v-if="item.slot"
          :show-overflow-tooltip="true"
          :align="item.align ? item.align : 'center'"
          :key="index"
          :prop="item.key"
          :label="item.title"
          min-width="180"
        >
          <template slot-scope="scope">
            {{ scope.row[item.slot] ? "启用" : "禁用" }}
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :show-overflow-tooltip="true"
          :key="item.key"
          :prop="item.key"
          :label="item.title"
          :min-width="item.width ? item.width : '150'"
          :align="item.align ? item.align : 'center'"
        >
        </el-table-column>
      </template>
    </el-table>
    <el-dialog
      :visible.sync="generateDialog"
      :title="'生成代码'"
      width="500"
    >
      <el-form
        :model="form"
        :rules="formValidate"
        ref="form"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="模块代码" prop="moduleCode">
          <el-input size="small" v-model.trim="form.moduleCode"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="ok" :loading="okLoading">确定</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {mapActions} from 'vuex';
import downLoadNotice from '@/mixins/downLoadNotice.vue';
export default {
  components: {},
  destroyed() {},
  mixins: [downLoadNotice],
  data() {
    return {
      generateDialog: false,
      tableSelection: [],
      okLoading: false,
      formValidate: {
        moduleCode: [
          {
            required: true,
            message: '模块代码不能为空！',
            trigger: 'change,blur',
          },
        ],
      },
      form: {
        tableList: [],
        moduleCode: '',
      },
      searchForm: {
        tableNameLike: '',
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '表名',
          key: 'tableName',
          align: 'center',
          minWidth: 180,
          tooltip: true,
        },
        {
          title: '注释',
          key: 'tableComment',
          align: 'center',
          minWidth: 160,
          tooltip: true,
        },
      ],
      tableData: [],
    };
  },
  watch: {},
  created() {},
  methods: {
    ...mapActions(['download']),
    // 执行查询
    handleSearch() {
      this.$api['codeGeneration/table-list-v2']({
        tableNameLike: this.searchForm.tableNameLike,
      }).then(data => {
        this.tableData = data;
      });
    },
    ok() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          console.log(this.form);
          this.$api['codeGeneration/generate-code'](this.form)
            .then(data => {
              this.$message.success('生成成功');
              this.generateDialog = false;
            })
            .finally(() => {
              this.okLoading = false;
            });
        }
      });
    },
    cancel() {
      // 取消关闭
      this.generateDialog = false;
    },
    // 生成
    handleGenerate() {
      if (this.tableSelection.length === 0) {
        this.$message.info('请选择需要生成代码的库表');
        return 0;
      }
      let tableNameList = [];
      this.tableSelection.forEach(e => {
        tableNameList.push(e.tableName);
      });
      this.form = {
        tableList: tableNameList,
        moduleCode: '',
      };
      this.generateDialog = true;
    },
    handleDownload() {
      let url = '/api/generator/code/generator/engine/pack-download';
      let downData = {
        url: url,
        downLoad: '生成代码-' + new Date().getTime() + '.zip'
      };
      this.download(downData); // 下载
    },
    selectionChange(val) {
      this.tableSelection = val;
    },
  },
};
</script>

<style scoped lang='less'>
</style>

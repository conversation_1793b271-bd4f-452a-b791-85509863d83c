{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\interceptors\\router.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\interceptors\\router.js", "mtime": 1745205562782}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.match\";\nimport \"core-js/modules/es6.regexp.constructor\";\nimport \"core-js/modules/es6.function.name\";\nimport store from '@/plugins/store';\nimport router from '@/plugins/router';\nimport global from '@/global/global';\nimport NProgress from 'nprogress';\nimport localCache from '@/utils/storage';\nexport function routerBeforeEachFunc(to, from, next) {\n  routerBeforeEachFuncNormal(to, from, next);\n}\nexport function routerBeforeEachFuncNormal(to, from, next) {\n  // 页面拦截、权限处理\n  // iView.LoadingBar.start();\n  NProgress.start();\n  // 拦截每一个方法、页面/例外登录页面\n  var permissions = store.state.common.permissions;\n  var issome = permissions.some(function (e) {\n    return e.resourcePath == to.path;\n  });\n  // if (!issome && permissions.length && to.name != 'home') {\n  //   Message.error('您暂无该页面权限');\n  //   return;\n  // }\n  var username = localCache.getLocal('nowUserName') || ''; // 用户登录信息\n  var loginPage = global.LOGIN_PAGE_NAME;\n  if (!username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound') {\n    // 1.登录信息失效(且非登录页面)，统一跳转用户登录界面\n    var reg = RegExp(/social/);\n    var reg1 = RegExp(/callback/);\n    console.log(reg.test(to.path) && reg1.test(to.path));\n    if (reg.test(to.path) && reg1.test(to.path)) {\n      var stype = to.path.match(/social(\\S*)callback/)[1];\n      stype.replace(/\\//g, '');\n      var _ref = {},\n        name = _ref.name,\n        params = _ref.params,\n        query = _ref.query;\n      name = 'social';\n      query = to.query, query.stype = stype;\n      router.push({\n        name: name,\n        params: params,\n        query: query\n      });\n    } else {\n      var _ref2 = {},\n        _name = _ref2.name,\n        _params = _ref2.params,\n        _query = _ref2.query;\n      _name = loginPage;\n      router.push({\n        name: _name,\n        params: _params,\n        query: _query\n      });\n    }\n  } else if (username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound' && !store.getters.menuList || to.name === null) {\n    // 2.登录信息存在，如果是登录状态,并且进入非登录页面\n    // 先获取登录信息，后跳转业务界面\n    if (to.path) {\n      store.dispatch('login').then(function (data) {\n        router.push({\n          path: to.path\n        });\n      });\n    } else {\n      store.dispatch('login').then(function (data) {\n        router.push({\n          path: '/home/<USER>'\n        });\n      });\n    }\n  } else {\n    next();\n  }\n}\nexport function routerAfterEachFunc(to) {\n  NProgress.done();\n  // iView.LoadingBar.finish();\n}", {"version": 3, "names": ["store", "router", "global", "NProgress", "localCache", "routerBeforeEachFunc", "to", "from", "next", "routerBeforeEachFuncNormal", "start", "permissions", "state", "common", "issome", "some", "e", "resourcePath", "path", "username", "getLocal", "loginPage", "LOGIN_PAGE_NAME", "name", "reg", "RegExp", "reg1", "console", "log", "test", "stype", "match", "replace", "_ref", "params", "query", "push", "_ref2", "getters", "menuList", "dispatch", "then", "data", "routerAfterEachFunc", "done"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/config/interceptors/router.js"], "sourcesContent": ["import store from '@/plugins/store';\r\nimport router from '@/plugins/router';\r\nimport global from '@/global/global';\r\nimport {Message} from 'element-ui';\r\nimport NProgress from 'nprogress';\r\nimport localCache from '@/utils/storage';\r\nexport function routerBeforeEachFunc(to, from, next) {\r\n  routerBeforeEachFuncNormal(to, from, next);\r\n}\r\nexport function routerBeforeEachFuncNormal(to, from, next) {\r\n  // 页面拦截、权限处理\r\n  // iView.LoadingBar.start();\r\n  NProgress.start();\r\n  // 拦截每一个方法、页面/例外登录页面\r\n  let permissions = store.state.common.permissions;\r\n  let issome = permissions.some(e => {\r\n    return e.resourcePath == to.path;\r\n  });\r\n  // if (!issome && permissions.length && to.name != 'home') {\r\n  //   Message.error('您暂无该页面权限');\r\n  //   return;\r\n  // }\r\n  let username = localCache.getLocal('nowUserName') || ''; // 用户登录信息\r\n  let loginPage = global.LOGIN_PAGE_NAME;\r\n  if (!username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound') { // 1.登录信息失效(且非登录页面)，统一跳转用户登录界面\r\n    var reg = RegExp(/social/);\r\n    var reg1 = RegExp(/callback/);\r\n    console.log(reg.test(to.path) && reg1.test(to.path));\r\n    if (reg.test(to.path) && reg1.test(to.path)) {\r\n      var stype = to.path.match(/social(\\S*)callback/)[1];\r\n      stype.replace(/\\//g, '');\r\n      let {\r\n        name,\r\n        params,\r\n        query\r\n      } = {};\r\n      name = 'social';\r\n      query = to.query,\r\n      query.stype = stype;\r\n      router.push({\r\n        name,\r\n        params,\r\n        query\r\n      });\r\n    } else {\r\n      let {\r\n        name,\r\n        params,\r\n        query\r\n      } = {};\r\n      name = loginPage;\r\n      router.push({\r\n        name,\r\n        params,\r\n        query\r\n      });\r\n    }\r\n  } else if ((username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound' && !store.getters.menuList) || to.name === null) { // 2.登录信息存在，如果是登录状态,并且进入非登录页面\r\n    // 先获取登录信息，后跳转业务界面\r\n    if (to.path) {\r\n      store.dispatch('login').then(data => {\r\n        router.push({\r\n          path: to.path\r\n        });\r\n      });\r\n    } else {\r\n      store.dispatch('login').then(data => {\r\n        router.push({\r\n          path: '/home/<USER>'\r\n        });\r\n      });\r\n    }\r\n\r\n  } else {\r\n    next();\r\n  }\r\n}\r\n\r\nexport function routerAfterEachFunc(to) {\r\n  NProgress.done();\r\n  // iView.LoadingBar.finish();\r\n}\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,iBAAiB;AAEpC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAO,SAASC,oBAAoBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACnDC,0BAA0B,CAACH,EAAE,EAAEC,IAAI,EAAEC,IAAI,CAAC;AAC5C;AACA,OAAO,SAASC,0BAA0BA,CAACH,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACzD;EACA;EACAL,SAAS,CAACO,KAAK,CAAC,CAAC;EACjB;EACA,IAAIC,WAAW,GAAGX,KAAK,CAACY,KAAK,CAACC,MAAM,CAACF,WAAW;EAChD,IAAIG,MAAM,GAAGH,WAAW,CAACI,IAAI,CAAC,UAAAC,CAAC,EAAI;IACjC,OAAOA,CAAC,CAACC,YAAY,IAAIX,EAAE,CAACY,IAAI;EAClC,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA,IAAIC,QAAQ,GAAGf,UAAU,CAACgB,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;EACzD,IAAIC,SAAS,GAAGnB,MAAM,CAACoB,eAAe;EACtC,IAAI,CAACH,QAAQ,IAAIb,EAAE,CAACiB,IAAI,KAAKF,SAAS,IAAIf,EAAE,CAACiB,IAAI,KAAK,QAAQ,IAAIjB,EAAE,CAACiB,IAAI,KAAK,WAAW,IAAIjB,EAAE,CAACiB,IAAI,KAAK,SAAS,EAAE;IAAE;IACpH,IAAIC,GAAG,GAAGC,MAAM,CAAC,QAAQ,CAAC;IAC1B,IAAIC,IAAI,GAAGD,MAAM,CAAC,UAAU,CAAC;IAC7BE,OAAO,CAACC,GAAG,CAACJ,GAAG,CAACK,IAAI,CAACvB,EAAE,CAACY,IAAI,CAAC,IAAIQ,IAAI,CAACG,IAAI,CAACvB,EAAE,CAACY,IAAI,CAAC,CAAC;IACpD,IAAIM,GAAG,CAACK,IAAI,CAACvB,EAAE,CAACY,IAAI,CAAC,IAAIQ,IAAI,CAACG,IAAI,CAACvB,EAAE,CAACY,IAAI,CAAC,EAAE;MAC3C,IAAIY,KAAK,GAAGxB,EAAE,CAACY,IAAI,CAACa,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACnDD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxB,IAAAC,IAAA,GAII,CAAC,CAAC;QAHJV,IAAI,GAAAU,IAAA,CAAJV,IAAI;QACJW,MAAM,GAAAD,IAAA,CAANC,MAAM;QACNC,KAAK,GAAAF,IAAA,CAALE,KAAK;MAEPZ,IAAI,GAAG,QAAQ;MACfY,KAAK,GAAG7B,EAAE,CAAC6B,KAAK,EAChBA,KAAK,CAACL,KAAK,GAAGA,KAAK;MACnB7B,MAAM,CAACmC,IAAI,CAAC;QACVb,IAAI,EAAJA,IAAI;QACJW,MAAM,EAANA,MAAM;QACNC,KAAK,EAALA;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAAE,KAAA,GAII,CAAC,CAAC;QAHJd,KAAI,GAAAc,KAAA,CAAJd,IAAI;QACJW,OAAM,GAAAG,KAAA,CAANH,MAAM;QACNC,MAAK,GAAAE,KAAA,CAALF,KAAK;MAEPZ,KAAI,GAAGF,SAAS;MAChBpB,MAAM,CAACmC,IAAI,CAAC;QACVb,IAAI,EAAJA,KAAI;QACJW,MAAM,EAANA,OAAM;QACNC,KAAK,EAALA;MACF,CAAC,CAAC;IACJ;EACF,CAAC,MAAM,IAAKhB,QAAQ,IAAIb,EAAE,CAACiB,IAAI,KAAKF,SAAS,IAAIf,EAAE,CAACiB,IAAI,KAAK,QAAQ,IAAIjB,EAAE,CAACiB,IAAI,KAAK,WAAW,IAAIjB,EAAE,CAACiB,IAAI,KAAK,SAAS,IAAI,CAACvB,KAAK,CAACsC,OAAO,CAACC,QAAQ,IAAKjC,EAAE,CAACiB,IAAI,KAAK,IAAI,EAAE;IAAE;IAC3K;IACA,IAAIjB,EAAE,CAACY,IAAI,EAAE;MACXlB,KAAK,CAACwC,QAAQ,CAAC,OAAO,CAAC,CAACC,IAAI,CAAC,UAAAC,IAAI,EAAI;QACnCzC,MAAM,CAACmC,IAAI,CAAC;UACVlB,IAAI,EAAEZ,EAAE,CAACY;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlB,KAAK,CAACwC,QAAQ,CAAC,OAAO,CAAC,CAACC,IAAI,CAAC,UAAAC,IAAI,EAAI;QACnCzC,MAAM,CAACmC,IAAI,CAAC;UACVlB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAEF,CAAC,MAAM;IACLV,IAAI,CAAC,CAAC;EACR;AACF;AAEA,OAAO,SAASmC,mBAAmBA,CAACrC,EAAE,EAAE;EACtCH,SAAS,CAACyC,IAAI,CAAC,CAAC;EAChB;AACF", "ignoreList": []}]}
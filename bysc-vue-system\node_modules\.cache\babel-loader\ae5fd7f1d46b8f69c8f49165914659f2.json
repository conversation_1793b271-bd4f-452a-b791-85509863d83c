{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\main.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\main.js", "mtime": 1745205562791}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.regexp.constructor\";\nimport _ElementUI2 from \"element-ui/lib/theme-chalk/index.css\";\nimport _ElementUI from \"element-ui/lib\";\nimport \"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\core-js\\\\modules\\\\es6.array.iterator.js\";\nimport \"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\core-js\\\\modules\\\\es6.promise.js\";\nimport \"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\core-js\\\\modules\\\\es6.object.assign.js\";\nimport \"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\core-js\\\\modules\\\\es7.promise.finally.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Vue from 'vue';\nimport App from \"./App.vue\";\nimport router from '@/plugins/router';\nimport store from '@/plugins/store';\nimport inject from '@/plugins/inject'; // 注意顺序，可能编译错乱，导致线上发布异常\nimport { AXIOS_DEFAULT_CONFIG } from '@/config';\nimport echarts from 'echarts';\nimport axiosOrg from 'axios';\nimport globalMy from \"../src/global/global.js\";\nimport \"./static/theme/theme.less\";\nimport VueCookies from 'vue-cookies';\nVue.use(VueCookies);\nimport \"./plugins/element-ui\"; // 引入 Element UI 配置\nVue.prototype.$echarts = echarts;\nVue.prototype.$global = globalMy;\nvar axiosDefault = axiosOrg.create(AXIOS_DEFAULT_CONFIG);\nVue.prototype.$axios = axiosDefault;\nimport localCache from \"../src/utils/storage\";\nVue.prototype.$localCache = localCache;\nVue.config.productionTip = false;\nglobal.vbus = new Vue();\nVue.config.productionTip = false;\nimport Directives from \"./directives\";\nVue.use(Directives);\nVue.use(inject);\nVue.use(echarts);\nimport 'element-ui/lib/theme-chalk/index.css';\nVue.use(_ElementUI);\nimport NProgress from 'nprogress';\nimport 'nprogress/nprogress.css';\nNProgress.configure({\n  easing: 'ease',\n  // 动画方式\n  speed: 500,\n  // 递增进度条的速度\n  showSpinner: false,\n  // 是否显示加载ico\n  trickleSpeed: 200,\n  // 自动递增间隔\n  minimum: 0.3 // 初始化时的最小百分比\n});\nVue.use(NProgress);\nimport \"./index.less\"; // 设置主题文件\nimport '@/assets/scrollbar.css';\nVue.prototype.$href = location.origin;\nVue.prototype.checkParams = function (param) {\n  var ruleIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var msg = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '您输入的内容含有特殊字符';\n  var rules = [\"^[a-z0-9A-Z-_\\\\u4e00-\\\\u9fa5]+$\"];\n  var flag = new RegExp(rules[ruleIndex]);\n  if (Object.prototype.toString.call(param) === '[object String]') {\n    console.log('str', flag.test(param));\n    var isFlags = flag.test(param);\n    if (!isFlags) {\n      msg.length && iView.Message.error(msg);\n    }\n    return isFlags;\n  }\n  console.warn('检测参数请传入字符串');\n};\nwindow.VueInstance = new Vue(_objectSpread({\n  el: '#app',\n  router: router,\n  store: store\n}, App));", {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "inject", "AXIOS_DEFAULT_CONFIG", "echarts", "axiosOrg", "globalMy", "VueCookies", "use", "prototype", "$echarts", "$global", "a<PERSON><PERSON><PERSON><PERSON>ault", "create", "$axios", "localCache", "$localCache", "config", "productionTip", "global", "vbus", "Directives", "_ElementUI", "NProgress", "configure", "easing", "speed", "showSpinner", "trickleSpeed", "minimum", "$href", "location", "origin", "checkParams", "param", "ruleIndex", "arguments", "length", "undefined", "msg", "rules", "flag", "RegExp", "Object", "toString", "call", "console", "log", "test", "isFlags", "iView", "Message", "error", "warn", "window", "VueInstance", "_objectSpread", "el"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/main.js"], "sourcesContent": ["import Vue from 'vue';\r\nimport App from './App.vue';\r\nimport router from '@/plugins/router';\r\nimport store from '@/plugins/store';\r\nimport inject from '@/plugins/inject'; // 注意顺序，可能编译错乱，导致线上发布异常\r\nimport {AXIOS_DEFAULT_CONFIG} from '@/config';\r\nimport echarts from 'echarts';\r\nimport axiosOrg from 'axios';\r\nimport globalMy from '../src/global/global.js';\r\nimport './static/theme/theme.less';\r\nimport VueCookies from 'vue-cookies';\r\nVue.use(VueCookies);\r\nimport './plugins/element-ui'; // 引入 Element UI 配置\r\nVue.prototype.$echarts = echarts;\r\nVue.prototype.$global = globalMy;\r\nlet axiosDefault = axiosOrg.create(AXIOS_DEFAULT_CONFIG);\r\nVue.prototype.$axios = axiosDefault;\r\nimport localCache from '../src/utils/storage';\r\nVue.prototype.$localCache = localCache;\r\nVue.config.productionTip = false;\r\nglobal.vbus = new Vue();\r\nVue.config.productionTip = false;\r\nimport Directives from './directives';\r\nVue.use(Directives);\r\nVue.use(inject);\r\nVue.use(echarts);\r\nimport ElementUI from 'element-ui';\r\nimport 'element-ui/lib/theme-chalk/index.css';\r\nVue.use(ElementUI);\r\nimport NProgress from 'nprogress';\r\nimport 'nprogress/nprogress.css';\r\nNProgress.configure({\r\n  easing: 'ease', // 动画方式\r\n  speed: 500, // 递增进度条的速度\r\n  showSpinner: false, // 是否显示加载ico\r\n  trickleSpeed: 200, // 自动递增间隔\r\n  minimum: 0.3 // 初始化时的最小百分比\r\n});\r\nVue.use(NProgress);\r\nimport './index.less'; // 设置主题文件\r\nimport '@/assets/scrollbar.css';\r\nVue.prototype.$href = location.origin;\r\nVue.prototype.checkParams = function (param, ruleIndex = 0, msg = '您输入的内容含有特殊字符') {\r\n  let rules = ['^[a-z0-9A-Z-_\\\\u4e00-\\\\u9fa5]+$'];\r\n  var flag = new RegExp(rules[ruleIndex]);\r\n  if (Object.prototype.toString.call(param) === '[object String]') {\r\n    console.log('str', flag.test(param));\r\n    let isFlags = flag.test(param);\r\n    if (!isFlags) {\r\n      msg.length && iView.Message.error(msg);\r\n    }\r\n    return isFlags;\r\n  }\r\n  console.warn('检测参数请传入字符串');\r\n};\r\nwindow.VueInstance = new Vue({\r\n  el: '#app',\r\n  router,\r\n  store,\r\n  ...App\r\n});\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG;AACV,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,MAAM,MAAM,kBAAkB,CAAC,CAAC;AACvC,SAAQC,oBAAoB,QAAO,UAAU;AAC7C,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,QAAQ,MAAM,OAAO;AAC5B,OAAOC,QAAQ;AACf;AACA,OAAOC,UAAU,MAAM,aAAa;AACpCT,GAAG,CAACU,GAAG,CAACD,UAAU,CAAC;AACnB,8BAA8B,CAAC;AAC/BT,GAAG,CAACW,SAAS,CAACC,QAAQ,GAAGN,OAAO;AAChCN,GAAG,CAACW,SAAS,CAACE,OAAO,GAAGL,QAAQ;AAChC,IAAIM,YAAY,GAAGP,QAAQ,CAACQ,MAAM,CAACV,oBAAoB,CAAC;AACxDL,GAAG,CAACW,SAAS,CAACK,MAAM,GAAGF,YAAY;AACnC,OAAOG,UAAU;AACjBjB,GAAG,CAACW,SAAS,CAACO,WAAW,GAAGD,UAAU;AACtCjB,GAAG,CAACmB,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,MAAM,CAACC,IAAI,GAAG,IAAItB,GAAG,CAAC,CAAC;AACvBA,GAAG,CAACmB,MAAM,CAACC,aAAa,GAAG,KAAK;AAChC,OAAOG,UAAU;AACjBvB,GAAG,CAACU,GAAG,CAACa,UAAU,CAAC;AACnBvB,GAAG,CAACU,GAAG,CAACN,MAAM,CAAC;AACfJ,GAAG,CAACU,GAAG,CAACJ,OAAO,CAAC;AAEhB,OAAO,sCAAsC;AAC7CN,GAAG,CAACU,GAAG,CAAAc,UAAU,CAAC;AAClB,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,yBAAyB;AAChCA,SAAS,CAACC,SAAS,CAAC;EAClBC,MAAM,EAAE,MAAM;EAAE;EAChBC,KAAK,EAAE,GAAG;EAAE;EACZC,WAAW,EAAE,KAAK;EAAE;EACpBC,YAAY,EAAE,GAAG;EAAE;EACnBC,OAAO,EAAE,GAAG,CAAC;AACf,CAAC,CAAC;AACF/B,GAAG,CAACU,GAAG,CAACe,SAAS,CAAC;AAClB,sBAAsB,CAAC;AACvB,OAAO,wBAAwB;AAC/BzB,GAAG,CAACW,SAAS,CAACqB,KAAK,GAAGC,QAAQ,CAACC,MAAM;AACrClC,GAAG,CAACW,SAAS,CAACwB,WAAW,GAAG,UAAUC,KAAK,EAAuC;EAAA,IAArCC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,GAAG,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAAc;EAC9E,IAAII,KAAK,GAAG,CAAC,iCAAiC,CAAC;EAC/C,IAAIC,IAAI,GAAG,IAAIC,MAAM,CAACF,KAAK,CAACL,SAAS,CAAC,CAAC;EACvC,IAAIQ,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACC,IAAI,CAACX,KAAK,CAAC,KAAK,iBAAiB,EAAE;IAC/DY,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEN,IAAI,CAACO,IAAI,CAACd,KAAK,CAAC,CAAC;IACpC,IAAIe,OAAO,GAAGR,IAAI,CAACO,IAAI,CAACd,KAAK,CAAC;IAC9B,IAAI,CAACe,OAAO,EAAE;MACZV,GAAG,CAACF,MAAM,IAAIa,KAAK,CAACC,OAAO,CAACC,KAAK,CAACb,GAAG,CAAC;IACxC;IACA,OAAOU,OAAO;EAChB;EACAH,OAAO,CAACO,IAAI,CAAC,YAAY,CAAC;AAC5B,CAAC;AACDC,MAAM,CAACC,WAAW,GAAG,IAAIzD,GAAG,CAAA0D,aAAA;EAC1BC,EAAE,EAAE,MAAM;EACVzD,MAAM,EAANA,MAAM;EACNC,KAAK,EAALA;AAAK,GACFF,GAAG,CACP,CAAC", "ignoreList": []}]}
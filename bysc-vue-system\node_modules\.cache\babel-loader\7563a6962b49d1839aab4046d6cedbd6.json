{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\storage.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\storage.js", "mtime": 1745205562825}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import _typeof from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/typeof.js\";\n/** 本地存储 */\n// 注：每个子项目必须要设置不一样的前缀\nvar prefix = 'bw_';\nexport default {\n  session: window.sessionStorage,\n  local: window.localStorage,\n  set: function set(type, key, value) {\n    if (this.isString(value)) {\n      return this[type].setItem(key, value);\n    }\n    if (this.isObject(value)) {\n      try {\n        value = JSON.stringify(value);\n      } catch (error) {\n        console.log('err');\n      }\n      return this[type].setItem(key, value);\n    }\n    return this[type].setItem(key, value);\n  },\n  get: function get(type, key) {\n    var value = this[type].getItem(key);\n    if (this.isParse(value)) {\n      try {\n        value = JSON.parse(value);\n      } catch (error) {\n        value = this[type].getItem(key);\n      }\n    }\n    return value;\n  },\n  setSession: function setSession(key, value) {\n    this.set('session', prefix + key, value);\n  },\n  getSession: function getSession(key) {\n    return this.get('session', prefix + key);\n  },\n  setLocal: function setLocal(key, value) {\n    if (key == 'userToken' || key == 'refreshToken') {\n      $cookies.set(key, value);\n    }\n    this.set('local', prefix + key, value);\n  },\n  getLocal: function getLocal(key) {\n    return this.get('local', prefix + key);\n  },\n  isString: function isString(value) {\n    return typeof value === 'string';\n  },\n  isObject: function isObject(value) {\n    return _typeof(value) === 'object';\n  },\n  removeLocal: function removeLocal(key) {\n    $cookies.remove(key);\n    this.local.removeItem(key);\n  },\n  removeSession: function removeSession(key) {\n    this.session.removeItem(key);\n  },\n  clearLocal: function clearLocal() {\n    this.local.clear();\n  },\n  clearSession: function clearSession() {\n    this.session.clear();\n  },\n  isParse: function isParse(value) {\n    if (!value) {\n      return false;\n    }\n    return !!(value.indexOf('{') !== -1 || value.indexOf('[') !== -1 || value.indexOf('('));\n  }\n};", {"version": 3, "names": ["prefix", "session", "window", "sessionStorage", "local", "localStorage", "set", "type", "key", "value", "isString", "setItem", "isObject", "JSON", "stringify", "error", "console", "log", "get", "getItem", "isParse", "parse", "setSession", "getSession", "setLocal", "$cookies", "getLocal", "_typeof", "removeLocal", "remove", "removeItem", "removeSession", "clearLocal", "clear", "clearSession", "indexOf"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/utils/storage.js"], "sourcesContent": ["/** 本地存储 */\r\n// 注：每个子项目必须要设置不一样的前缀\r\nconst prefix = 'bw_';\r\nexport default {\r\n  session: window.sessionStorage,\r\n  local: window.localStorage,\r\n  set(type, key, value) {\r\n    if (this.isString(value)) {\r\n      return this[type].setItem(key, value);\r\n    }\r\n    if (this.isObject(value)) {\r\n      try {\r\n        value = JSON.stringify(value);\r\n      } catch (error) {\r\n        console.log('err');\r\n      }\r\n      return this[type].setItem(key, value);\r\n    }\r\n    return this[type].setItem(key, value);\r\n  },\r\n  get(type, key) {\r\n    let value = this[type].getItem(key);\r\n    if (this.isParse(value)) {\r\n      try {\r\n        value = JSON.parse(value);\r\n      } catch (error) {\r\n        value = this[type].getItem(key);\r\n      }\r\n    }\r\n    return value;\r\n  },\r\n  setSession(key, value) {\r\n    this.set('session', prefix + key, value);\r\n  },\r\n  getSession(key) {\r\n    return this.get('session', prefix + key);\r\n  },\r\n  setLocal(key, value) {\r\n    if (key == 'userToken' || key == 'refreshToken') {\r\n      $cookies.set(key, value);\r\n    }\r\n    this.set('local', prefix + key, value);\r\n  },\r\n  getLocal(key) {\r\n    return this.get('local', prefix + key);\r\n  },\r\n  isString(value) {\r\n    return typeof value === 'string';\r\n  },\r\n  isObject(value) {\r\n    return typeof value === 'object';\r\n  },\r\n  removeLocal(key) {\r\n    $cookies.remove(key);\r\n    this.local.removeItem(key);\r\n  },\r\n  removeSession(key) {\r\n    this.session.removeItem(key);\r\n  },\r\n  clearLocal() {\r\n    this.local.clear();\r\n  },\r\n  clearSession() {\r\n    this.session.clear();\r\n  },\r\n  isParse(value) {\r\n    if (!value) {\r\n      return false;\r\n    }\r\n    return !!(value.indexOf('{') !== -1\r\n      || value.indexOf('[') !== -1\r\n      || value.indexOf('('));\r\n  }\r\n};\r\n"], "mappings": ";AAAA;AACA;AACA,IAAMA,MAAM,GAAG,KAAK;AACpB,eAAe;EACbC,OAAO,EAAEC,MAAM,CAACC,cAAc;EAC9BC,KAAK,EAAEF,MAAM,CAACG,YAAY;EAC1BC,GAAG,WAAHA,GAAGA,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;IACpB,IAAI,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;MACxB,OAAO,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IACvC;IACA,IAAI,IAAI,CAACG,QAAQ,CAACH,KAAK,CAAC,EAAE;MACxB,IAAI;QACFA,KAAK,GAAGI,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC;MAC/B,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;MACpB;MACA,OAAO,IAAI,CAACV,IAAI,CAAC,CAACI,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IACvC;IACA,OAAO,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;EACvC,CAAC;EACDS,GAAG,WAAHA,GAAGA,CAACX,IAAI,EAAEC,GAAG,EAAE;IACb,IAAIC,KAAK,GAAG,IAAI,CAACF,IAAI,CAAC,CAACY,OAAO,CAACX,GAAG,CAAC;IACnC,IAAI,IAAI,CAACY,OAAO,CAACX,KAAK,CAAC,EAAE;MACvB,IAAI;QACFA,KAAK,GAAGI,IAAI,CAACQ,KAAK,CAACZ,KAAK,CAAC;MAC3B,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdN,KAAK,GAAG,IAAI,CAACF,IAAI,CAAC,CAACY,OAAO,CAACX,GAAG,CAAC;MACjC;IACF;IACA,OAAOC,KAAK;EACd,CAAC;EACDa,UAAU,WAAVA,UAAUA,CAACd,GAAG,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACH,GAAG,CAAC,SAAS,EAAEN,MAAM,GAAGQ,GAAG,EAAEC,KAAK,CAAC;EAC1C,CAAC;EACDc,UAAU,WAAVA,UAAUA,CAACf,GAAG,EAAE;IACd,OAAO,IAAI,CAACU,GAAG,CAAC,SAAS,EAAElB,MAAM,GAAGQ,GAAG,CAAC;EAC1C,CAAC;EACDgB,QAAQ,WAARA,QAAQA,CAAChB,GAAG,EAAEC,KAAK,EAAE;IACnB,IAAID,GAAG,IAAI,WAAW,IAAIA,GAAG,IAAI,cAAc,EAAE;MAC/CiB,QAAQ,CAACnB,GAAG,CAACE,GAAG,EAAEC,KAAK,CAAC;IAC1B;IACA,IAAI,CAACH,GAAG,CAAC,OAAO,EAAEN,MAAM,GAAGQ,GAAG,EAAEC,KAAK,CAAC;EACxC,CAAC;EACDiB,QAAQ,WAARA,QAAQA,CAAClB,GAAG,EAAE;IACZ,OAAO,IAAI,CAACU,GAAG,CAAC,OAAO,EAAElB,MAAM,GAAGQ,GAAG,CAAC;EACxC,CAAC;EACDE,QAAQ,WAARA,QAAQA,CAACD,KAAK,EAAE;IACd,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC,CAAC;EACDG,QAAQ,WAARA,QAAQA,CAACH,KAAK,EAAE;IACd,OAAOkB,OAAA,CAAOlB,KAAK,MAAK,QAAQ;EAClC,CAAC;EACDmB,WAAW,WAAXA,WAAWA,CAACpB,GAAG,EAAE;IACfiB,QAAQ,CAACI,MAAM,CAACrB,GAAG,CAAC;IACpB,IAAI,CAACJ,KAAK,CAAC0B,UAAU,CAACtB,GAAG,CAAC;EAC5B,CAAC;EACDuB,aAAa,WAAbA,aAAaA,CAACvB,GAAG,EAAE;IACjB,IAAI,CAACP,OAAO,CAAC6B,UAAU,CAACtB,GAAG,CAAC;EAC9B,CAAC;EACDwB,UAAU,WAAVA,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC5B,KAAK,CAAC6B,KAAK,CAAC,CAAC;EACpB,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,IAAI,CAACjC,OAAO,CAACgC,KAAK,CAAC,CAAC;EACtB,CAAC;EACDb,OAAO,WAAPA,OAAOA,CAACX,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,CAAC,EAAEA,KAAK,CAAC0B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAC9B1B,KAAK,CAAC0B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IACzB1B,KAAK,CAAC0B,OAAO,CAAC,GAAG,CAAC,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}]}
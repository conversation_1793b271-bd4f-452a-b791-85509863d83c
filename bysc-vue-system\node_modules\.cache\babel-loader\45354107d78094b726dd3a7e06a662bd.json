{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\thirdLogin.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\thirdLogin.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'justauth-page',\n  // 第三方列表分页\n  method: 'POST',\n  path: '/extension/extension/justauth/source/page'\n}, {\n  name: 'switch-justauth',\n  // 改变第三方状态\n  method: 'POST',\n  path: '/extension/extension/justauth/source/status/switch'\n}, {\n  name: 'justauth-save',\n  // 保存\n  method: 'POST',\n  path: '/extension/extension/justauth/source/save'\n}, {\n  name: 'justauth-delete',\n  // 删除\n  method: 'POST',\n  path: '/extension/extension/justauth/source/batch/delete'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/thirdLogin.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'justauth-page', // 第三方列表分页\r\n    method: 'POST',\r\n    path: '/extension/extension/justauth/source/page'\r\n  },\r\n  {\r\n    name: 'switch-justauth', // 改变第三方状态\r\n    method: 'POST',\r\n    path: '/extension/extension/justauth/source/status/switch'\r\n  },\r\n  {\r\n    name: 'justauth-save', // 保存\r\n    method: 'POST',\r\n    path: '/extension/extension/justauth/source/save'\r\n  },\r\n  {\r\n    name: 'justauth-delete', // 删除\r\n    method: 'POST',\r\n    path: '/extension/extension/justauth/source/batch/delete'\r\n  }\r\n];"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
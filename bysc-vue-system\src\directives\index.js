import copy from './copy';
import LazyLoad from './lazyLoad';
import permission from './permission';
import draggable from './draggable';
import debounces from './debounce';
// 自定义指令
const directives = {
  copy,
  LazyLoad,
  permission,
  draggable,
  debounces
};

export default {
  install(Vue) {
    Object.keys(directives).forEach(key => {
      Vue.directive(key, directives[key]);
    });
  },
};
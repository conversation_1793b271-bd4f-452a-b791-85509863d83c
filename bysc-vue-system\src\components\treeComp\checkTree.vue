<!--
 * @Author: czw
 * @Date: 2022-11-04 09:34:34
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 09:54:34
 * @FilePath: \kdsp_vue_clear\src\components\treeComp\commonTree.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-input
      placeholder="输入关键字进行过滤"
      size="small"
      style="width:100%;margin-bottom:10px"
      v-model="filterText">
    </el-input>
    <el-tree
      :default-expand-all="defaultexpandall"
      :data="treeData"
      :check-strictly="checkStrictly"
      :props="treeProps"
      :filter-node-method="filterNode"
      :node-key="nodeKey"
      :default-checked-keys="defaultcheckedkeys"
      :highlight-current="highlightcurrent"
      accordion
      ref="tree"
      @check="handleNodeClick"
      :show-checkbox="true"
    >
    </el-tree>
  </div>
</template>
<script>
export default {
  name: 'checkTree',
  props: {
    treeData: {
      type: Array,
      default() {
        return [];
      },
    },
    checkStrictly: {
      type: Boolean,
      default() {
        return false;
      }
    },
    defaultcheckedkeys: {
      type: Array,
      default() {
        return [];
      }
    },
    highlightcurrent: {
      type: Boolean,
      default() {
        return false;
      },
    },
    nodeKey: {
      type: String,
      default() {
        return 'id';
      },
    },
    defaultexpandall: {
      type: Boolean,
      default() {
        return false;
      },
    },
    treeProps: {
      type: Object,
      default() {
        return {
          children: 'children',
          label: 'label',
        };
      },
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  data() {
    return {
      filterText: ''
    };
  },
  methods: {
    setTree(e) {
      this.$refs.tree.setCheckedKeys(e);
    },
    setCheckedNodes(e) {
      this.$refs.tree.setCheckedNodes(e);
    },
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      console.log(data);
      return data[this.treeProps.label].indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      this.$emit('treeNode', this.$refs.tree.getCheckedNodes(false, true));
      this.$emit('treeNodes', this.$refs.tree.getCheckedNodes());
    }
  },
};
</script>
<style lang="less" scoped></style>

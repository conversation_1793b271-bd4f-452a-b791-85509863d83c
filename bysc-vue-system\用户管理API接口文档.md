# 用户管理系统 API 接口文档

## 1. 用户管理接口

### 1.1 获取用户分页列表

- **接口地址**：`systems/userPage`
- **请求方式**：GET
- **接口说明**：分页获取用户列表信息
- **请求参数**：

| 参数名       | 类型   | 必填 | 说明         |
|--------------|--------|------|--------------|
| account      | String | 否   | 用户名       |
| realName     | String | 否   | 姓名         |
| mobile       | String | 否   | 手机号       |
| jobNo        | String | 否   | 员工编号     |
| status       | Number | 否   | 状态(0禁用,1正常,99已锁定) |
| companyName  | String | 否   | 公司名称     |
| workDeptName | String | 否   | 工作部门名称 |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "userId1",
        "account": "user001",
        "realName": "张三",
        "mobile": "***********",
        "jobNo": "EMP001",
        "gender": "1",
        "status": 1,
        "companyId": "company1",
        "companyName": "XX科技有限公司",
        "belongDeptId": "dept1",
        "belongDeptName": "研发部",
        "workDeptId": "dept2",
        "workDeptName": "前端组",
        "createTime": "2022-01-01 10:00:00",
        "updateTime": "2022-01-10 15:30:00",
        "lastLoginTime": "2022-01-20 09:15:30"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 1.2 新增/修改用户

- **接口地址**：`systems/addUser`
- **请求方式**：POST
- **接口说明**：新增或修改用户信息
- **请求参数**：

| 参数名           | 类型   | 必填 | 说明         |
|------------------|--------|------|--------------|
| id               | String | 否   | 用户ID，存在则为修改，不存在则为新增 |
| account          | String | 是   | 用户名       |
| realName         | String | 是   | 姓名         |
| mobile           | String | 是   | 手机号       |
| jobNo            | String | 是   | 员工编号     |
| companyId        | String | 是   | 公司ID       |
| belongDeptId     | String | 是   | 所属部门ID   |
| workDeptId       | String | 是   | 工作部门ID   |
| roleIds          | Array  | 是   | 角色ID数组   |
| gender           | String | 是   | 性别(1男,0女) |
| status           | Number | 是   | 状态(0禁用,1正常) |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "userId1",
    "account": "user001",
    "realName": "张三",
    "mobile": "***********",
    "jobNo": "EMP001",
    "gender": "1",
    "status": 1,
    "companyId": "company1",
    "companyName": "XX科技有限公司",
    "belongDeptId": "dept1",
    "belongDeptName": "研发部",
    "workDeptId": "dept2",
    "workDeptName": "前端组",
    "createTime": "2022-01-01 10:00:00",
    "updateTime": "2022-01-10 15:30:00"
  }
}
```

### 1.3 删除用户

- **接口地址**：`systems/userDelete`
- **请求方式**：POST
- **接口说明**：删除指定用户
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明     |
|--------|--------|------|----------|
| id     | String | 是   | 用户ID   |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 1.4 解锁用户

- **接口地址**：`systems/unLock`
- **请求方式**：POST
- **接口说明**：解锁已锁定的用户账号
- **请求参数**：

| 参数名 | 类型  | 必填 | 说明         |
|--------|-------|------|--------------|
| ids    | Array | 是   | 用户ID数组   |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "解锁成功",
  "data": {
    "successCount": 2,
    "failCount": 0,
    "failIds": []
  }
}
```

### 1.5 批量分配角色

- **接口地址**：`systems/batch-save-user-role`
- **请求方式**：POST
- **接口说明**：为多个用户批量分配角色
- **请求参数**：

| 参数名  | 类型  | 必填 | 说明         |
|---------|-------|------|--------------|
| userIds | Array | 是   | 用户ID数组   |
| roleIds | Array | 是   | 角色ID数组   |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "successCount": 3,
    "failCount": 0,
    "details": [
      {
        "userId": "userId1",
        "userName": "张三",
        "roles": ["管理员", "操作员"]
      },
      {
        "userId": "userId2",
        "userName": "李四",
        "roles": ["管理员", "操作员"]
      },
      {
        "userId": "userId3",
        "userName": "王五",
        "roles": ["管理员", "操作员"]
      }
    ]
  }
}
```

## 2. 角色管理接口

### 2.1 获取角色列表

- **接口地址**：`systems/roleList`
- **请求方式**：GET
- **接口说明**：获取所有角色信息
- **请求参数**：无
- **返回结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "role1",
      "roleName": "系统管理员",
      "roleCode": "ADMIN",
      "status": 1,
      "remark": "系统管理员角色",
      "createTime": "2022-01-01 10:00:00",
      "updateTime": "2022-01-10 15:30:00"
    },
    {
      "id": "role2",
      "roleName": "普通用户",
      "roleCode": "USER",
      "status": 1,
      "remark": "普通用户角色",
      "createTime": "2022-01-01 10:00:00",
      "updateTime": "2022-01-10 15:30:00"
    }
  ]
}
```

## 3. 组织机构接口

### 3.1 获取组织机构树

- **接口地址**：`systems/organizationTree`
- **请求方式**：GET
- **接口说明**：获取组织机构树形结构数据
- **请求参数**：

| 参数名   | 类型   | 必填 | 说明         |
|----------|--------|------|--------------|
| parentId | Number | 是   | 父级组织ID   |

- **返回结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "org1",
      "organizationName": "总公司",
      "organizationCode": "ZGS",
      "parentId": 0,
      "sort": 1,
      "status": 1,
      "remark": "总公司",
      "children": [
        {
          "id": "org2",
          "organizationName": "北京分公司",
          "organizationCode": "BJFGS",
          "parentId": "org1",
          "sort": 1,
          "status": 1,
          "remark": "北京分公司",
          "children": [
            {
              "id": "org5",
              "organizationName": "研发部",
              "organizationCode": "YFB",
              "parentId": "org2",
              "sort": 1,
              "status": 1,
              "remark": "研发部",
              "children": []
            }
          ]
        },
        {
          "id": "org3",
          "organizationName": "上海分公司",
          "organizationCode": "SHFGS",
          "parentId": "org1",
          "sort": 2,
          "status": 1,
          "remark": "上海分公司",
          "children": []
        }
      ]
    }
  ]
}
```

## 4. 系统同步接口

### 4.1 部门同步

- **接口地址**：`systems/deptSync`
- **请求方式**：GET/POST
- **接口说明**：同步部门数据
- **请求参数**：无
- **返回结果**：
```json
{
  "code": 200,
  "msg": "同步成功",
  "data": {
    "newDeptCount": 5,
    "updatedDeptCount": 3,
    "deletedDeptCount": 1,
    "totalDeptCount": 20,
    "syncTime": "2022-01-20 15:30:00"
  }
}
```

### 4.2 人员同步

- **接口地址**：`systems/personSync`
- **请求方式**：GET/POST
- **接口说明**：同步人员数据
- **请求参数**：无
- **返回结果**：
```json
{
  "code": 200,
  "msg": "同步成功",
  "data": {
    "newPersonCount": 10,
    "updatedPersonCount": 5,
    "deletedPersonCount": 2,
    "totalPersonCount": 50,
    "syncTime": "2022-01-20 15:35:00"
  }
}
```

### 4.3 用户同步

- **接口地址**：`systems/userSyc`
- **请求方式**：GET/POST
- **接口说明**：同步用户数据
- **请求参数**：无
- **返回结果**：
```json
{
  "code": 200,
  "msg": "同步成功",
  "data": {
    "newUserCount": 8,
    "updatedUserCount": 6,
    "deletedUserCount": 1,
    "totalUserCount": 45,
    "syncTime": "2022-01-20 15:40:00"
  }
}
``` 
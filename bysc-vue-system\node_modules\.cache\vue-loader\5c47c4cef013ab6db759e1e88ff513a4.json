{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\webscoket\\wsnotice.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\webscoket\\wsnotice.vue", "mtime": 1745205562778}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./wsnotice.vue?vue&type=template&id=9c0326b8&scoped=true\"\nimport script from \"./wsnotice.vue?vue&type=script&lang=js\"\nexport * from \"./wsnotice.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9c0326b8\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('9c0326b8')) {\n      api.createRecord('9c0326b8', component.options)\n    } else {\n      api.reload('9c0326b8', component.options)\n    }\n    module.hot.accept(\"./wsnotice.vue?vue&type=template&id=9c0326b8&scoped=true\", function () {\n      api.rerender('9c0326b8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/webscoket/wsnotice.vue\"\nexport default component.exports"]}
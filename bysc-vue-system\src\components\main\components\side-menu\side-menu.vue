<template>
  <div>
    <slot></slot>
    <el-menu
      style="border: 0px !important;"
      :key="timer"
      ref="menu"
      :collapse="collapsed"
      :default-active="activeName"
      text-color="#fff"
      :default-openeds="openedNames"
      :unique-opened="accordion"
      background-color="#002140"
      width="auto"
      @select="handleSelect"
    >
      <!-- <el-menu-item
        index="home"
      >
        <i class="el-icon-s-home"></i>
        <span slot="title">首页</span>
      </el-menu-item> -->
      <el-menu-item
        v-for="(item, index) in noChildren"
        :key="index + 'nochild'"
        :index="item.resourcePageName"
      >
        <i :class="item.resourceIcon"></i>
        <span slot="title">{{ item.resourceName }}</span>
      </el-menu-item>
      <el-submenu
        v-for="(item, index) in hasChildren"
        :key="index + 'haschild'"
        :index="item.resourcePageName"
      >
        <template slot="title">
          <i :class="item.resourceIcon"></i>
          <span>{{ item.resourceName }}</span>
        </template>
        <template v-for="(i, ind) in item.children">
          <el-menu-item
            v-if="!i.children&&i.resourceShow"
            :index="i.resourcePageName"
            :key="'menu-' + ind + i.resourceName"
            >
            <i :class="i.resourceIcon"></i>
            {{ i.resourceName }}</el-menu-item
          >
          <el-submenu
            v-if="i.children&&i.resourceShow"
            :index="i.resourcePageName"
            :key="'menu-' + ind + i.resourceName"
          >
            <template slot="title">
              <i :class="i.resourceIcon"></i>
              <span>{{ i.resourceName }}</span>
            </template>
            <template v-for="r in i.children">
              <el-menu-item v-if="!r.children&&r.resourceShow" :index="r.resourcePageName" :key="'menu-'+r.resourceName">
                <i :class="r.resourceIcon"></i>
                {{ r.resourceName }}
              </el-menu-item>
            </template>
          </el-submenu>
        </template>
      </el-submenu>
    </el-menu>
  </div>
</template>
<script>

const getUnion = function (arr1, arr2) {
  return Array.from(new Set([...arr1, ...arr2]));
};

export default {
  name: 'sidemenu',
  props: {
    menuList: {
      type: Array,
      default() {
        return [];
      },
    },
    collapsed: {
      type: Boolean,
    },
    theme: {
      type: String,
      default: 'dark',
    },
    rootIconSize: {
      type: Number,
      default: 20,
    },
    iconSize: {
      type: Number,
      default: 16,
    },
    accordion: {
      type: Boolean,
      default: true,
    },
    activeName: {
      type: String,
      default: '',
    },
    // openNames: {
    //   type: Array,
    //   default: () => []
    // }
  },
  data() {
    return {
      timer: '',
      openedNames: [],
      defaultActiveName: null,
    };
  },
  computed: {
    noChildren() {
      return this.menuList.filter(item => (!item.children && item.resourceShow));
    },
    hasChildren() {
      return this.menuList.filter(item => (item.children && item.resourceShow));
    },
    textColor() {
      return this.theme === 'dark' ? '#fff' : '#495060';
    },
  },
  methods: {
    setOpenedNames(openedNames) {
      this.openedNames = openedNames;
      // this.timer = new Date().getTime();
    },
    handleSelect(name) {
      this.defaultActiveName = name;
      this.$emit('on-select', name);
    },
    getOpenedNamesByActiveName(name) {
      return this.$route.matched
        .map(item => item.name)
        .filter(item => item !== name);
    },
    updateOpenName(name) {
      this.openedNames = this.getOpenedNamesByActiveName(name);
    },
  },
  // eslint-disable-next-line no-dupe-keys
  watch: {
    activeName(name) {
      if (this.accordion) {
        this.openedNames = this.getOpenedNamesByActiveName(name);
      } else {
        this.openedNames = getUnion(
          this.openedNames,
          this.getOpenedNamesByActiveName(name)
        );
      }
    },
  },
  mounted() {
    this.openedNames = getUnion(
      this.openedNames,
      this.getOpenedNamesByActiveName(name)
    );
  },
};
</script>

//@import '~iview/src/styles/index.less';
// 升级4.0.0
.technology{
  //@menu-dark-title: #001D30 !important;
  //@menu-dark-active-bg: #001D30 !important;
  //@menu-dark-group-title-color: #025C7E;
  //@menu-dark-subsidiary-color: #52768C !important;
  //@layout-sider-background: #001D30 !important;
  //@layout-header-background: #153952;
  //@layout-body-background: ;
  //@table-td-highlight-bg: rgba(208,235,249,0.1) !important;
  //@table-td-stripe-bg: rgba(33,72,100,0.7) !important;
  //@table-thead-bg: rgba(208,235,249,0.1) !important;
  //@table-td-hover-bg: rgba(208,235,249,0.2) !important;
  //@border-color-base: #396d83 !important;
  //@card-prefix-cls: rgba(208,235,249,0) !important;
  //@btn-primary-bg: #025C7E !important;
  //@btn-primary-color:#FFFFFF !important;
  //@input-bg: #396D83 !important;
  //@primary-color:#025C7E !important;
  //@input-color:#396D83;
  //@input-placeholder-color:#396D83;
  /* menu样式 */
  /*系统顶部标题、消息中心、个人中心整个横条区域样式*/
  .headerBarSortTheme {
    line-height: 67px;
    padding-right: 35px;
    height       : 67px;
    min-width    : 1080px;
    background   : #001D2F;
    border-bottom: 1px solid rgba(57, 109, 131, 1);
    //position     : fixed;
    width        : 100%;
    //z-index      : 999;
  }

  /*平台标题总称区域的样式*/
  .platformTitleGeneral {
    color: white;
    float          : left;
    height         : 65px;
    display        : flex;
    justify-content: center;
    align-items    : center;
    margin-left    : 15px;
  }
  .amap-logo{
    display: none !important;
  }
  .ivu-menu-submenu.ivu-menu-item-active.ivu-menu-child-item-active>div.ivu-menu-submenu-title{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .theme1 .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-child-item-active div.ivu-menu-submenu-title,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-child-item-active{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu .ivu-menu-dark.ivu-menu-vertical .ivu-submenu .ivu-menu-item:hover{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened div.ivu-menu-submenu-title:hover{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-layout-sider{
    background: #001D30 !important;
  }
  .ivu-menu-dark{
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-submenu-title {
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened div.ivu-menu-submenu-title,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li{
    background: #00253F !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened>div.ivu-menu-submenu-title{
    background: #001D30 !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu-item{
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu li.ivu-menu-item-active, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu li.ivu-menu-item-active:hover{
    background: #025C7E !important;
    color: rgba(255,255,255,0.8) !important;
  }
  .left-sider.ivu-layout-sider{
    border-right: 2px solid #2E5A6C;
  }
  /* layout背景图 */
  //.@{layout-prefix-cls}{
  //  background: rgba(0,0,0,0);
  //}
  background-color: rgb(0,37,63);
  #app{
    background-color: rgb(0,37,63);
  }
  .ivu-layout{
    background: url('../../assets/xbwbg.png') no-repeat fixed ;
    background-size:auto 100% ;
    color: #85C2D6;
    -webkit-background-size: cover;
    //background-attachment:fixed;
    //background-size:cover;
    //-webkit-background-size: cover;
    //-o-background-size: cover;
  }
  .ivu-notice-notice.ivu-notice-notice-closable.ivu-notice-notice-with-desc{
    background: url('../../assets/xbwbg.png') no-repeat fixed 100%;
    color: #85C2D6;
  }
  /* table样式 */

  .ivu-table.ivu-table-default{
    background-color: rgba(208,235,249,0.1) !important;
    //border: 1px solid #396d83 !important;
  }
  .ivu-table-body tr:nth-child(2n) td, .ivu-table-fixed-body tr:nth-child(2n) td{
    background-color: rgba(208,235,249,0) !important;
    color: #85C2D6 !important;
    border: 0px !important;
  }
  .ivu-table-fixed-body tr:nth-child(2n) td {
    background-color: rgba(208,235,249,0) !important;
    color: #85C2D6 !important;
    border: 0px !important;
  }
  .ivu-table td{
    background-color: rgba(33,72,100,0.7) !important;
    color: #85C2D6 !important;
    border: 0px !important;
  }
  .ivu-table-body tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td,.ivu-table-fixed-body tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td,.ivu-table .ivu-table-row-hover td{
    background-color: rgba(208,235,249,0.1) !important;
    color: #85C2D6 !important;
    border: 0px !important;
  }
  .ivu-table-tbody{
    border-bottom: 0px !important;
  }
  .ivu-table thead tr th{
    background-color: rgba(208,235,249,0) !important;
    color: #619BB3 !important;
    border: 0px !important;
  }
  .style-table-num{
    background-color: unset !important;
    color: #85C2D6 !important;
  }
  .ivu-table-wrapper {
    border: none;
  }
  .ivu-table {
    border: 0px !important;
  }
  .ivu-table:before{
    width:0% !important;
  }
  .ivu-table-fixed::before, .ivu-table-fixed-right::before{
    width:0% !important;
  }
  .ivu-table:after{
    width:0% !important;
  }
  .ivu-table-fixed-body,.ivu-table-fixed-header{
    background-color: #025C7E !important;
  }
  .ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n-1).ivu-table-row td:not(.ivu-table-hidden),.ivu-table-fixed-header table thead tr th{
    background-color: rgb(29,68,94) !important;
  }
  .ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n).ivu-table-row td:not(.ivu-table-hidden),.ivu-table-fixed-header table thead tr th{
    background-color: rgb(21,57,82) !important;
  }
  .ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td:not(.ivu-table-hidden){
    background-color: rgb(40,75,99) !important;
  }
  .ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n-1).ivu-table-row.ivu-table-row-hover td:not(.ivu-table-hidden){
    background-color: rgb(40,75,99) !important;
  }
  body *{
    color: #85C2D6;
  }
// 列表下划线
.ivu-list-split{
border-bottom: 1px solid rgba(57,109,131,1);
}
  .ivu-list-item{
border-bottom: 1px solid rgba(57,109,131,1);
  }
  /* 筛选样式 */
  .ivu-badge-dot{
    top: 8px;
    right: 1px;
  }
  .condition-container{
    padding: 0px !important;
    border:1px solid rgba(57,109,131,1);
    color: #85C2D6;
  }
  .condition-title,.nav.height40,.nav.heightAuto{
    background:rgba(208,235,249,0.1) !important;
  }
  .condition-title{
    border-top-right-radius: unset !important;
  }
  .condition-container span.active{
    color: #F87A7B !important;
  }
  .condition-box{
    background: rgba(0,0,0,0) !important;
  }
  .mutil-query-title{
    color: #619BB3 !important;
  }
  .more{
    color: #85C2D6 !important;
  }
  .nav.height40,.nav.heightAuto{
    border-color: #396D83 !important;
  }
  .nav.height40:first-child{
    border-top-style: solid;
    border-color: #396D83 !important;
  }
  .header-con{
    background: #153952 !important;
  }
  .table-sort i{
    color: #11758e !important;
  }
  .sort-active span{
    color: #21C2D8 !important;
  }
  .sort-active{
    border-color: #21C2D8 !important;
  }
  .table-sort i:hover{
    color: #21C2D8 !important;
  }
  .ivu-layout-content .ivu-icon{
    color: paleturquoise !important;
  }
  .ivu-layout-content .ivu-icon.iconActive{
    color: #21C2D8 !important;
  }
  /* 面包屑样式 */
  .ivu-breadcrumb-item-link{
    color: #85C2D6 !important;
  }
  .custom-content-con *{
    color: #85C2D6 !important;
  }
  /*环信样式*/
  .el-drawer span{
    color: #85C2D6!important;
  }
  .el-drawer{
    background:rgba(57,109,131,1)!important;
    color: #85C2D6!important;
  }
  .ant-menu-item {
    background:transparent!important;
    border-bottom:1px solid #85C2D6 !important;
    color: #85C2D6!important;
  }
  .ant-menu-item-selected{
    background:rgba(57,109,131,1)!important;
    color: #85C2D6!important;
  }
  .userlist{
    background:transparent!important;
    color: #85C2D6!important;
  }
  .ant-layout-sider-children{
    background:transparent!important;
    color: #85C2D6!important;
  }
  .moreMsgs{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .messagebox{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .messagebox-footer{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .messagebox-header{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .messagebox-content{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .ant-layout-header{
    background:transparent!important;
    border:1px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .ant-menu-horizontal{
    background:transparent!important;
    border:0px solid rgba(57,109,131,1) !important;
    color: #85C2D6!important;
  }
  .ant-layout{
    background: transparent!important;
  }
  /* card样式 */
  .ivu-card{
    background:rgba(208,235,249,0);
    border:0px solid rgba(57,109,131,1) !important;
    color: #85C2D6;
  }
  .ivu-card:hover{
    box-shadow: unset !important;
  }
  .ivu-card-head{
    border:1px solid rgba(57,109,131,1) !important;
    background:rgba(208,235,249,0);
    color: #85C2D6;
  }
  .ivu-card-head *{
    color: #85C2D6 !important;
  }
  .table-sort{
    color: #85C2D6 !important;
  }
  .ivu-card-header{
    background: rgba(208,235,249,0) !important;
  }
  .ivu-card-body{
    background:rgba(208,235,249,0);
    border:1px solid rgba(57,109,131,1);
  }
  /* 半透明card */
  .ivu-card.translucent{
    background:rgba(208,235,249,0);
    border:0px solid rgba(57,109,131,1) !important;
    color: #85C2D6;
  }
  .ivu-card.translucent .ivu-card-head{
    border:1px solid rgba(57,109,131,1) !important;
    background:rgba(208,235,249,0.1);
    color: #85C2D6;
  }
  .ivu-card.translucent .ivu-card-head *{
    color: #85C2D6 !important;
  }
  .ivu-card.translucent .ivu-card-header{
    background: rgba(208,235,249,0.1) !important;
  }
  .ivu-card.translucent .ivu-card-body{
    background:rgba(208,235,249,0.1);
    border:1px solid rgba(57,109,131,1);
  }
  /* 全透明无边框card */
  .ivu-card.no-border{
    background:rgba(208,235,249,0);
    border:0px solid rgba(57,109,131,0) !important;
    color: #85C2D6;
  }
  .ivu-card.no-border .ivu-card-head{
    border:1px solid rgba(57,109,131,0) !important;
    background:rgba(208,235,249,0);
    color: #85C2D6;
  }
  .ivu-card.no-border .ivu-card-head *{
    color: #85C2D6 !important;
  }
  .ivu-card.no-border .ivu-card-header{
    background: rgba(208,235,249,0) !important;
  }
  .ivu-card.no-border .ivu-card-body{
    background:rgba(208,235,249,0);
    border:1px solid rgba(57,109,131,0);
  }
  /* 全透明有边框无分界线card */
  .ivu-card.no-title-line{
    background:rgba(208,235,249,0);
    border:0px solid rgba(57,109,131,1) !important;
    color: #85C2D6;
  }
  .ivu-card:hover.no-title-line{
    box-shadow: unset !important;
  }
  .no-title-line .ivu-card-head{
    border:1px solid rgba(57,109,131,1) !important;
    border-bottom: 0px !important;
    background:rgba(208,235,249,0);
    color: #85C2D6;
  }
  .no-title-line .ivu-card-head *{
    color: #85C2D6 !important;
  }
  .no-title-line .table-sort{
    color: #85C2D6 !important;
  }
  .no-title-line .ivu-card-header{
    background: rgba(208,235,249,0) !important;
  }
  .no-title-line .ivu-card-body{
    background:rgba(208,235,249,0);
    border-top: 0px !important;
    border:1px solid rgba(57,109,131,1);
  }
  /* btn */
  .ivu-btn-primary{
    background-color: #025C7E !important;
    color: #FFFFFF !important;
    border-color: #001D30 !important;
  }
  .ivu-btn-text{
    background-color: #025C7E !important;
    color: #FFFFFF !important;
    border-color: #001D30 !important;
  }
  .ivu-btn i.ivu-icon{
    color: #FFFFFF !important;
  }
  .ivu-btn span{
    color: #FFFFFF !important;
  }
  .ivu-btn-default{
    background-color: #025C7E !important;
    color: #FFFFFF !important;
    border-color: #001D30 !important;
  }
  .ivu-btn-error{
    background-color: #F87A7B !important;
    color: #FFFFFF !important;
    border-color: #001D30 !important;
  }
  .actionBtn{
    color: #21C2D8 !important;
  }
  .operator-btn{
    margin-bottom: 10px !important;
  }
  /* 滚动条样式 */
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar
  {
    width: 10px;
    background-color: #025C7E;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track
  {
    box-shadow: inset 0 0 6px rgba(0,0,0,0.3) !important;
    border-radius: 10px !important;
    background-color: #025C7E !important;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb
  {
    border-radius: 10px !important;
    box-shadow: inset 0 0 6px rgba(0,0,0,.3) !important;
    background-color: #001D30 !important;
  }
  /*定义最上方和最下方的按钮*/
  ::-webkit-scrollbar-button{
    background-color: #001D30 !important;
    border:1px solid #001D30 !important;
  }
  /* input样式 */
  //input{
  //  background-color: rgba(57,109,131,0);
  //  color: #ffffff;
  //  border-color:rgba(57,109,131,1);
  //  border-radius:5px;
  //}
  .ivu-input{
    background-color: rgba(57,109,131,0);
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-cascader-label {
    color: #ffffff;
  }
  .ivu-input-number-input{
    background-color: rgba(57,109,131,0);
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-input-number-input-wrap{
    background-color: rgba(57,109,131,0);
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-input-number.ivu-input-number-default{
    background-color: rgba(57,109,131,0);
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .style-body .ivu-input{
    background-color: #00253f !important;
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .style-body  .ivu-select{
    background-color: #00253f !important;
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  input::placeholder{
    color: #396D83 !important;
  }
  .ivu-select-placeholder{
    color: #396D83 !important;
  }
  textarea::placeholder{
    color: #396D83 !important;
  }
  .ivu-input-word-count{
    background-color: rgba(57,109,131,0) !important;
    color: rgba(57,109,131,1) !important;
  }
  .ivu-form-item-error-tip{
    color: #F87A7B !important;
  }
  a{
    color: #21C2D8 !important;
  }
  .ivu-select{
    background-color: rgba(57,109,131,0) !important;
    color: #ffffff !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-selection{
    background-color: rgba(57,109,131,0) !important;
    color: #ffffff !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-dropdown{
    background-color: #284B63 !important;
    color: #85C2D6 !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-item:hover{
    background-color: rgba(208,235,249,0.2) !important;
  }
  .ivu-select-item{
    color: #85C2D6  !important;
  }
  .ivu-select-input{
    color: #85C2D6  !important;
  }
  /* 分页样式 */
  .ivu-page-prev,.ivu-page-item,.ivu-page-next{
    background-color: #001D30 !important;
    color: #FFFFFF !important;
    border-color: #001D30 !important;
  }
  .ivu-page-item.ivu-page-item-active{
    background-color: #284B63 !important;
    color: #85C2D6 !important;
    border-color: #001D30 !important;
  }
  .ivu-page-options input{
    background-color: rgba(57,109,131,0);
    color: rgba(57,109,131,1);
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-spin{
    border-radius: 5px;
    background-color: rgba(57,109,131,0.7);
  }
  .ivu-page.mini *{
    color: #85C2D6 !important;
  }
  /* 树形下拉框样式 */
  .style-poptip .ivu-btn-default{
    background-color: rgba(57,109,131,0) !important;
    color: rgba(57,109,131,1) !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px;
  }
  .ivu-poptip{
    //width: calc(100% - 200px);
    z-index: 999;
  }
  .ivu-poptip-inner{
    z-index: 999;
  }
  .style-poptip .ivu-poptip-rel{
    width: 100%
  }
  .ivu-poptip-title{
    background-color: #001D30 !important;
    border-bottom: #85C2D6!important;
  }
  .ivu-poptip-title-inner{
    color: #85C2D6!important;
  }
  .ivu-poptip-title:after{
    background-color: #001D30 !important;
  }
  .ivu-poptip-body{
    background-color: #001D30 !important;
    color: #85C2D6 !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:0px !important;
    max-height: 300px;
    overflow-y: scroll;
  }
  .ivu-poptip-body-content{
    background-color: #001D30 !important;
    color: #85C2D6 !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:0px !important;
  }
  .ivu-collapse.ivu-collapse-simple{
    background-color: rgba(57,109,131) !important;
    color: #85C2D6 !important;
    border-color:rgba(57,109,131,1) !important;
  }
  .ivu-collapse.ivu-collapse-simple .ivu-collapse-item .ivu-collapse-header{
    background-color: rgba(57,109,131) !important;
    color: #85C2D6 !important;
    border-color:rgba(57,109,131,1) !important;
  }
  .pt-button span{
    display: inline;
    color: #FFFFFF;
    width: auto !important;
  }
  .pt-button i.ivu-icon{
    color: #11758e !important;
  }
  .pt-button span div{
    height: 100%;
    line-height: 30px;
  }
  .ivu-tabs{
    height: 100%;
  }
  .ivu-collapse-content{
    border: 0px;
    background-color: rgba(57,109,131) !important;

  }
  .ivu-collapse > .ivu-collapse-item *{
    color: #85C2D6;
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header{
    border-bottom: 0px !important;
  }
  /* tag样式 */
  .ivu-tag-dot{
    background-color: #001D30 !important;
    border-color: #001D30 !important;
  }
  .ivu-tag-text{
    color: white !important;
  }
  .ivu-tag.ivu-tag-default,.ivu-tag-blue,.ivu-tag-green,.ivu-tag-orange,.ivu-tag-red{
    background-color: #001D30 !important;
    border-color: #001D30 !important;
  }
  .ivu-select-multiple .ivu-tag {
    background-color: transparent !important;
    border-color: transparent !important;
  }
  /* 微标记样式 */
  .ivu-badge-status-text {
    color: #85C2D6 !important;
  }
  /* 模态框样式 */
  .ivu-modal-content{
    background:url('../../assets/layout-body-bg.png') no-repeat fixed 100% !important;
    border-radius: 0px;
  }
  .ivu-modal-header{
    background:#153952 !important;
    border-color: #153952 !important;
  }
  .ivu-modal-header-inner{
    color: #85C2D6;
  }
  .ivu-modal-body{
    background:#153952 !important;
  }
  .ivu-modal-body div,ivu-modal-body p{
    color: #85C2D6;
  }
  .ivu-modal-footer{
    background:#153952 !important;
    border-color: #153952 !important;
  }
  /* message提示样式 */
  .ivu-message-notice-content{
    background-color: #1F2D3D !important;
    color: #85C2D6;
  }
  .ivu-notice-notice{
    // margin-top: 120% !important;
    position: relative!important;
    top: 35vh!important;
  }
  /* 穿梭框样式 */
  .ivu-transfer-list-header{
    background:rgba(208,235,249,0) !important;
    border-color: #396d83 !important;
    color: #85C2D6 !important;
  }
  .ivu-transfer-list input{
    background-color: #00253f !important;
    color: #ffffff;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-transfer-list-body{
    background:rgba(208,235,249,0) !important;
    border-color: #396d83 !important;
  }
  .ivu-transfer-list-body *{
    color: #85C2D6 !important;
  }
  .ivu-transfer-list-content-item:hover{
    background: #025C7E !important;
  }
  .ivu-transfer-list-content-item:hover span{
    color: #ffffff !important;
  }
  label{
    color: #85C2D6 !important;
  }
  .ivu-form-item-required .ivu-form-item-label:before{
    color: #F87A7B !important;
  }
  .ivu-checkbox-inner{
    border-color: #396d83 !important;
    background-color: #001D30 !important;
  }
  .ivu-checkbox-wrapper.ivu-checkbox-wrapper-checked.ivu-checkbox-default .ivu-checkbox-inner{
    border-color: #001D30 !important;
    background-color: #025C7E !important;
  }
  .ivu-divider.ivu-divider-horizontal.ivu-divider-default.ivu-divider-dashed{
    border-top:1px dashed #025C7E !important;
  }
  /* tabs */
  .ivu-tabs-bar{
    border-color: #2E5A6C !important;
  }
  .ivu-tabs-tab{
    background-color: #025C7E !important;
    color: #FFFFFF !important;
    border-color: #025C7E !important;
  }
  .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
    background-color: #00253F !important;
    border-color: #85C2D6 !important;
    color: #85C2D6 !important;
  }
  .info{
    color: #85C2D6 !important;
    border-color: #025C7E !important;
  }
  .style-card{
    border-color: #396d83 !important;
    color: #85C2D6 !important;
  }
  .style-header{
    border:1px solid #396d83 !important;
    border-radius: 5px 5px 0px 0px;
    background-color: rgba(57,109,131,0.1) !important;
    color: #85C2D6 !important ;
  }
  .style-body{
    border:1px solid #396d83 !important;
    background-color: rgba(57,109,131,0.1) !important;
    color: #85C2D6 !important;
  }
  .style-dashed-left{
    border-left-color: #396d83 !important;
  }
  .style-title{
    color: #85C2D6 !important;
  }
  .style-border{
    border:1px solid #396d83 !important;
    padding: 10px;
    margin: 0;
    margin-bottom: 10px !important;
  }
  .higher-search{
    background-color: rgba(57,109,131,0) !important;
    border:1px solid #396d83 !important;
  }
  /* step步骤条样式 */
  .ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner{
    background-color: #619bb3 !important;
    border-color: #619bb3 !important;
  }
  .ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner>span{
    color: #02253E !important;
    font-weight: bold !important;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner{
    background-color: rgba(0,0,0,0) !important;
    border-color: #025C7E !important;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner>span{
    color: #619bb3 !important;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-tail > i,.ivu-steps-item.ivu-steps-status-process .ivu-steps-tail > i{
    background-color: #025C7E !important;
    margin-left: 15px !important;
    width: 95% !important;
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail > i,.ivu-steps-item.ivu-steps-status-process .ivu-steps-tail > i{
    background-color: #025C7E !important;
    margin-left: 15px !important;
    width: 95% !important;
  }
  .theme1 .ivu-layout-content .ivu-icon.ivu-steps-icon{
    color: #ffffff !important;
  }
  .ivu-steps .ivu-steps-title{
    color: #619bb3 !important;
    display: block;
    background-color: rgba(0,0,0,0);
  }
  .ivu-steps .ivu-steps-head{
    background-color: rgba(0,0,0,0);
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner{
    background-color: #619BB3 !important;
    border-color: #619BB3 !important;
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner>span{
    color: #02253E !important;
  }
  /* tree样式 */
  .tree-bar > div{
    border-color: #396d83 !important;
  }
  .ivu-tree-title{
    color: #85C2D6;
  }
  .ivu-tree-title:hover{
    color: #ffffff !important;
    background: #025C7E !important;
  }
  .ivu-tree-title.ivu-tree-title-selected{
    color: #ffffff !important;
    background: #025C7E !important;
  }
  .ivu-select-dropdown *{
    color: #85C2D6 !important;
  }
  a.drop-down,a.drop-down i.ivu-icon{
    color: #85C2D6 !important;
  }
  /*字体颜色*/
  .whiteWord{
    color: lightgray;
  }
  .action-wrapper{
    margin: 0 5px 0 0 !important;
  }
  /* 时间控件 */
  span.ivu-date-picker-cells-cell-disabled, span.ivu-date-picker-cells-cell-disabled:hover{
    background: rgba(0,0,0,0.3) !important;
  }
  span.ivu-date-picker-cells-cell-disabled em, span.ivu-date-picker-cells-cell-disabled:hover em{
    color: rgba(170,170,170,0.5) !important;
    background: rgba(0,0,0,0) !important;

  }
  /* 抽屉样式 */
  .ivu-drawer-content {
    background-color: #00253f;
    .drawer-footer {
      background-color: #00253f;
    }
  }
  .ivu-drawer-drag-move-trigger {
    background-color: #2E5A6C;
  }
  // Here are the variables to cover, such as:
  // @primary-color:#8c0776;
  //@background-color:#000000;

  // Base
  /*@body-background        : #001529;
  @component-background   : #001529;
  @input-bg                    : #001529;

  @body-background        : #001529;
  @component-background   : #001529;

  @layout-body-background      : #001529;

  @head-bg                      : #001529;
  @table-thead-bg               : #001529;
  @table-td-stripe-bg           : #001529;*/
  .image-upload-list-cover {
    .ivu-icon{
      color: white !important;
    }
  }
  .ivu-divider-inner-text {
    color: #85C2D6 !important
  }

  //环信样式
  .userMemu{
    background:transparent!important;
    color: #85C2D6!important;
    border-right: 0px solid black!important;
  }
  .menuChild{
    width: 100%;
    background:transparent!important;
    color: #85C2D6!important;
    border: 0px solid black!important;
  }
  .header{
    width: 100%;
    border:1px solid rgba(57,109,131,1) !important;
  }
  .ivu-notice-title{
    color: #17233d !important;
  }
  .ivu-notice-desc{
    color: #ffffff !important;
  }
}

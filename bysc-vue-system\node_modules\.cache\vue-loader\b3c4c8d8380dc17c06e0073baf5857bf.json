{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue", "mtime": 1753782531199}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\nexport default {\r\n  props: {\r\n    showPage: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    eventBus: Object,\r\n    newcolumn: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    searchParams: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n        };\r\n      },\r\n    },\r\n    autoLoad: {\r\n      type: <PERSON><PERSON><PERSON>,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showColumn: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showReset: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showSearch: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    api: {\r\n      type: String,\r\n      default() {\r\n        return '';\r\n      },\r\n    },\r\n    pageSizeOpts: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 50];\r\n      },\r\n      validator(val) {\r\n        return (\r\n          Array.isArray(val)\r\n          && val.length > 0\r\n          && val.reduce((result, item) => {\r\n            return typeof item === 'number' && item > 0 && result;\r\n          }, true)\r\n        );\r\n      },\r\n    },\r\n    currentPage: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  inject: ['showLoading', 'hideLoading'],\r\n  watch: {\r\n    currentPage: {\r\n      deep: true,\r\n      immediate: true,\r\n      handler: function (newVal) {\r\n        if (newVal) {\r\n          this.localCurrentPage = newVal;\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      direction: 'rtl',\r\n      single: false,\r\n      showSearchForm: true,\r\n      checkAllGroup1: [],\r\n      checkAllGroup: [],\r\n      columnArr: [],\r\n      localCurrentPage: 1,\r\n      loading: false,\r\n      rows: [],\r\n      total: 0,\r\n      pageSize: this.pageSizeOpts[0],\r\n    };\r\n  },\r\n  created() {\r\n    if (this.autoLoad) {\r\n      this.query();\r\n    }\r\n    this.eventBus.$on('search', e => {\r\n      if (e !== 'update') {\r\n        this.localCurrentPage = 1;\r\n      }\r\n      this.query();\r\n    });\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.columnArr = [...this.newcolumn];\r\n      this.columnArr.forEach(e => {\r\n        if (e.title) {\r\n          this.checkAllGroup.push(e);\r\n          this.checkAllGroup1.push(e.title);\r\n        }\r\n      });\r\n    }, 100);\r\n  },\r\n  methods: {\r\n    showAll() {\r\n      let arr = [];\r\n      this.checkAllGroup.forEach(e => {\r\n        arr.push(e.title);\r\n      });\r\n      this.checkAllGroup1 = arr;\r\n      this.checkAllGroupChange(arr);\r\n    },\r\n    checkAllGroupChange(e) {\r\n      let arr = [];\r\n      if (this.columnArr[0] && this.columnArr[0].type == 'selection') {\r\n        arr.push({\r\n          type: 'selection',\r\n          width: 60,\r\n          align: 'center',\r\n          fixed: 'left',\r\n        });\r\n      }\r\n      this.columnArr.forEach(item => {\r\n        let isSome = e.some(row => {\r\n          return row == item.title;\r\n        });\r\n        if (isSome) {\r\n          arr.push(item);\r\n        }\r\n      });\r\n      this.$emit('columnChange', arr);\r\n    },\r\n    query(isTimer) {\r\n      if (isTimer) {\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n        ).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      } else {\r\n        this.localCurrentPage = 1;\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: 1,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    queryData() {\r\n      this.localCurrentPage = 1;\r\n      this.loading = true;\r\n      this.$api[this.api](\r\n        {\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n      )\r\n        .then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handlePageChange(current) {\r\n      this.localCurrentPage = current;\r\n      this.query(true);\r\n      this.$emit('pageChange');\r\n    },\r\n    handlePageSizeChange(pageSize) {\r\n      this.localCurrentPage = 1;\r\n      this.pageSize = pageSize;\r\n      this.query();\r\n    },\r\n    // fanqz add 补充可以获取table当前数据\r\n    getTableData() {\r\n      return this.rows;\r\n    },\r\n    setTableData(rows) {\r\n      this.rows = rows;\r\n    },\r\n  },\r\n};\r\n", {"version": 3, "sources": ["Grid.vue"], "names": [], "mappings": ";AAoHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Grid.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-show=\"showSearchForm\" style=\"width: 100%;margin-bottom: 20px;\">\r\n      <slot name=\"search\"></slot>\r\n    </div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <div style=\"float: left\">\r\n          <slot name=\"action\"></slot>\r\n        </div>\r\n        <el-drawer :wrapperClosable=\"false\"\r\n          size=\"200px\"\r\n          title=\"展示/隐藏列\"\r\n          :visible.sync=\"drawer\"\r\n          :direction=\"direction\"\r\n          :append-to-body=\"true\"\r\n        >\r\n          <div style=\"padding: 0 20px; z-index: 999\">\r\n            <div @click.stop=\"showSearchForm = showSearchForm\">\r\n              <el-checkbox-group\r\n                @change=\"checkAllGroupChange($event, index)\"\r\n                v-for=\"(item, index) of checkAllGroup\"\r\n                :key=\"index + 'CheckboxGroup'\"\r\n                v-model=\"checkAllGroup1\"\r\n              >\r\n                <el-checkbox\r\n                  style=\"float: left; clear: both\"\r\n                  :label=\"item.title\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n              <div\r\n                style=\"\r\n                  text-align: center;\r\n                  width: 100%;\r\n                  clear: both;\r\n                  font-size: 14px;\r\n                  color: cornflowerblue;\r\n                  position: relative;\r\n                  top: 10px;\r\n                \"\r\n                v-show=\"checkAllGroup.length > checkAllGroup1.length\"\r\n              >\r\n                <a @click=\"showAll\">全部展示</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-drawer>\r\n        <el-tooltip\r\n          v-if=\"showColumn\"\r\n          style=\"float: right; margin-bottom: 10px\"\r\n          content=\"操作列\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <div>\r\n            <el-button\r\n              size=\"small\"\r\n              circle\r\n              @click.native=\"drawer = true\"\r\n              icon=\"el-icon-s-grid\"\r\n            ></el-button>\r\n          </div>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          v-if=\"showReset\"\r\n          style=\"float: right; margin-right: 10px; margin-bottom: 10px\"\r\n          content=\"刷新\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <el-button\r\n            size=\"small\"\r\n            circle\r\n            @click.native=\"query\"\r\n            icon=\"el-icon-refresh\"\r\n          ></el-button>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          v-if=\"showSearch\"\r\n          style=\"float: right; margin-right: 10px; margin-bottom: 10px\"\r\n          :content=\"showSearchForm ? '隐藏搜索' : '展示搜索'\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <el-button\r\n            size=\"small\"\r\n            @click=\"showSearchForm = !showSearchForm\"\r\n            circle\r\n            icon=\"el-icon-search\"\r\n          ></el-button>\r\n        </el-tooltip>\r\n      </el-col>\r\n    </el-row>\r\n    <slot name=\"other\"></slot>\r\n    <slot name=\"table\" :data=\"rows\" :loading=\"loading\"></slot>\r\n    <el-row\r\n      type=\"flex\"\r\n      justify=\"end\"\r\n      style=\"margin-top: 10px; padding-bottom: 5px; margin-bottom: 10px\"\r\n    >\r\n      <el-pagination\r\n        v-show=\"showPage\"\r\n        @size-change=\"handlePageSizeChange\"\r\n        @current-change=\"handlePageChange\"\r\n        :current-page.sync=\"localCurrentPage\"\r\n        :page-sizes=\"pageSizeOpts\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    showPage: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    eventBus: Object,\r\n    newcolumn: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    searchParams: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n        };\r\n      },\r\n    },\r\n    autoLoad: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showColumn: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showReset: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showSearch: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    api: {\r\n      type: String,\r\n      default() {\r\n        return '';\r\n      },\r\n    },\r\n    pageSizeOpts: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 50];\r\n      },\r\n      validator(val) {\r\n        return (\r\n          Array.isArray(val)\r\n          && val.length > 0\r\n          && val.reduce((result, item) => {\r\n            return typeof item === 'number' && item > 0 && result;\r\n          }, true)\r\n        );\r\n      },\r\n    },\r\n    currentPage: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  inject: ['showLoading', 'hideLoading'],\r\n  watch: {\r\n    currentPage: {\r\n      deep: true,\r\n      immediate: true,\r\n      handler: function (newVal) {\r\n        if (newVal) {\r\n          this.localCurrentPage = newVal;\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      direction: 'rtl',\r\n      single: false,\r\n      showSearchForm: true,\r\n      checkAllGroup1: [],\r\n      checkAllGroup: [],\r\n      columnArr: [],\r\n      localCurrentPage: 1,\r\n      loading: false,\r\n      rows: [],\r\n      total: 0,\r\n      pageSize: this.pageSizeOpts[0],\r\n    };\r\n  },\r\n  created() {\r\n    if (this.autoLoad) {\r\n      this.query();\r\n    }\r\n    this.eventBus.$on('search', e => {\r\n      if (e !== 'update') {\r\n        this.localCurrentPage = 1;\r\n      }\r\n      this.query();\r\n    });\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.columnArr = [...this.newcolumn];\r\n      this.columnArr.forEach(e => {\r\n        if (e.title) {\r\n          this.checkAllGroup.push(e);\r\n          this.checkAllGroup1.push(e.title);\r\n        }\r\n      });\r\n    }, 100);\r\n  },\r\n  methods: {\r\n    showAll() {\r\n      let arr = [];\r\n      this.checkAllGroup.forEach(e => {\r\n        arr.push(e.title);\r\n      });\r\n      this.checkAllGroup1 = arr;\r\n      this.checkAllGroupChange(arr);\r\n    },\r\n    checkAllGroupChange(e) {\r\n      let arr = [];\r\n      if (this.columnArr[0] && this.columnArr[0].type == 'selection') {\r\n        arr.push({\r\n          type: 'selection',\r\n          width: 60,\r\n          align: 'center',\r\n          fixed: 'left',\r\n        });\r\n      }\r\n      this.columnArr.forEach(item => {\r\n        let isSome = e.some(row => {\r\n          return row == item.title;\r\n        });\r\n        if (isSome) {\r\n          arr.push(item);\r\n        }\r\n      });\r\n      this.$emit('columnChange', arr);\r\n    },\r\n    query(isTimer) {\r\n      if (isTimer) {\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n        ).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      } else {\r\n        this.localCurrentPage = 1;\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: 1,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    queryData() {\r\n      this.localCurrentPage = 1;\r\n      this.loading = true;\r\n      this.$api[this.api](\r\n        {\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n      )\r\n        .then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handlePageChange(current) {\r\n      this.localCurrentPage = current;\r\n      this.query(true);\r\n      this.$emit('pageChange');\r\n    },\r\n    handlePageSizeChange(pageSize) {\r\n      this.localCurrentPage = 1;\r\n      this.pageSize = pageSize;\r\n      this.query();\r\n    },\r\n    // fanqz add 补充可以获取table当前数据\r\n    getTableData() {\r\n      return this.rows;\r\n    },\r\n    setTableData(rows) {\r\n      this.rows = rows;\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}
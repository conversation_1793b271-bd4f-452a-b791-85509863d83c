{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\index.js", "mtime": 1745205562784}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/web.dom.iterable\";\nimport copy from \"./copy\";\nimport LazyLoad from \"./lazyLoad\";\nimport permission from \"./permission\";\nimport draggable from \"./draggable\";\nimport debounces from \"./debounce\";\n// 自定义指令\nvar directives = {\n  copy: copy,\n  LazyLoad: LazyLoad,\n  permission: permission,\n  draggable: draggable,\n  debounces: debounces\n};\nexport default {\n  install: function install(Vue) {\n    Object.keys(directives).forEach(function (key) {\n      Vue.directive(key, directives[key]);\n    });\n  }\n};", {"version": 3, "names": ["copy", "LazyLoad", "permission", "draggable", "debounces", "directives", "install", "<PERSON><PERSON>", "Object", "keys", "for<PERSON>ach", "key", "directive"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/index.js"], "sourcesContent": ["import copy from './copy';\r\nimport LazyLoad from './lazyLoad';\r\nimport permission from './permission';\r\nimport draggable from './draggable';\r\nimport debounces from './debounce';\r\n// 自定义指令\r\nconst directives = {\r\n  copy,\r\n  LazyLoad,\r\n  permission,\r\n  draggable,\r\n  debounces\r\n};\r\n\r\nexport default {\r\n  install(Vue) {\r\n    Object.keys(directives).forEach(key => {\r\n      Vue.directive(key, directives[key]);\r\n    });\r\n  },\r\n};"], "mappings": ";;AAAA,OAAOA,IAAI;AACX,OAAOC,QAAQ;AACf,OAAOC,UAAU;AACjB,OAAOC,SAAS;AAChB,OAAOC,SAAS;AAChB;AACA,IAAMC,UAAU,GAAG;EACjBL,IAAI,EAAJA,IAAI;EACJC,QAAQ,EAARA,QAAQ;EACRC,UAAU,EAAVA,UAAU;EACVC,SAAS,EAATA,SAAS;EACTC,SAAS,EAATA;AACF,CAAC;AAED,eAAe;EACbE,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;IACXC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,UAAAC,GAAG,EAAI;MACrCJ,GAAG,CAACK,SAAS,CAACD,GAAG,EAAEN,UAAU,CAACM,GAAG,CAAC,CAAC;IACrC,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}
<template>
  <div class="scoket"></div>
</template>

<script>
export default {
  name: 'WebsocketConnectTest',
  data() {
    return {
      wsUrl: 'ws://localhost',
      websock: null, // ws实例
      startPort: 6146, // 链接webscoket的开始端口
      tryTime: 0, // 当前重连次数
      len: 10, // 设置重连次数
      connectEnd: false,
      errorMsg: '连接失败！请稍后重试',
      msg: {
        type: 1, // 1请求成功，2服务端返回的消息，3断开连接
        data: {}
      }
    };
  },
  mounted() {
  },
  beforeDestroy() {
    // 离开路由之后断开websocket连接
    this.websock && this.websock.close();
  },
  methods: {
    // 初始化weosocket
    initWebSocket(wsUrl, token) {
      if (typeof WebSocket === 'undefined') {
        this.$Message.error('您的浏览器不支持WebSocket，无法获取数据');
        return false;
      }
      this.websock = new WebSocket(wsUrl, token);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen(e) {
      // 链接成功
      this.msg.data = e;
      this.msg.type = 1;
      this.$emit('wsMsg', this.msg);
    },
    websocketonerror() {
      // 连接建立失败重连
      // this.initWebSocket();
    },
    websocketonmessage(e) {
      // 数据接收
      const redata = JSON.parse(e.data);
      this.msg.type = 2;
      this.msg.data = redata;
      this.$emit('wsMsg', this.msg);
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data);
    },
    websocketclose(e) {
      this.msg.data = e;
      this.msg.type = 3;
      this.$emit('wsMsg', this.msg);
    }
  }
};
</script>

<style lang="scss" scoped></style>

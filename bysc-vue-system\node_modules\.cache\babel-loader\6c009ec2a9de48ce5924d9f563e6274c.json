{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\index.js", "mtime": 1745205562807}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import actions from \"./actions\";\nimport getters from \"./getters\";\nimport { state, mutations } from \"./mutations\";\nexport default {\n  state: state,\n  mutations: mutations,\n  actions: actions,\n  getters: getters\n};", {"version": 3, "names": ["actions", "getters", "state", "mutations"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/store/common/index.js"], "sourcesContent": ["import actions from './actions';\r\nimport getters from './getters';\r\nimport {state, mutations} from './mutations';\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters\r\n};\r\n"], "mappings": "AAAA,OAAOA,OAAO;AACd,OAAOC,OAAO;AACd,SAAQC,KAAK,EAAEC,SAAS;AAExB,eAAe;EACbD,KAAK,EAALA,KAAK;EACLC,SAAS,EAATA,SAAS;EACTH,OAAO,EAAPA,OAAO;EACPC,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}
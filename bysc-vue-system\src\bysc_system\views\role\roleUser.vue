<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-08 09:32:24
 * @FilePath: \bycloud-vue\src\systemView\views\role\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="systems/userPage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="rolegrid"
          :auto-load="false"
        >
          <div slot="search">
            <el-input
              style="width: 200px;margin:0 10px 0 0;"
              v-model.trim="searchForm.realName"
              size="small"
              placeholder="请输入姓名"
            ></el-input>
            <el-input
              style="width: 200px;margin:0 10px 0 0;"
              v-model.trim="searchForm.account"
              size="small"
              placeholder="请输入账户名"
            ></el-input>
            <el-button size="small" type="primary" style="margin:0 0 0 10px;" @click="searchTable">搜索</el-button>
            <el-button size="small" @click="resetTable">重置</el-button>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading"  :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{scope.row[item.slot]=='1' ? "男" : "女"}}
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <template>
                  <el-popconfirm
                  @confirm="unbind(scope.row)"
                    title="您确定要解除该角色绑定吗？"
                  >
                    <el-button type="text" size="small" slot="reference">解除角色绑定</el-button>
                  </el-popconfirm>
                  </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import commonTree from '@/components/treeComp/commonTree';
import checkTree from '@/components/treeComp/checkTree';
import iconChoose from '@/components/choose/icon-choose';
const defaultSearchForm = {
  roleId: '',
  account: '',
  realName: ''
};
const defaultForm = {
  comments: '',
  roleKey: '',
  roleName: '',
  roleStatus: 1,
};
export default {
  components: {commonTree, Grid, iconChoose, checkTree},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      loading: false,
      dialogVisible: false,
      roleStatus: true,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        roleName: [
          {required: true, message: '请输入名称', trigger: 'blur'},
          {
            min: 1,
            max: 15,
            message: '长度在 1 到 15 个字符',
            trigger: 'blur',
          },
        ],
        roleKey: [
          {required: true, message: '请输入标识', trigger: 'blur'},
        ]
      },
      drawerName: '添加',
      drawer: false,
      menuDrawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      treeProps: {
        children: 'children',
        label: 'resourceName',
      },
      resourceName: '',
      columns: [
        {
          title: '账户名',
          key: 'account',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '姓名',
          key: 'realName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '手机号',
          key: 'mobile',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '组织',
          key: 'organizationName',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '邮箱',
          key: 'email',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '性别',
          slot: 'gender',
          tooltip: true,
          minWidth: 170,
        },
      ],
      permissionsList: [],
      roleName: '',
      tableData: [],
      selectedResouce: {},
      treedata: [],
      resourceIds: [],
      roleForm: {
        resourceIds: [],
        roleId: ''
      }
    };
  },
  mounted() {
  },

  methods: {
    // 解除角色绑定
    unbind(e) {
      this.$api['systems/revoke']({userId: e.id, roleId: this.searchForm.roleId}).then(data => {
        this.$refs.rolegrid.query();
      });
    },
    // 查询表单数据
    searchTableData() {
      this.$refs.rolegrid.query();
    },
    searchTable() {
      this.$refs.rolegrid.query();
    },
    resetTable() {
      this.searchForm.account = '';
      this.searchForm.realName = '';
      this.$nextTick(() => {
        this.$refs.rolegrid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    gerTreeData() {
      if (this.resourceName) {
        this.$api['systems/getTreeList']({
          resourceName: this.resourceName,
        }).then(data => {
          this.treedata = data;
        });
      } else {
        this.searchForm.parentId = null;
        this.getTrees();
      }
    },
    getTrees() {
      this.$api['systems/getPremissTree']({parentId: 0}).then(data => {
        this.treedata = data;
      });
    },
  },
};
</script>
<style lang="less" scoped></style>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\social.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\social.vue", "mtime": 1745205562854}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./social.vue?vue&type=template&id=3744cf87&scoped=true\"\nimport script from \"./social.vue?vue&type=script&lang=js\"\nexport * from \"./social.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3744cf87\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3744cf87')) {\n      api.createRecord('3744cf87', component.options)\n    } else {\n      api.reload('3744cf87', component.options)\n    }\n    module.hot.accept(\"./social.vue?vue&type=template&id=3744cf87&scoped=true\", function () {\n      api.rerender('3744cf87', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/social.vue\"\nexport default component.exports"]}
<template>
  <div class="app-container">
    <el-popover
      v-model="showSearchDeptTree"
      placement="right"
      width="300"
      trigger="click"
      @show="showSearchTreeEvent"
    >
    <el-input
        size="small"
        clearable
        v-model.trim="filterText"
        placeholder="请输入关键词"
        style="margin-bottom:6px"
      ></el-input>
    <div style="max-height:800px;overflow-y:auto">
      <el-tree
        :data="defaultData"
        :props="treeProps"
        check-strictly
        check-on-click-node
        :node-key="treeKey"
        :highlight-current="true"
        default-expand-all
        :expand-on-click-node="false"
        show-checkbox
        @check="handleNodeClick"
        :filter-node-method="filterNode"
        style="maxheight: 500px; overflow: auto"
        ref="tree"
      >
      </el-tree>
    </div>

      <el-input
        size="small"
        v-model.trim="selectValue"
        :placeholder="treeplaceholder"
        readonly
        clearable
        @clear="clearSelect"
        slot="reference"
      ></el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  props: {
    // 树数据
    defaultData: {
      type: Array,
      default: []
    },
    treeplaceholder: {
      type: String,
      default: '请选择申请部门'
    },
    treeProps: {
      type: Object,
      default: {
        children: "children",
        label: "label",
      },
    },
    // 已选中数据
    value: {
      type: String,
      default: "",
    },
    cnvalue: {
      type: String,
      default: "",
    },
    // 所需绑定key
    treeKey: {
      type: String,
      default: "id",
    },
  },
  data() {
    return {
      treeDatas: [],
      filterText: "",
      selectValue: "",
      expandOnClickNode: true,
      options: [],
      showSearchDeptTree: false,
    };
  },
  mounted() {
    this.selectValue = this.cnvalue
  },
  watch: {
    value(val) {
      if (!val) {
        this.clearSelect();
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data[this.treeProps.label].indexOf(value) !== -1;
    },
    // 展示树时
    showSearchTreeEvent() {
      this.filterText = "";
      this.$refs.tree.setCheckedKeys([]);
      this.$refs.tree.setCheckedKeys([this.value]);
    },
    // 点击节点的响应
    handleNodeClick(node) {
      this.showSearchDeptTree = false;
      this.selectValue = node[this.treeProps.label];
      console.log(node[this.treeProps.label],this.selectValue)
      this.$emit("getSelectCnData", node[this.treeProps.label]);
      this.$emit("getSelectData", node[this.treeKey]);
    },
    // 清空选项时回调
    clearSelect(e) {
      this.selectValue = '';
      this.$emit("getSelectData", "");
    },
  },
};
</script>

  <style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}

.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>
<template>
  <div v-show="showDrawer" class="notice">
    <Tabs :value="tabsName">
      <div slot="extra" class="clearFont">
        <a @click="showDrawer = false" href="#" style="color: #999"
          >收起<Icon type="md-arrow-round-up" size="16"
        /></a>
      </div>
      <TabPane v-if="noticeAuth" label="通知消息" name="name1">
        <div class="content" v-show="noticeMsg.length">
          <div
            class="msgStyle"
            v-for="(item, index) in noticeMsg"
            :key="'notice' + index"
          >
            <div class="lineStyle">
              <div style="float: left">
                级别：
                <span v-show="item.msgLevel == 1" style="color: #63c579"
                  >普通</span
                >
                <span v-show="item.msgLevel == 2" style="color: #f8913b"
                  >中级</span
                >
                <span v-show="item.msgLevel == 3" style="color: #eb3f4c"
                  >严重</span
                >
              </div>
              <div style="float: right; color: #999">{{ item.createTime }}</div>
            </div>
            <div class="lineStyle">类型：{{ item.msgNotifyTypeName }}</div>
            <div class="lineStyle">标题：{{ item.msgTitle }}</div>
            <div class="lineStyle">
              <div style="float: left">
                内容：{{
                  item.msgContent.length > 16
                    ? item.msgContent.substring(0, 15) + "..."
                    : item.msgContent
                }}
              </div>
              <div :class="[noticeAuth?'btnStyle':'btnStyle1']" @click="showMsgDetail(item, 1)">查看</div>
            </div>
          </div>
          <div class="more" v-show="noticeMsg.length == 5&&noticeAuth">
            <a href="#" @click="showMore(1)">查看更多</a>
          </div>
        </div>
        <div v-show="!noticeMsg.length">
          <img
            src="../../../../assets/empty.png"
            style="
              width: 50%;
              position: relative;
              left: 25%;
              top: 20;
              margin-top: 30px;
            "
            alt="暂无数据"
          />
          <div class="textStyle">暂无消息通知~</div>
        </div>
      </TabPane>
      <TabPane v-if="alertAuth" label="告警消息" name="name2">
        <!-- <div class="noticeHeader">
          <div class="titleFont">告警管理</div>
        </div> -->
        <div class="content" v-show="noticeData.length">
          <div
            class="msgStyle"
            v-for="(item, index) in noticeData"
            :key="'notice' + index"
          >
            <!-- <Icon @click.native="clearAlarm(index)" class="iconStyle" size="22" type="ios-close-circle" /> -->
            <div class="lineStyle">
              <div style="float: left">
                级别：
                <span v-show="item.msgLevel == 1" style="color: #63c579"
                  >普通</span
                >
                <span v-show="item.msgLevel == 2" style="color: #f8913b"
                  >中级</span
                >
                <span v-show="item.msgLevel == 3" style="color: #eb3f4c"
                  >严重</span
                >
              </div>
              <div style="float: right; color: #999">{{ item.createTime }}</div>
            </div>
            <div class="lineStyle">类型：{{ item.msgNotifyTypeName }}</div>
            <div class="lineStyle">标题：{{ item.msgTitle }}</div>
            <div class="lineStyle">
              <div style="float: left">
                内容：{{
                  item.msgContent.length > 16
                    ? item.msgContent.substring(0, 15) + "..."
                    : item.msgContent
                }}
              </div>
              <div :class="[alertAuth?'btnStyle':'btnStyle1']" @click="showMsgDetail(item, 2)">查看</div>
            </div>
          </div>
          <div class="more" v-show="noticeData.length == 5&&alertAuth">
            <a href="#" @click="showMore(2)">查看更多</a>
          </div>
        </div>
        <div v-show="!noticeData.length">
          <img
            src="../../../../assets/empty.png"
            style="
              width: 50%;
              position: relative;
              left: 25%;
              top: 20;
              margin-top: 30px;
            "
            alt="暂无数据"
          />
          <div class="textStyle">暂无告警消息~</div>
        </div>
      </TabPane>
    </Tabs>
    <div style="width:100%;clear: both;" v-show="!alertAuth&&!noticeAuth">
        <img src="@/assets/empty.png" style="width:80%;margin-left: 10%;margin-top: 150px;" alt="暂无数据"/>
      </div>
      <div v-show="!alertAuth&&!noticeAuth" style="text-align: center;color: #ccc;clear: both;width: 100%;">您无查看消息的权限哦~</div>
    <Modal footer-hide v-model="showDetail" :title="msgDetails.msgTitle">
      <div>
        <Row>
          <Col span="24" style="padding: 10px">
            <div class="lineStyle">
              <div style="float: left">
                级别：
                <span v-show="msgDetails.msgLevel == 1" style="color: #63c579"
                  >普通</span
                >
                <span v-show="msgDetails.msgLevel == 2" style="color: #f8913b"
                  >中级</span
                >
                <span v-show="msgDetails.msgLevel == 3" style="color: #eb3f4c"
                  >严重</span
                >
              </div>
            </div>
            <div class="lineStyle">
              类型：{{ msgDetails.msgNotifyTypeName }}
            </div>
            <div class="lineStyle">标题：{{ msgDetails.msgTitle }}</div>
            <div class="lineStyle">
              <div style="float: left" v-if="msgDetails.msgContent">
                内容：{{ msgDetails.msgContent }}
              </div>
            </div>
            <div class="lineStyle">
              <div style="float: left" v-if="msgDetails.msgContent">
                创建时间：{{ msgDetails.createTime }}
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'system_notice',
  props: {},
  data() {
    return {
      showDrawer: false,
      noticeData: [],
      noticeMsg: [],
      showDetail: false,
      msgDetails: {},
      noticeAuth: true,
      alertAuth: true,
      tabsName: 'name1'
    };
  },
  inject: ['getUnReadCount'],
  mounted() {
    this.getMenuAuth();
  },
  watch: {
    showDrawer(val) {
      if (val) {
        this.getUnReadList();
      }
    },
  },
  computed: {
  },
  methods: {
    getMenuAuth() {
      let menuarr = [];
      this.$store.getters.menuList.forEach(e => {
        if (e.children) {
          e.children.forEach(row => {
            menuarr.push(row.path);
          });
        }
      });
      this.noticeAuth = menuarr.some(item => {
        return item == '/notice/notification';
      });
      this.alertAuth = menuarr.some(item => {
        return item == '/alert/alert';
      });
      this.$localCache.setLocal('noticeAuth', this.noticeAuth);
      this.$localCache.setLocal('alertAuth', this.alertAuth);
      this.tabsName = this.noticeAuth ? 'name1' : 'name2';
    },
    showMsgDetail(item, i) {
      if (i == 1 && !this.noticeAuth) {
        this.$Message.error('您无查看消息通知的权限');
        return false;
      } else if (i == 2 && !this.alertAuth) {
        this.$Message.error('您无查看告警消息的权限');
        return false;
      }
      this.showDetail = true;
      this.msgDetails = item;
      this.batchRead(item);
    },
    batchRead(ids) {
      this.$api['alert/icsmsginstance-changeReadStatus']({
        ids: Array.isArray(ids) ? ids : [ids.id],
      }).then(() => {
        this.getUnReadList();
        this.getUnReadCount();
      });
    },
    clearAlarm(i) {
      this.noticeData.splice(i, 1);
    },
    clearMsg(i) {
      this.noticeMsg.splice(i, 1);
    },
    getUnReadList() {
      this.$api['config/icsmsginstance-userMsgList']({
        username: this.$localCache.getSession('username'),
      }).then(data => {
        this.noticeData = data.alarm;
        this.noticeMsg = data.message;
      });
    },
    showMore(e) {
      this.showDrawer = false;
      this.$router.push({
        path: e == 2 ? '/alert/alert' : '/notice/notification',
      });
    },
  },
};
</script>
<style lang="less" scoped>
.more {
  width: 100%;
  height: 30px;
  text-align: center;
  color: #1a6fd1;
  position: relative;
  top: -15px;
}
.iconStyle {
  position: absolute;
  right: -10px;
  top: -10px;
  opacity: 0.8;
  z-index: 999;
}
.btnStyle {
  width: 60px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  border-radius: 2px;
  background: #1a6fd1;
  color: #fff;
  float: right;
}
.btnStyle1 {
  width: 60px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  border-radius: 2px;
  background: #ccc;
  color: #fff;
  float: right;
}
.lineStyle {
  width: 100%;
  min-height: 25px;
  word-break: break-all;
  word-wrap: break-word;
  font-size: 13px;
  line-height: 25px;
  clear: both;
}
.msgStyle {
  width: 100%;
  height: auto;
  padding: 10px 15px;
  border: 1px solid #f2f2f2;
  box-shadow: 1px 1px 10px #ccc;
  position: relative;
  margin-bottom: 15px;
}
.content {
  width: 100%;
  height: auto;
  padding: 10px 15px;
  overflow-y: hidden;
}
.closeStyle {
  text-align: center;
  width: 33px;
  height: 33px;
  line-height: 35px;
  position: absolute;
  left: -33px;
  top: 12px;
  background: #2d8cf0;
}
.titleFont {
  margin-left: 10px;
  float: left;
  font-size: 16px;
  font-weight: bold;
  color: #262626;
  line-height: 45px;
}
.clearFont {
  float: right;
  font-size: 12px;
  font-weight: normal;
  color: #313131;
  line-height: 25px;
  opacity: 0.75;
  line-height: 30px;
  margin-right: 10px;
}
.noticeHeader {
  width: 100%;
  height: 45px;
  line-height: 45px;
  position: relative;
  clear: both;
  border-bottom: 1px solid #f2f2f2;
}
.notice {
  width: 390px;
  height: auto;
  padding-bottom: 10px;
  overflow-y: auto;
  border: 1px solid #f2f2f2;
  background: #fff;
  box-shadow: 1px 1px 10px #ccc;
  position: absolute;
  right: 10px;
  top: 67px;
  z-index: 779;
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 1px;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(255, 0, 0, 0.4);
}
.textStyle {
  color: #cccccc;
  font-size: 18px;
  text-align: center;
}
</style>

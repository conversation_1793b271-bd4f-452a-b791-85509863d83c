<template>
  <Card>
    <br/><br/>
    <Form ref="formValidate" :model="users" :rules="ruleValidate"  :label-width="100">
      <Row justify="center" type="flex">
        <Col span="8">
          <Form-item >
            <upload-pic-avatar @on-change="handleUploadLogo" width="235px" ref="upload" ></upload-pic-avatar>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="账户名" prop="username">
            <Input disabled v-model="users.username" maxlength="32" placeholder="暂无数据"></Input>
          </FormItem>
        </Col>
        <Col span="11">
        <FormItem label="姓名" prop="displayName">
          <Input disabled v-model="users.displayName" maxlength="32" placeholder="暂无数据"></Input>
        </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="手机" prop="mobile">
            <Input v-model="users.mobile" maxlength="32" placeholder="暂无数据"></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem label="邮箱" prop="email" v-if="!isPatient">
            <Input v-model="users.email" maxlength="32" placeholder="暂无数据"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
      </Row>
      <Form-item style="text-align: right">
        <Button
          style="margin-right: 20px"
          type="primary"
          @click="ok()"
        >
          保存
        </Button>
      </Form-item>
<!--      <Row justify="center" type="flex">-->
<!--        <Col span="8">-->
<!--          -->
<!--        </Col>-->
<!--      </Row>-->
    </Form>
  </Card>
</template>

<script>
import defaults from '@/assets/defaultAvatar.jpg';
import UploadPicAvatar from '@/components/upload/upload-pic-avatar';
export default {
  name: 'personalCenter',
  components: {UploadPicAvatar, defaults},
  props: {
    value: Boolean,
    isPatient: {
      type: Boolean,
      default: false
    },
    // users: {
    //   type: Object,
    //   default () {
    //     return {
    //       headImg: '',
    //       id: '',
    //       displayName: '',
    //       password: '',
    //       rePassword: '',
    //       nickName: '',
    //       username: '',
    //       sex: 1,
    //       mobile: '',
    //       email: '',
    //       dutyId: ''
    //     }
    //   }
    // }
  },
  inject: ['reload'],
  data() {
    return {
      users: {
        headImg: '',
        id: '',
        displayName: '',
        password: '',
        rePassword: '',
        nickName: '',
        username: '',
        sex: 1,
        mobile: '',
        email: '',
        dutyId: ''
      },
      roleList: [],
      dutyType: [],
      isEdit: false,
      modal: this.value,
      loading: true,
      ruleValidate: {
        username: [
          {required: true, message: '姓名不能为空', trigger: 'change'}
        ],
        email: [
          // { required: true, message: '邮箱不能为空', trigger: 'submit' }
          {type: 'email', message: '请输入正确的邮箱名', trigger: 'change'}
        ],
        mobile: [
          {required: true, message: '入手机号不能为空', trigger: 'change'}
        ],
        displayName: [
          {required: true, message: '姓名不能为空', trigger: 'change'}
        ],
        nickName: [
          {required: true, message: '昵称不能为空', trigger: 'change'}
        ],
        password: [
          {required: true, message: '密码不能为空', trigger: 'change'}
        ]
      }
    };
  },
  watch: {
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    handleUploadLogo(v) {
      this.users.headImg = v;
      this.$store.state.common.userInfo.headImg = v;
    },
    setPicUrl(picUrl) {
      this.$refs.upload.setPicUrl(picUrl);
    },
    handleCancel() {
      this.$router.push({
        name: this.$localCache.getLocal('userJumpUrl')
      });
    },
    onChange(value) {
      this.users.roleId = value;
    },
    ok() {
      this.$refs.formValidate.validate(valid => {
        if (valid) {
          this.$api['account/addUser'](this.users).then(data => {
            this.reload();
            // this.handleCancel()
            // this.getUserAvatar()
            // this.reload()
          });
        }
      });
    },
    cancel() {
      this.$emit('input', false);
      if (this.$refs.depTree) {
        this.$refs.depTree.clearSelect();
      }
      this.$refs.formValidate.resetFields();
    },
    handleSelectDepTree(v) {
      this.users.deptId = v[0];
    },
    initPostSelect() {
      this.$api['param/getSelectListByType']({code: 'DUTY_TYPE'}).then(data => {
        this.dutyType = data;
      });
    },
    getUserInfo() {
      this.$api['account/getUserInfo']({username: this.$store.getters.userInfo.username}).then(data => {
        this.users = data.account;
        this.$refs.upload.setPicUrl(data.account.headImg);
        if (data.account.headImg) {
          this.$refs.upload.setPicUrl(data.account.headImg);
        } else {
          this.$refs.upload.setPicUrl(defaults);
        }
      });
    },

  },
  created() {
    this.getUserInfo();
  }
};
</script>

<style scoped>

</style>

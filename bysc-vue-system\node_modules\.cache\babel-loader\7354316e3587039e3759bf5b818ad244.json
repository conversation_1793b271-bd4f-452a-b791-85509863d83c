{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\routes\\permission-system.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\routes\\permission-system.js", "mtime": 1745205562801}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import Main from '@/components/main/Main';\nimport systemRoutes from '@/bysc_system/routes/system';\nimport localCache from '@/utils/storage';\nimport _ from 'lodash';\nvar defaultRoutes = [\n  // {\n  //   name: 'homeindex',\n  //   path: '/home',\n  //   component: Main,\n  //   redirect: '/home/<USER>',\n  //   meta: {\n  //     showInMenu: true,\n  //     showAlways: true, // 表示一定要展示子菜单\n  //     title: '首页',\n  //     icon: 'ios-apps'\n  //   },\n  //   children: [\n  //     {\n  //       name: 'home',\n  //       path: '/home/<USER>',\n  //       // TODO 这里变更首页\n  //       component: () => import('@/views/home/<USER>'),\n  //       meta: {\n  //         showInMenu: false,\n  //         path: '/home/<USER>',\n  //         title: '系统首页',\n  //         icon: 'logo-apple'\n  //       }\n  //     }\n  //   ]\n  // }\n];\nexport default (function (permissions) {\n  var allRoutes = _.cloneDeep(defaultRoutes);\n  allRoutes = allRoutes.concat(systemRoutes); // 合并系统模块\n  allRoutes = _.cloneDeep(allRoutes);\n  // 可以根据菜单权限过滤路由\n  if (allRoutes.length) {\n    allRoutes.push({\n      path: '*',\n      redirect: allRoutes[0].path\n    });\n  } else {\n    localCache.removeLocal('userToken');\n    localCache.removeLocal('refreshToken');\n    localCache.removeSession('username');\n  }\n  return allRoutes;\n});", {"version": 3, "names": ["Main", "systemRoutes", "localCache", "_", "defaultRoutes", "permissions", "allRoutes", "cloneDeep", "concat", "length", "push", "path", "redirect", "removeLocal", "removeSession"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/routes/permission-system.js"], "sourcesContent": ["import Main from '@/components/main/Main';\r\nimport systemRoutes from '@/bysc_system/routes/system';\r\nimport localCache from '@/utils/storage';\r\nimport _ from 'lodash';\r\n\r\nlet defaultRoutes = [\r\n  // {\r\n  //   name: 'homeindex',\r\n  //   path: '/home',\r\n  //   component: Main,\r\n  //   redirect: '/home/<USER>',\r\n  //   meta: {\r\n  //     showInMenu: true,\r\n  //     showAlways: true, // 表示一定要展示子菜单\r\n  //     title: '首页',\r\n  //     icon: 'ios-apps'\r\n  //   },\r\n  //   children: [\r\n  //     {\r\n  //       name: 'home',\r\n  //       path: '/home/<USER>',\r\n  //       // TODO 这里变更首页\r\n  //       component: () => import('@/views/home/<USER>'),\r\n  //       meta: {\r\n  //         showInMenu: false,\r\n  //         path: '/home/<USER>',\r\n  //         title: '系统首页',\r\n  //         icon: 'logo-apple'\r\n  //       }\r\n  //     }\r\n  //   ]\r\n  // }\r\n];\r\nexport default permissions => {\r\n  let allRoutes = _.cloneDeep(defaultRoutes);\r\n  allRoutes = allRoutes.concat(systemRoutes); // 合并系统模块\r\n  allRoutes = _.cloneDeep(allRoutes);\r\n  // 可以根据菜单权限过滤路由\r\n  if (allRoutes.length) {\r\n    allRoutes.push({\r\n      path: '*',\r\n      redirect: allRoutes[0].path\r\n    });\r\n  } else {\r\n    localCache.removeLocal('userToken');\r\n    localCache.removeLocal('refreshToken');\r\n    localCache.removeSession('username');\r\n  }\r\n\r\n  return allRoutes;\r\n};\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,wBAAwB;AACzC,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,CAAC,MAAM,QAAQ;AAEtB,IAAIC,aAAa,GAAG;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAA,CACD;AACD,gBAAe,UAAAC,WAAW,EAAI;EAC5B,IAAIC,SAAS,GAAGH,CAAC,CAACI,SAAS,CAACH,aAAa,CAAC;EAC1CE,SAAS,GAAGA,SAAS,CAACE,MAAM,CAACP,YAAY,CAAC,CAAC,CAAC;EAC5CK,SAAS,GAAGH,CAAC,CAACI,SAAS,CAACD,SAAS,CAAC;EAClC;EACA,IAAIA,SAAS,CAACG,MAAM,EAAE;IACpBH,SAAS,CAACI,IAAI,CAAC;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEN,SAAS,CAAC,CAAC,CAAC,CAACK;IACzB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLT,UAAU,CAACW,WAAW,CAAC,WAAW,CAAC;IACnCX,UAAU,CAACW,WAAW,CAAC,cAAc,CAAC;IACtCX,UAAU,CAACY,aAAa,CAAC,UAAU,CAAC;EACtC;EAEA,OAAOR,SAAS;AAClB,CAAC", "ignoreList": []}]}
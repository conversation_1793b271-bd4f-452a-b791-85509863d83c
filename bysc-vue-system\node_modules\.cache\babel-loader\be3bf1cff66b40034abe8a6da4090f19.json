{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\inject.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\inject.js", "mtime": 1745205562795}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport axios from \"./axios\";\nimport api from \"./api\";\nimport consts from \"./const\";\nimport dayjs from 'dayjs';\nimport store from \"./store\";\n// import global from '../global/global'\nvar global = require('@/global/global');\n// 全局ajax\nglobal.ajax = axios;\nexport default {\n  install: function install(Vue, options) {\n    // 需要挂载的都放在这里\n    Vue.prototype.$ajax = axios;\n    Vue.prototype.$api = api;\n    Vue.prototype.$const = consts;\n    Vue.prototype.$dayjs = dayjs;\n    Vue.prototype.$havePermission = function (routeName) {\n      return store.state.common.permissionList.some(function (permission) {\n        return permission.code.toUpperCase() === routeName.toUpperCase();\n      });\n    };\n    Object.keys(global).map(function (key) {\n      Vue.prototype[key] = global[key];\n    });\n  }\n};", {"version": 3, "names": ["axios", "api", "consts", "dayjs", "store", "global", "require", "ajax", "install", "<PERSON><PERSON>", "options", "prototype", "$ajax", "$api", "$const", "$dayjs", "$havePermission", "routeName", "state", "common", "permissionList", "some", "permission", "code", "toUpperCase", "Object", "keys", "map", "key"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/inject.js"], "sourcesContent": ["import axios from './axios';\r\nimport api from './api';\r\nimport consts from './const';\r\nimport dayjs from 'dayjs';\r\nimport store from './store';\r\n// import global from '../global/global'\r\nconst global = require('@/global/global');\r\n// 全局ajax\r\nglobal.ajax = axios;\r\n\r\nexport default {\r\n  install: (Vue, options) => {\r\n    // 需要挂载的都放在这里\r\n    Vue.prototype.$ajax = axios;\r\n    Vue.prototype.$api = api;\r\n    Vue.prototype.$const = consts;\r\n    Vue.prototype.$dayjs = dayjs;\r\n    Vue.prototype.$havePermission = function (routeName) {\r\n      return store.state.common.permissionList.some(permission => {\r\n        return permission.code.toUpperCase() === routeName.toUpperCase();\r\n      });\r\n    };\r\n    Object.keys(global).map(key => {\r\n      Vue.prototype[key] = global[key];\r\n    });\r\n  }\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK;AACZ,OAAOC,GAAG;AACV,OAAOC,MAAM;AACb,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK;AACZ;AACA,IAAMC,MAAM,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACzC;AACAD,MAAM,CAACE,IAAI,GAAGP,KAAK;AAEnB,eAAe;EACbQ,OAAO,EAAE,SAATA,OAAOA,CAAGC,GAAG,EAAEC,OAAO,EAAK;IACzB;IACAD,GAAG,CAACE,SAAS,CAACC,KAAK,GAAGZ,KAAK;IAC3BS,GAAG,CAACE,SAAS,CAACE,IAAI,GAAGZ,GAAG;IACxBQ,GAAG,CAACE,SAAS,CAACG,MAAM,GAAGZ,MAAM;IAC7BO,GAAG,CAACE,SAAS,CAACI,MAAM,GAAGZ,KAAK;IAC5BM,GAAG,CAACE,SAAS,CAACK,eAAe,GAAG,UAAUC,SAAS,EAAE;MACnD,OAAOb,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,cAAc,CAACC,IAAI,CAAC,UAAAC,UAAU,EAAI;QAC1D,OAAOA,UAAU,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAKP,SAAS,CAACO,WAAW,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC;IACDC,MAAM,CAACC,IAAI,CAACrB,MAAM,CAAC,CAACsB,GAAG,CAAC,UAAAC,GAAG,EAAI;MAC7BnB,GAAG,CAACE,SAAS,CAACiB,GAAG,CAAC,GAAGvB,MAAM,CAACuB,GAAG,CAAC;IAClC,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}
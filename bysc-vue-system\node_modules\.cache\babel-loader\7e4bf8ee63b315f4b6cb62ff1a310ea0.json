{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\lazyLoad.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\lazyLoad.js", "mtime": 1745205562785}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["// {/* <img v-LazyLoad=\"xxx.jpg\" /> */}\nvar LazyLoad = {\n  // install方法\n  install: function install(Vue, options) {\n    var defaultSrc = options.default;\n    Vue.directive('lazy', {\n      bind: function bind(el, binding) {\n        LazyLoad.init(el, binding.value, defaultSrc);\n      },\n      inserted: function inserted(el) {\n        if (IntersectionObserver) {\n          LazyLoad.observe(el);\n        } else {\n          LazyLoad.listenerScroll(el);\n        }\n      }\n    });\n  },\n  // 初始化\n  init: function init(el, val, def) {\n    el.setAttribute('data-src', val);\n    el.setAttribute('src', def);\n  },\n  // 利用IntersectionObserver监听el\n  observe: function observe(el) {\n    var io = new IntersectionObserver(function (entries) {\n      var realSrc = el.dataset.src;\n      if (entries[0].isIntersecting) {\n        if (realSrc) {\n          el.src = realSrc;\n          el.removeAttribute('data-src');\n        }\n      }\n    });\n    io.observe(el);\n  },\n  // 监听scroll事件\n  listenerScroll: function listenerScroll(el) {\n    var handler = LazyLoad.throttle(LazyLoad.load, 300);\n    LazyLoad.load(el);\n    window.addEventListener('scroll', function () {\n      handler(el);\n    });\n  },\n  // 加载真实图片\n  load: function load(el) {\n    var windowHeight = document.documentElement.clientHeight;\n    var elTop = el.getBoundingClientRect().top;\n    var elBtm = el.getBoundingClientRect().bottom;\n    var realSrc = el.dataset.src;\n    if (elTop - windowHeight < 0 && elBtm > 0) {\n      if (realSrc) {\n        el.src = realSrc;\n        el.removeAttribute('data-src');\n      }\n    }\n  },\n  // 节流\n  throttle: function throttle(fn, delay) {\n    var timer;\n    var prevTime;\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var currTime = Date.now();\n      var context = this;\n      if (!prevTime) {\n        prevTime = currTime;\n      }\n      clearTimeout(timer);\n      if (currTime - prevTime > delay) {\n        prevTime = currTime;\n        fn.apply(context, args);\n        clearTimeout(timer);\n        return;\n      }\n      timer = setTimeout(function () {\n        prevTime = Date.now();\n        timer = null;\n        fn.apply(context, args);\n      }, delay);\n    };\n  }\n};\nexport default LazyLoad;", {"version": 3, "names": ["LazyLoad", "install", "<PERSON><PERSON>", "options", "defaultSrc", "default", "directive", "bind", "el", "binding", "init", "value", "inserted", "IntersectionObserver", "observe", "listenerScroll", "val", "def", "setAttribute", "io", "entries", "realSrc", "dataset", "src", "isIntersecting", "removeAttribute", "handler", "throttle", "load", "window", "addEventListener", "windowHeight", "document", "documentElement", "clientHeight", "elTop", "getBoundingClientRect", "top", "elBtm", "bottom", "fn", "delay", "timer", "prevTime", "_len", "arguments", "length", "args", "Array", "_key", "currTime", "Date", "now", "context", "clearTimeout", "apply", "setTimeout"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/lazyLoad.js"], "sourcesContent": ["// {/* <img v-LazyLoad=\"xxx.jpg\" /> */}\r\nconst LazyLoad = {\r\n  // install方法\r\n  install(Vue, options) {\r\n    const defaultSrc = options.default;\r\n    Vue.directive('lazy', {\r\n      bind(el, binding) {\r\n        LazyLoad.init(el, binding.value, defaultSrc);\r\n      },\r\n      inserted(el) {\r\n        if (IntersectionObserver) {\r\n          LazyLoad.observe(el);\r\n        } else {\r\n          LazyLoad.listenerScroll(el);\r\n        }\r\n      },\r\n    });\r\n  },\r\n  // 初始化\r\n  init(el, val, def) {\r\n    el.setAttribute('data-src', val);\r\n    el.setAttribute('src', def);\r\n  },\r\n  // 利用IntersectionObserver监听el\r\n  observe(el) {\r\n    var io = new IntersectionObserver(entries => {\r\n      const realSrc = el.dataset.src;\r\n      if (entries[0].isIntersecting) {\r\n        if (realSrc) {\r\n          el.src = realSrc;\r\n          el.removeAttribute('data-src');\r\n        }\r\n      }\r\n    });\r\n    io.observe(el);\r\n  },\r\n  // 监听scroll事件\r\n  listenerScroll(el) {\r\n    const handler = LazyLoad.throttle(LazyLoad.load, 300);\r\n    LazyLoad.load(el);\r\n    window.addEventListener('scroll', () => {\r\n      handler(el);\r\n    });\r\n  },\r\n  // 加载真实图片\r\n  load(el) {\r\n    const windowHeight = document.documentElement.clientHeight;\r\n    const elTop = el.getBoundingClientRect().top;\r\n    const elBtm = el.getBoundingClientRect().bottom;\r\n    const realSrc = el.dataset.src;\r\n    if (elTop - windowHeight < 0 && elBtm > 0) {\r\n      if (realSrc) {\r\n        el.src = realSrc;\r\n        el.removeAttribute('data-src');\r\n      }\r\n    }\r\n  },\r\n  // 节流\r\n  throttle(fn, delay) {\r\n    let timer;\r\n    let prevTime;\r\n    return function (...args) {\r\n      const currTime = Date.now();\r\n      const context = this;\r\n      if (!prevTime) {\r\n        prevTime = currTime;\r\n      }\r\n      clearTimeout(timer);\r\n\r\n      if (currTime - prevTime > delay) {\r\n        prevTime = currTime;\r\n        fn.apply(context, args);\r\n        clearTimeout(timer);\r\n        return;\r\n      }\r\n\r\n      timer = setTimeout(function () {\r\n        prevTime = Date.now();\r\n        timer = null;\r\n        fn.apply(context, args);\r\n      }, delay);\r\n    };\r\n  },\r\n};\r\n\r\nexport default LazyLoad;"], "mappings": "AAAA;AACA,IAAMA,QAAQ,GAAG;EACf;EACAC,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAEC,OAAO,EAAE;IACpB,IAAMC,UAAU,GAAGD,OAAO,CAACE,OAAO;IAClCH,GAAG,CAACI,SAAS,CAAC,MAAM,EAAE;MACpBC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAE;QAChBT,QAAQ,CAACU,IAAI,CAACF,EAAE,EAAEC,OAAO,CAACE,KAAK,EAAEP,UAAU,CAAC;MAC9C,CAAC;MACDQ,QAAQ,WAARA,QAAQA,CAACJ,EAAE,EAAE;QACX,IAAIK,oBAAoB,EAAE;UACxBb,QAAQ,CAACc,OAAO,CAACN,EAAE,CAAC;QACtB,CAAC,MAAM;UACLR,QAAQ,CAACe,cAAc,CAACP,EAAE,CAAC;QAC7B;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAE,IAAI,WAAJA,IAAIA,CAACF,EAAE,EAAEQ,GAAG,EAAEC,GAAG,EAAE;IACjBT,EAAE,CAACU,YAAY,CAAC,UAAU,EAAEF,GAAG,CAAC;IAChCR,EAAE,CAACU,YAAY,CAAC,KAAK,EAAED,GAAG,CAAC;EAC7B,CAAC;EACD;EACAH,OAAO,WAAPA,OAAOA,CAACN,EAAE,EAAE;IACV,IAAIW,EAAE,GAAG,IAAIN,oBAAoB,CAAC,UAAAO,OAAO,EAAI;MAC3C,IAAMC,OAAO,GAAGb,EAAE,CAACc,OAAO,CAACC,GAAG;MAC9B,IAAIH,OAAO,CAAC,CAAC,CAAC,CAACI,cAAc,EAAE;QAC7B,IAAIH,OAAO,EAAE;UACXb,EAAE,CAACe,GAAG,GAAGF,OAAO;UAChBb,EAAE,CAACiB,eAAe,CAAC,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CAAC;IACFN,EAAE,CAACL,OAAO,CAACN,EAAE,CAAC;EAChB,CAAC;EACD;EACAO,cAAc,WAAdA,cAAcA,CAACP,EAAE,EAAE;IACjB,IAAMkB,OAAO,GAAG1B,QAAQ,CAAC2B,QAAQ,CAAC3B,QAAQ,CAAC4B,IAAI,EAAE,GAAG,CAAC;IACrD5B,QAAQ,CAAC4B,IAAI,CAACpB,EAAE,CAAC;IACjBqB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,YAAM;MACtCJ,OAAO,CAAClB,EAAE,CAAC;IACb,CAAC,CAAC;EACJ,CAAC;EACD;EACAoB,IAAI,WAAJA,IAAIA,CAACpB,EAAE,EAAE;IACP,IAAMuB,YAAY,GAAGC,QAAQ,CAACC,eAAe,CAACC,YAAY;IAC1D,IAAMC,KAAK,GAAG3B,EAAE,CAAC4B,qBAAqB,CAAC,CAAC,CAACC,GAAG;IAC5C,IAAMC,KAAK,GAAG9B,EAAE,CAAC4B,qBAAqB,CAAC,CAAC,CAACG,MAAM;IAC/C,IAAMlB,OAAO,GAAGb,EAAE,CAACc,OAAO,CAACC,GAAG;IAC9B,IAAIY,KAAK,GAAGJ,YAAY,GAAG,CAAC,IAAIO,KAAK,GAAG,CAAC,EAAE;MACzC,IAAIjB,OAAO,EAAE;QACXb,EAAE,CAACe,GAAG,GAAGF,OAAO;QAChBb,EAAE,CAACiB,eAAe,CAAC,UAAU,CAAC;MAChC;IACF;EACF,CAAC;EACD;EACAE,QAAQ,WAARA,QAAQA,CAACa,EAAE,EAAEC,KAAK,EAAE;IAClB,IAAIC,KAAK;IACT,IAAIC,QAAQ;IACZ,OAAO,YAAmB;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACtB,IAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3B,IAAMC,OAAO,GAAG,IAAI;MACpB,IAAI,CAACV,QAAQ,EAAE;QACbA,QAAQ,GAAGO,QAAQ;MACrB;MACAI,YAAY,CAACZ,KAAK,CAAC;MAEnB,IAAIQ,QAAQ,GAAGP,QAAQ,GAAGF,KAAK,EAAE;QAC/BE,QAAQ,GAAGO,QAAQ;QACnBV,EAAE,CAACe,KAAK,CAACF,OAAO,EAAEN,IAAI,CAAC;QACvBO,YAAY,CAACZ,KAAK,CAAC;QACnB;MACF;MAEAA,KAAK,GAAGc,UAAU,CAAC,YAAY;QAC7Bb,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBV,KAAK,GAAG,IAAI;QACZF,EAAE,CAACe,KAAK,CAACF,OAAO,EAAEN,IAAI,CAAC;MACzB,CAAC,EAAEN,KAAK,CAAC;IACX,CAAC;EACH;AACF,CAAC;AAED,eAAezC,QAAQ", "ignoreList": []}]}
<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-12-09 09:29:22
 * @FilePath: \bycloud-vue\src\bysc_system\views\user\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <!-- <el-col :span="6">
        <common-tree
          @treeNode="getClickTreeNode"
          :tree-props="treeProps"
          :tree-data="treedata"
        ></common-tree>
      </el-col> -->
      <el-col :span="24">
        <Grid
          api="systems/userPage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="用户名">
                <el-input
                  v-model.trim="searchForm.account"
                  size="small"
                  placeholder="请输入用户名"
                ></el-input>
              </el-form-item>

              <el-form-item label="姓名">
                <el-input
                  v-model.trim="searchForm.realName"
                  size="small"
                  placeholder="请输入姓名"
                ></el-input>
              </el-form-item>

              <el-form-item label="手机号">
                <el-input
                  v-model.trim="searchForm.mobile"
                  size="small"
                  placeholder="请输入手机号"
                ></el-input>
              </el-form-item>

              <el-form-item label="员工编号">
                <el-input
                  v-model.trim="searchForm.jobNo"
                  size="small"
                  placeholder="请输入员工编号"
                ></el-input>
              </el-form-item>

              <el-form-item label="证件ID">
                <el-input
                  v-model.trim="searchForm.certificateId"
                  size="small"
                  placeholder="请输入证件ID"
                ></el-input>
              </el-form-item>

              <el-form-item label="状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in statusOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="公司">
                <el-input
                  v-model.trim="searchForm.companyName"
                  size="small"
                  placeholder="请输入公司"
                ></el-input>
              </el-form-item>

              <el-form-item label="工作部门">
                <el-input
                  v-model.trim="searchForm.workDeptName"
                  size="small"
                  placeholder="请输入工作部门"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button
              v-permission="'user_add'"
              size="small"
              type="primary"
              @click="handleAdd"
              >添加</el-button
            >
            <el-button
              v-permission="'user_batch_role'"
              size="small"
              type="primary"
              @click="handleBatchRole"
              >批量分配角色</el-button
            >
            <el-button
              v-permission="'user_batch_unlock'"
              size="small"
              type="primary"
              @click="handleBatchUnlock"
              >批量解锁</el-button
            >

            <el-button
              v-permission="'user_import'"
              size="small"
              type="primary"
              @click="handleBatchImport"
              >模版导入</el-button
            >


            <el-button
              v-permission="'user_download'"
              size="small"
              type="primary"
              @click="handleTemplateDownload"
              >模版下载</el-button
            >
            <el-button
              v-permission="'user_download'"
              size="small"
              type="primary"
              @click="handleFsSyncUser"
              >飞书同步</el-button
            >
          </div>
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="tableData"
            ref="table"
            :row-key="getRowKeys"
            @selection-change="changeSelect"
            stripe
            style="width: 100%"
          >
            <el-table-column
              fixed="left"
              type="selection"
              :reserve-selection="true"
              width="55"
            >
            </el-table-column>
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot == 'gender'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">{{
                  scope.row[item.slot] === "1" ? "男" : scope.row[item.slot] === "0" ? "女" : ""
                }}</template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot == 'status'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row[item.slot] == '0'">禁用</span>
                  <span v-if="scope.row[item.slot] == '1'">正常</span>
                  <span v-if="scope.row[item.slot] == '99'">已锁定</span>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <!-- <el-button
                  v-permission="'user_edit'"
                  @click="switchUser(scope.row)"
                  type="text"
                  size="small"
                  >切换用户</el-button
                > -->

                <el-button
                  v-permission="'user_resetPwd'"
                  @click="handleResetPwd('resetPwd', scope.row)"
                  type="text"
                  size="small"
                  >重置密码</el-button
                >
                <el-button
                  v-permission="'user_edit'"
                  @click="handleRdit(scope.row)"
                  type="text"
                  size="small"
                  style="margin-right: 6px"
                  >编辑</el-button
                >

                <el-popconfirm
                  @confirm="handleDelete(scope.row.id)"
                  title="您确定要删除该用户吗？"
                >
                  <el-button
                    v-permission="'user_del'"
                    type="text"
                    size="small"
                    slot="reference"
                    style="margin-right: 6px"
                    >删除</el-button
                  >
                </el-popconfirm>

                <el-button
                 v-if="scope.row.status == '99'"
                  v-permission="'user_unlock'"
                  @click="handleUnlock(scope.row.id)"
                  type="text"
                  size="small"
                  >解锁</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>

    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
    >
      <div style="width: 100%; padding: 0 10px; min-height: 700px;  overflow-y: auto;">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="用户名" prop="account">
            <el-input
              size="small"

              v-model.trim="ruleForm.account"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="realName">
            <el-input
              size="small"

              v-model.trim="ruleForm.realName"
            ></el-input>
          </el-form-item>

          <el-form-item label="手机号" prop="mobile">
            <el-input
              size="small"

              v-model.trim="ruleForm.mobile"
            ></el-input>
          </el-form-item>
          <el-form-item label="员工编号" prop="jobNo">
            <el-input
              size="small"
              v-model.trim="ruleForm.jobNo"
            ></el-input>
          </el-form-item>

          <el-form-item label="证件id" prop="certificateId">
            <el-input
              size="small"
              disabled
              v-model.trim="ruleForm.certificateId"
            ></el-input>
          </el-form-item>
          <!-- :disabled="ruleForm.isCompanyId" -->
          <el-form-item label="公司" prop="companyId">
            <el-cascader
              v-model="ruleForm.companyId"
              :options="organizationtreedata"
              :props="{
                value: 'id',
                label: 'organizationName',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
              }"

              clearable
              style="width: 100%"
              size="small"
              placeholder="请选择公司"
              @change="
                (val) => {
                  if (val) {
                    const findNode = (nodes, id) => {
                      for (let node of nodes) {
                        if (node.id === id) {
                          return node;
                        }
                        if (node.children) {
                          const found = findNode(node.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };
                    const node = findNode(organizationtreedata, val);
                    ruleForm.organizationName = node.organizationName;
                  } else {
                    ruleForm.organizationName = '';
                  }
                }
              "
            >
            </el-cascader>
          </el-form-item>
          <!-- :disabled="ruleForm.isBelongDeptId" -->
          <el-form-item label="所属部门" prop="belongDeptId">
            <el-cascader
              v-model="ruleForm.belongDeptId"
              :options="organizationtreedata"
              :props="{
                value: 'id',
                label: 'organizationName',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
              }"

              clearable
              style="width: 100%"
              size="small"
              placeholder="请选择公司"
              @change="
                (val) => {
                  if (val) {
                    const findNode = (nodes, id) => {
                      for (let node of nodes) {
                        if (node.id === id) {
                          return node;
                        }
                        if (node.children) {
                          const found = findNode(node.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };
                    const node = findNode(organizationtreedata, val);
                    ruleForm.organizationName = node.organizationName;
                  } else {
                    ruleForm.organizationName = '';
                  }
                }
              "
            >
            </el-cascader>
          </el-form-item>
          <!-- :disabled="ruleForm.isWorkDeptId" -->
          <el-form-item label="工作部门" prop="workDeptId">
            <el-cascader
              v-model="ruleForm.workDeptId"
              :options="organizationtreedata"
              :props="{
                value: 'id',
                label: 'organizationName',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
              }"

              clearable
              style="width: 100%"
              size="small"
              placeholder="请选择公司"
              @change="
                (val) => {
                  if (val) {
                    const findNode = (nodes, id) => {
                      for (let node of nodes) {
                        if (node.id === id) {
                          return node;
                        }
                        if (node.children) {
                          const found = findNode(node.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };
                    const node = findNode(organizationtreedata, val);
                    ruleForm.organizationName = node.organizationName;
                  } else {
                    ruleForm.organizationName = '';
                  }
                }
              "
            >
            </el-cascader>
          </el-form-item>
          <!-- :disabled="drawerName === '编辑' && (!ruleForm.roleTypes || !ruleForm.roleTypes.includes('1'))" -->
          <el-form-item label="角色" prop="roleId">
            <el-select
              style="width: 250px"
              size="small"
              multiple
              filterable
              v-model.trim="ruleForm.roleIds"
              placeholder="请选择角色"
             :disabled="drawerName === '编辑'"
            >
              <el-option
                v-for="(item, index) in roleLists"
                :key="index"
                :label="item.roleName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="性别">
            <el-radio-group
              v-model="ruleForm.gender"

            >
              <el-radio :label="'1'">男</el-radio>
              <el-radio :label="'0'">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model.trim="status"
            ></el-switch>
          </el-form-item>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer">取消</el-button>
          <el-button
            size="small"
            type="primary"
            :loading="okLoading"
            @click="submitForm('ruleForm')"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <!-- <el-dialog title="组织" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false">
      <div>
        <common-tree
          @treeNode="getSelectTreeNode"
          :tree-props="treeProps"
          :highlightcurrent="true"
          :tree-data="organizationtreedata"
        ></common-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="setParentInfo">确 定</el-button>
      </span>
    </el-dialog> -->
    <el-drawer
      size="30%"
      title="重置密码"
      :visible.sync="resetDialog"
      :direction="direction"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
    >
      <div style="width: 100%; padding: 0 20px">
        <el-form
          :model="formInline"
          :rules="resetPwdRules"
          ref="resetPwdForm"
          label-width="100px"
          class="demo-form-inline"
        >
          <el-form-item label="新密码" prop="password">
            <el-input
              v-model="formInline.password"
              type="password"
              style="width: 100%"
              placeholder="请输入新密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formInline.confirmPassword"
              type="password"
              style="width: 100%"
              placeholder="请再次输入新密码"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="resetDialog = false">取 消</el-button>
          <el-button
            size="small"
            type="primary"
            @click="resetNewPwd"
          >确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      title="批量分配角色"
      :visible.sync="batchRoleVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-checkbox-group v-model="userRoleList">
        <template v-for="item in roleLists">
          <el-checkbox
            :key="item.id"
            :label="item.id"
            style="margin-top: 5px"
            >{{ item.roleName }}</el-checkbox
          >
          <br :key="item.id + 'br'" />
        </template>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelBatchRole">取消</el-button>
        <el-button
          type="primary"
          @click="submitBatchRole"
          :loading="batchRoleOkLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>


    <bw-import
      :action="importUrl"
      :on-success="successInit"
      :on-error="errorInit"
      :before-upload="handleBeforeUpload"
      accept=".xlsx"
      ref="importNode"
    ></bw-import>

  </div>
</template>

<script>
import Vue from "vue";
import Grid from "@/components/Grid";
import {mapActions} from "vuex";
import _ from "lodash";
import BwImport from "@/components/BwImport";
import commonTree from "@/components/treeComp/commonTree";
import iconChoose from "@/components/choose/icon-choose";
const defaultSearchForm = {};
const defaultForm = {
  account: "",
  areas: [],
  avatar: "",
  email: "",
  gender: "1",
  mobile: "",
  nickname: "",
  password: "",
  realName: "",
  roleId: "",
  roleIds: [],
  status: '1',
};
export default {
  components: {commonTree, Grid, iconChoose, BwImport},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    // 定义密码确认校验规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.formInline.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };

    this.searchEventBus = new Vue();
    return {

      importUrl: '/api/icb/personUser/importPerson',
      lastLoginTimes: [],
      statusOptions: [
        {label: "正常", value: 1},
        {label: "禁用", value: 0},
        {label: "已锁定", value: 99},
      ],
      batchRoleOkLoading: false,
      userRoleList: [],
      batchRoleVisible: false,
      tableSelection: [],
      formInline: {
        password: null,
        confirmPassword: null
      },
      resetPwdRules: {
        password: [
          {required: true, message: '请输入新密码', trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '请再次输入新密码', trigger: 'blur'},
          {validator: validateConfirmPassword, trigger: 'blur'}
        ]
      },
      resetDialog: false,
      resetPwdId: "",
      dialogVisible: false,
      status: true,
      okLoading: false,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        account: [
          {required: true, message: "请输入用户名", trigger: "blur"},
          {
            min: 3,
            max: 15,
            message: "长度在 3 到 16 个字符",
            trigger: "blur",
          },
          {
            pattern: /^[a-z0-9_\-!]+$/,
            message: "只能包含小写字母、数字、下划线_、连字符-、感叹号!",
            trigger: "blur"
          }
        ],
        realName: [
          {required: true, message: "请输入姓名", trigger: "blur"},
          {min: 2, max: 16, message: "长度在 2 到 16 个字符", trigger: "blur"}
        ],
        nickName: [{required: true, message: "请输入姓名", trigger: "blur"}],
        mobile: [
          {required: true, message: "请输入手机号", trigger: "blur"},
          {pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号格式", trigger: "blur"}
        ],
        organizationId: [
          {required: true, message: "请选择组织", trigger: "change"},
        ],
        companyId: [
          {required: true, message: "请选择公司", trigger: "change"},
        ],
        belongDeptId: [
          {required: true, message: "请选择所属部门", trigger: "change"},
        ],
        workDeptId: [
          {required: true, message: "请选择工作部门", trigger: "change"},
        ],
      },
      drawerName: "添加",
      drawer: false,
      direction: "rtl",
      searchForm: _.cloneDeep(defaultSearchForm),
      treeProps: {
        children: "children",
        label: "organizationName",
      },
      columns: [
        {
          title: "用户名",
          key: "account",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "姓名",
          key: "realName",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "手机号",
          key: "mobile",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: "员工编号",
          key: "jobNo",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "证件ID",
          key: "certificateId",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "公司",
          key: "companyName",
          tooltip: true,
          minWidth: 130,
        },

        {
          title: "所属部门",
          key: "belongDeptName",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "工作部门",
          key: "workDeptName",
          minWidth: 150,
          tooltip: true,
        },

        {
          title: "性别",
          slot: "gender",
          tooltip: true,
          minWidth: 170,
        },
        {
          title: "状态",
          slot: "status",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "最后登录时间",
          key: "lastLoginTime",
          tooltip: true,
          minWidth: 150,
        },
      ],
      treedata: [],
      organizationtreedata: [],
      resourceName: "",
      tableData: [],
      roleLists: [],
      selectedResouce: {},
      sysconfig: {},
    };
  },
  watch: {
    status(val) {
      this.ruleForm.status = val ? 1 : 0;
    },
  },
  mounted() {
    this.getSysDetail();
    this.getRoleLists();
    this.getTrees();
  },

  methods: {
    ...mapActions(["download"]),
    handleFsSyncUser() {
      this.$confirm('确定要同步飞书用户数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api["systems/fs-sync-user"]().then(data => {
          this.$message.success("飞书同步成功");
        });
      });
    },
    handleTemplateDownload() {
      let url = "/api/icb//personUser/templateDownload";
      let downData = {
        url: url,
        downLoad: "用户导入模板.xlsx",
      };
      this.download(downData); // 下载
    },
    handleBatchImport() {
      this.$refs.importNode.$refs.fileUpload.$refs['upload-inner'].handleClick();
    },

    errorInit(response, file, fileList) {
      this.$message.error("批量导入出错");
    },
    handleBeforeUpload() {
      return true;
    },

    successInit(response, file, fileList) {
      console.log('response, file, fileList', response, file, fileList);
      this.$nextTick(() => {
        this.$message.success("批量导入成功");
        this.$refs.grid.query();
      });
    },
    getSysDetail() {
      this.$api["config/baseSystemSetting-get"]({id: 1}).then(data => {
        this.sysconfig = data;
      });
    },
    resetNewPwd() {
      this.$refs.resetPwdForm.validate(valid => {
        if (valid) {
          // 验证两次密码是否一致
          if (this.formInline.password !== this.formInline.confirmPassword) {
            this.$message.error('两次输入的密码不一致');
            return false;
          }

          let pwdLength = "{" + this.sysconfig.minlength + ",16}";
          if (this.sysconfig.complexDictId == 1) {
            let vali = new RegExp(
              "^(?=.*[0-9])(?=.*[a-zA-Z])." + pwdLength + "$",
              ""
            );
            if (!vali.test(this.formInline.password)) {
              this.$message.error(
                "密码请输入数字加字母，长度为" + this.sysconfig.minlength + "-16"
              );
              return false;
            }
          } else if (this.sysconfig.complexDictId == 2) {
            let vali = new RegExp(
              "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])." + pwdLength + "$",
              ""
            );
            if (!vali.test(this.formInline.password)) {
              this.$message.error(
                "密码请输入数字加大小写字母，长度为"
                  + this.sysconfig.minlength
                  + "-16"
              );
              return false;
            }
          } else {
            let vali = new RegExp(
              "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@~^%&'.,;=?$])."
                + pwdLength
                + "$",
              ""
            );
            if (!vali.test(this.formInline.password)) {
              this.$message.error(
                "密码请输入数字加大小写字母加特殊字符，长度为"
                  + this.sysconfig.minlength
                  + "-16，支持的特殊字符范围：@~^%&'.,;=?$"
              );
              return false;
            }
          }
          this.$api["systems/changePwd"]({
            id: this.resetPwdId,
            password: this.formInline.password,
          }).then(data => {
            this.$message.success("重置成功");
            this.resetDialog = false;
          });
        }
      });
    },
    handleResetPwd(e, r) {
      this.resetPwdId = r.id;
      if (e === "resetPwd") {
        this.formInline = {
          password: "",
          confirmPassword: ""
        };
        this.resetDialog = true;
      }
    },
    handleAdd() {
      this.drawer = true;
      this.drawerName = "添加";
      this.ruleForm = _.cloneDeep(defaultForm);
    },
    switchUser(e) {
      let refreshToken = this.$cookies.get("refreshToken");
      this.$api["account/switchUser"]({
        targetUserId: e.id,
        refreshToken: refreshToken,
      }).then(data => {
        this.$api["account/getToken"]({
          switch_key: data,
          grant_type: "switch_user",
        }).then(res => {
          this.$localCache.setLocal("userToken", res.token);
          this.$localCache.setLocal("refreshToken", res.refreshToken);
          this.$localCache.setLocal("userExpir", res.exp);
          location.reload();
        });
      });
    },
    handleRdit(data) {

      console.log("data", data);
      data.isCompanyId = !!data.companyId;
      data.isBelongDeptId = !!data.belongDeptId;
      data.isWorkDeptId = !!data.workDeptId;
      this.ruleForm = JSON.parse(JSON.stringify(data));


      this.status = !!data.status;
      this.drawer = true;
      this.drawerName = "编辑";
    },
    getRoleName(arr, param) {
      let name = "";
      arr.forEach(e => {
        if (e.children) {
          if (e.id == param) {
            name = e.resourceName;
          }
          this.getParentName(e.children, param);
        } else {
          if (e.id == param) {
            name = e.resourceName;
          }
        }
      });
      return name;
    },
    handleDateChange(e) {
      this.searchForm.lastLoginStartTime = e[0];
      this.searchForm.lastLoginEndTime = e[1];
    },
    handleDelete(e) {
      this.$api["systems/userDelete"]({id: e}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: "删除成功",
          type: "success",
        });
      });
    },
    getIcon(e) {
      this.ruleForm.resourceIcon = e;
    },
    setParentInfo() {
      this.ruleForm.organizationId = this.selectedResouce.id;
      this.ruleForm.organizationName = this.selectedResouce.organizationName;
      this.$refs.grid.query();
      this.dialogVisible = false;
    },
    getSelectTreeNode(e) {
      this.selectedResouce = e;
    },
    submitForm(formName) {
      let that = this;
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.ruleForm.status = this.status ? 1 : 0;
          this.$api["systems/addUser"]({...this.ruleForm})
            .then(data => {
              this.drawer = false;
              this.okLoading = false;
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$nextTick(() => {
                this.$refs.grid.query();
              });
            })
            .catch(() => {
              that.okLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.ruleForm = _.cloneDeep(defaultForm);
      this.$refs[formName].resetFields();
    },
    closeDrawer() {
      this.refs = _.cloneDeep(defaultForm);
      this.$refs.ruleForm.resetFields();
      this.drawer = false;
    },
    addTable() {},
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.lastLoginTimes = [];
      this.searchForm.parentId = null;
      this.$refs.table.clearSelection();
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
      // let ids = e.map(item => item.id);
      // let p1 = this.$api["systems/find-user-module"]({ids});
      // let p2 = this.$api["systems/find-user-now-module"]({ids});
      // let p3 = this.$api["systems/find-user-dept"]({ids});
      // let p4 = this.$api["systems/find-zhi-yuan-person"]({ids});
      // Promise.all([p1, p2, p3, p4]).then(res => {
      //   this.tableData = e.map(item => {
      //     // console.log('res[3][item.state] ', res[3][item]);
      //     let _e = res[3].filter(initem => {
      //       if (initem.sysUserId == item.id) {
      //         return initem.state;
      //       }
      //     });
      //     let state = _e[0]?_e[0].state : '';
      //     let corporateIdentity = _e[0]?_e[0].corporateIdentity : '';
      //     return {
      //       ...item,
      //       moduleNames: res[0][item.id] || '',
      //       nowModuleNames: res[1][item.id] || '',
      //       preOrganizationName: res[2][item.id] || '',
      //       state,
      //       corporateIdentity
      //     };
      //   });
      // });
    },
    getClickTreeNode(e) {
      this.searchForm.organizationId
        = this.searchForm.organizationId == e.id ? null : e.id;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getRoleLists() {
      this.$api["systems/roleList"]().then(data => {
        this.roleLists = data;
      });
    },
    clearParent() {
      this.ruleForm.organizationId = null;
      this.ruleForm.organizationName = null;
    },
    getTrees() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.treedata = data;
        this.organizationtreedata = data;
      });
    },

    getRowKeys(row) {
      return row.id;
    },
    changeSelect(selection) {
      this.tableSelection = selection;
    },
    handleBatchUnlock() {
      if (
        this.$refs.table.selection
        && this.$refs.table.selection.length == 0
      ) {
        this.$message({
          type: "info",
          message: "请选择至少一项",
        });
        return;
      }
      let ids = [];
      this.$refs.table.selection.forEach(e => {
        ids.push(e.id);
      });
      this.$confirm("您确定要批量解锁已选用户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api["systems/unLock"]({ids: ids}).then(() => {
          this.$refs.grid.loading = false;
          this.$refs.grid.query();
          this.$message({
            message: "批量解锁成功",
            type: "success",
          });
        });
      });
    },



    handleUnlock(id) {
      let ids = [id];
      this.$refs.table.selection.forEach(e => {
        ids.push(e.id);
      });
      this.$confirm("您确定要解锁该用户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api["systems/unLock"]({ids: ids}).then(() => {
          this.$refs.grid.loading = false;
          this.$refs.grid.query();
          this.$message({
            message: "解锁成功",
            type: "success",
          });
        });
      });
    },



    handleSyncHronous() {
      this.$confirm("确定人员信息同步？", "人员信息同步提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.grid.loading = true;
          this.$api["systems/deptSync"]()
            .then(() => {
              this.$api["systems/personSync"]().then(() => {
                // eslint-disable-next-line
                this.$api["systems/userSyc"]().then(() => {
                  this.$refs.grid.loading = false;
                  this.$refs.grid.query();
                  this.$message({
                    message: "人员信息同步成功",
                    type: "success",
                  });
                });
              });
            })
            .catch(error => {
              this.$refs.grid.loading = false;
              this.$message({
                message: "人员信息同步失败",
                type: "error",
              });
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消人员信息同步",
          });
        });
    },
    handleBatchRole() {
      if (
        this.$refs.table.selection
        && this.$refs.table.selection.length == 0
      ) {
        this.$message({
          type: "info",
          message: "请选择至少一项",
        });
        return;
      }
      this.userRoleList = [];
      this.batchRoleVisible = true;
    },
    cancelBatchRole() {
      this.userRoleList = [];
      this.batchRoleVisible = false;
    },
    submitBatchRole() {
      let that = this;
      if (this.userRoleList && this.userRoleList.length == 0) {
        this.$message({
          type: "info",
          message: "请选择至少一个角色",
        });
        return;
      }
      this.batchRoleOkLoading = true;
      let userIds = this.tableSelection.reduce((result, row) => {
        result.push(row.id);
        return result;
      }, []);
      this.$api["systems/batch-save-user-role"]({
        userIds: userIds,
        roleIds: this.userRoleList,
      })
        .then(data => {
          this.batchRoleVisible = false;
          this.batchRoleOkLoading = false;
          this.$message({
            type: "success",
            message: "操作成功",
          });
          this.$refs.table.clearSelection();
          this.$nextTick(() => {
            this.$refs.grid.query();
          });
        })
        .catch(() => {
          that.batchRoleOkLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}
</style>

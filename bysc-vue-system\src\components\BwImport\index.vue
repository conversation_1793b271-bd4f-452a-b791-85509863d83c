<template>
    <div class="bs-import">
        <el-upload
            ref="fileUpload"
            name="file"
            class="file-upload"
            :accept="accept"
            :action="action"
            :list-type="listType"
            :data="uploadData"
            :headers="$uploadHeaders.headers"
            :multiple="multiple"
            :limit="limit"
            :disabled="disabled"
            :auto-upload="autoUpload"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-progress="handleProgress"
            :on-exceed="handleOnExceed"
            :show-file-list="showFileList"
            v-bind="$attrs"
        >
        </el-upload>
    </div>
</template>

<script>

function noop() {}
export default {
  name: 'BwImport',
  data() {
    return {
      isImport: false,
      uploadLoading: null,
    };
  },
  computed: {
    uploadData() {
      return {
        ...this.data,
        ...this.extraParams
      };
    }
  },
  props: {
    /** 是否支持多选，默认支持* */
    multiple: {
      type: Boolean,
      default: false
    },
    showFileList: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: '.doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.gif,.bmp,.txt'
    },
    disabled: Boolean,
    /** 文件大小* */
    fileSizeLimit: {
      type: Array,
      default: () => []
    },
    action: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    },
    beforeUpload: Function,
    beforeRemove: Function,
    onRemove: Function,
    onPreview: {
      type: Function
    },
    onSuccess: {
      type: Function,
      default: noop
    },
    onProgress: {
      type: Function,
      default: noop
    },
    onError: {
      type: Function,
      default: noop
    },
    fileList: {
      default() {
        return [];
      }
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    listType: {
      type: String,
      default: 'text' // text,picture,picture-card
    },
    limit: Number,
    onExceed: {
      type: Function,
      default: noop
    },
    extraParams: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
  },
  created() {
  },
  watch: {
    data: {
      handler(newVal) {
      },
      immediate: true
    }
  },
  methods: {
    handleBeforeUpload(file) {
      this.$forceUpdate();

      if (this.beforeUpload) {
        const result = this.beforeUpload(file, this.extraParams);
        if (result === false) {
          return false;
        }
      }

      return this.handleCheck(file);
    },
    handleSuccess(response, file, fileList) {
      /** 上传成功状态码判断* */
      if (response.code === 'S00000') {
        this.onSuccess(response, file, fileList);
      } else {
        this.onError(response, file, fileList);
        this.$message({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: response.msg
        });

      }
      this.uploadLoading.close();
    },
    handleProgress() {
      this.uploadLoading = this.$loading({
        lock: true,
        text: '文件上传中~',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    handleError(err, file, fileList) {
      this.$message.error('上传失败!');
      this.onError(err, file, fileList);
      this.uploadLoading.close();
    },
    // 文件校验
    handleCheck(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1);
      const isLt10M = Number(file.size / 1024 / 1024) > this.fileSizeLimit;
      if (!this.accept.includes('*')) {
        if (!this.accept.toLowerCase().includes(fileSuffix.toLowerCase())) {
          this.$message({
            message: '文件格式错误，不可上传！',
            type: 'warning'
          });
          return false;
        }
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    handleOnExceed() {
      this.$message.warning(`文件超出个数,最大限制个数为${this.limit}!`);
    }
  }
};
</script>

<style scoped lang="less">
.bs-import {
    width: 0 !important;
    height: 0;
}
</style>
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\const.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\const.js", "mtime": 1745205562794}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _classCallCheck from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CONST_DEFAULT_CONFIG } from '@/config';\nimport CONST_CONFIG from '@/service/const';\nvar MakeConst = /*#__PURE__*/function () {\n  function MakeConst(options) {\n    _classCallCheck(this, MakeConst);\n    this.const = {};\n    this.constBuilder(options);\n  }\n  return _createClass(MakeConst, [{\n    key: \"constBuilder\",\n    value: function constBuilder(_ref) {\n      var _this = this;\n      var _ref$sep = _ref.sep,\n        sep = _ref$sep === void 0 ? '/' : _ref$sep,\n        _ref$config = _ref.config,\n        config = _ref$config === void 0 ? [] : _ref$config;\n      Object.keys(config).map(function (namespace) {\n        _this._constSingleBuilder({\n          namespace: namespace,\n          sep: sep,\n          config: config[namespace]\n        });\n      });\n    }\n  }, {\n    key: \"_constSingleBuilder\",\n    value: function _constSingleBuilder(_ref2) {\n      var _this2 = this;\n      var namespace = _ref2.namespace,\n        sep = _ref2.sep,\n        config = _ref2.config;\n      config.forEach(function (cst) {\n        var name = cst.name,\n          value = cst.value;\n        var constName = \"\".concat(namespace.toUpperCase()).concat(sep).concat(name);\n        Object.defineProperty(_this2.const, constName, {\n          value: value\n        });\n      });\n    }\n  }]);\n}();\nexport default new MakeConst(_objectSpread({\n  config: CONST_CONFIG\n}, CONST_DEFAULT_CONFIG)).const;", {"version": 3, "names": ["CONST_DEFAULT_CONFIG", "CONST_CONFIG", "MakeConst", "options", "_classCallCheck", "const", "constBuilder", "_createClass", "key", "value", "_ref", "_this", "_ref$sep", "sep", "_ref$config", "config", "Object", "keys", "map", "namespace", "_constSingleBuilder", "_ref2", "_this2", "for<PERSON>ach", "cst", "name", "constName", "concat", "toUpperCase", "defineProperty", "_objectSpread"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/const.js"], "sourcesContent": ["import {CONST_DEFAULT_CONFIG} from '@/config';\r\nimport CONST_CONFIG from '@/service/const';\r\n\r\nclass MakeConst {\r\n  constructor(options) {\r\n    this.const = {};\r\n    this.constBuilder(options);\r\n  }\r\n\r\n  constBuilder({\r\n    sep = '/',\r\n    config = []\r\n  }) {\r\n    Object.keys(config).map(namespace => {\r\n      this._constSingleBuilder({\r\n        namespace,\r\n        sep,\r\n        config: config[namespace]\r\n      });\r\n    });\r\n  }\r\n\r\n  _constSingleBuilder({\r\n    namespace,\r\n    sep,\r\n    config\r\n  }) {\r\n    config.forEach(cst => {\r\n      const {name, value} = cst;\r\n      const constName = `${namespace.toUpperCase()}${sep}${name}`;\r\n      Object.defineProperty(this.const, constName, {value});\r\n    });\r\n  }\r\n}\r\n\r\nexport default new MakeConst({\r\n  config: CONST_CONFIG,\r\n  ...CONST_DEFAULT_CONFIG\r\n}).const;\r\n"], "mappings": ";;;;;;;;;AAAA,SAAQA,oBAAoB,QAAO,UAAU;AAC7C,OAAOC,YAAY,MAAM,iBAAiB;AAAC,IAErCC,SAAS;EACb,SAAAA,UAAYC,OAAO,EAAE;IAAAC,eAAA,OAAAF,SAAA;IACnB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,YAAY,CAACH,OAAO,CAAC;EAC5B;EAAC,OAAAI,YAAA,CAAAL,SAAA;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAH,YAAYA,CAAAI,IAAA,EAGT;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAF,IAAA,CAFDG,GAAG;QAAHA,GAAG,GAAAD,QAAA,cAAG,GAAG,GAAAA,QAAA;QAAAE,WAAA,GAAAJ,IAAA,CACTK,MAAM;QAANA,MAAM,GAAAD,WAAA,cAAG,EAAE,GAAAA,WAAA;MAEXE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,GAAG,CAAC,UAAAC,SAAS,EAAI;QACnCR,KAAI,CAACS,mBAAmB,CAAC;UACvBD,SAAS,EAATA,SAAS;UACTN,GAAG,EAAHA,GAAG;UACHE,MAAM,EAAEA,MAAM,CAACI,SAAS;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAAW,mBAAmBA,CAAAC,KAAA,EAIhB;MAAA,IAAAC,MAAA;MAAA,IAHDH,SAAS,GAAAE,KAAA,CAATF,SAAS;QACTN,GAAG,GAAAQ,KAAA,CAAHR,GAAG;QACHE,MAAM,GAAAM,KAAA,CAANN,MAAM;MAENA,MAAM,CAACQ,OAAO,CAAC,UAAAC,GAAG,EAAI;QACpB,IAAOC,IAAI,GAAWD,GAAG,CAAlBC,IAAI;UAAEhB,KAAK,GAAIe,GAAG,CAAZf,KAAK;QAClB,IAAMiB,SAAS,MAAAC,MAAA,CAAMR,SAAS,CAACS,WAAW,CAAC,CAAC,EAAAD,MAAA,CAAGd,GAAG,EAAAc,MAAA,CAAGF,IAAI,CAAE;QAC3DT,MAAM,CAACa,cAAc,CAACP,MAAI,CAACjB,KAAK,EAAEqB,SAAS,EAAE;UAACjB,KAAK,EAALA;QAAK,CAAC,CAAC;MACvD,CAAC,CAAC;IACJ;EAAC;AAAA;AAGH,eAAe,IAAIP,SAAS,CAAA4B,aAAA;EAC1Bf,MAAM,EAAEd;AAAY,GACjBD,oBAAoB,CACxB,CAAC,CAACK,KAAK", "ignoreList": []}]}
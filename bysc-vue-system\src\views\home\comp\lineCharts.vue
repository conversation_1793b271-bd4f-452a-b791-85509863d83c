<template>
  <div>
    <div :key="timer" ref="main" :style="{width: width,height:height}"></div>
  </div>
</template>
<script>
import {on, off} from '@/utils/tools';
export default {
  name: 'linecharts',
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '40vh'
    },
    lineData: Object
  },
  data() {
    return {
      timer: '',
      myChart: null
    };
  },
  beforeDestroy() {
    off(window, 'resize', this.resize);
  },
  methods: {
    resize() {
      this.myChart.resize();
    },
    drawChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.main);
      // 指定图表的配置项和数据
      const colors = ['#AFE66B', '#FFB767'];
      let option = {
        title: {
          text: '终端在线状态'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.lineData.x_data
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {}
        //   }
        // },
        xAxis: {
          type: 'category',
          splitLine: {show: false},
          axisLine: {
            show: false,
            lineStyle: {
              color: '#999999'
            }
          },
          axisTick: { // y轴刻度线
            show: false
          },
          boundaryGap: false,
          data: this.lineData.xdata
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisLine: {
            show: false,
            lineStyle: {
              color: '#999999'
            }
          },
          splitLine: {show: false},
          axisTick: { // y轴刻度线
            show: false
          },
        },
        series: [
          {
            name: '在线',
            type: 'line',
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#AFE66B' // 0% 处的颜色
                }, {
                  offset: 1, color: '#ffffff' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            itemStyle: {
              normal: {
                color: colors[0], // 改变折线点的颜色
                lineStyle: {
                  color: colors[0] // 改变折线颜色
                }
              }
            },
            data: this.lineData.ydataOnline
          },
          {
            name: '离线',
            type: 'line',
            stack: 'Total',
            smooth: true,
            // 区域填充样式
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#FFB767' // 0% 处的颜色
                }, {
                  offset: 1, color: '#fff' // 1% 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            itemStyle: {
              normal: {
                color: colors[1], // 改变折线点的颜色
                // 区域填充样式
                lineStyle: {
                  color: colors[1] // 改变折线颜色
                }
              }
            },
            data: this.lineData.ydataOffline
          }
        ]
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
      on(window, 'resize', this.resize);
    }
  },
  mounted() {
    // const that = this;
    // window.onresize = () => {
    //   return (() => {
    //     that.timer = new Date().getTime();
    //     setTimeout(() => {
    //       that.drawChart();
    //     }, 1000);
    //   })();
    // };
    // 加延迟防止图表溢出盒子
    setTimeout(() => {
      this.drawChart();
    }, 10);
  }
};
</script>

// 防抖

export default {

  _debounce(fn, delay) {

    // eslint-disable-next-line no-redeclare
    var delay = delay || 200;

    var timer;

    return function () {

      var th = this;

      // eslint-disable-next-line prefer-rest-params
      var args = arguments;

      if (timer) {

        clearTimeout(timer);

      }

      timer = setTimeout(function () {

        timer = null;

        fn.apply(th, args);

      }, delay);

    };

  },

  // 节流

  _throttle(fn, interval) {

    var last;

    var timer;

    // eslint-disable-next-line no-redeclare
    var interval = interval || 200;

    return function () {

      var th = this;

      // eslint-disable-next-line prefer-rest-params
      var args = arguments;

      var now = +new Date();

      if (last && now - last < interval) {

        clearTimeout(timer);

        timer = setTimeout(function () {

          last = now;

          fn.apply(th, args);

        }, interval);

      } else {

        last = now;

        fn.apply(th, args);

      }

    };

  }

};

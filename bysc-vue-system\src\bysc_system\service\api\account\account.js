export default [
  {
    name: 'login',
    method: 'POST_FORM', // 此类型为自定义提交类型，其实是post form类型
    path: '/ics/account/login',
    desc: '登录接口'
  },
  {
    name: 'getToken',
    method: 'POST_TOKEN',
    path: '/oauth/oauth/token',
    desc: '登录接口'
  },
  {
    name: 'switchUser',
    method: 'POST_FORM',
    path: '/oauth/oauth/switch-user',
  },
  {
    name: 'getUsers',
    method: 'GET',
    path: '/system/account/user/info',
    desc: '用户信息接口'
  },
  {
    name: 'logout',
    method: 'POST_FORM',
    path: '/oauth/oauth/logout',
    desc: '退出接口'
  },
  {
    name: 'update-password-for-expired', // 修改密码
    method: 'POST_FORM',
    path: '/ics/account/update-password-for-expired'
  },
  {
    name: 'getUserInfo',
    method: 'GET',
    path: '/ics/account/principal-get'// ?username=fanqz
  },
  {
    name: 'lock', // 锁定
    method: 'POST_FORM',
    path: '/ics/account/lock'
  },
  {
    name: 'unLock', // 解锁
    method: 'POST_FORM',
    path: '/ics/account/unLock'
  },
  {
    name: 'disabled', // 停用
    method: 'POST_FORM',
    path: '/ics/account/disabled'
  },
  {
    name: 'enable', // 启用
    method: 'POST_FORM',
    path: '/ics/account/enable'
  },
  {
    name: 'getUser',
    method: 'GET',
    path: '/app/account/account-get'
  },
  {
    name: 'findById',
    method: 'GET',
    path: '/app/account/account-find-by-id'
  },
  {
    name: 'getAppPassword',
    method: 'GET',
    path: '/app/account/account-generate-login-code'// 获取临时密码
  },
  {
    name: 'getUserPage',
    method: 'POST',
    path: '/app/account/account-page',
    postForm: true
  },
  {
    name: 'getUserList',
    method: 'POST',
    path: '/app/account/account-list'
  },
  {
    name: 'getPageWithRole',
    method: 'POST',
    path: '/app/account/account-page-with-role'
  },
  {
    name: 'socialcallback',
    method: 'POST',
    path: '/oauth/social/callback'
  },
  {
    name: 'addUser',
    method: 'POST',
    path: '/app/account/enhanced/account-save'
  },
  {
    name: 'deleteUser',
    method: 'POST',
    path: '/app/account/account-delete'
  },
  {
    name: 'updateRole',
    method: 'POST',
    path: '/app/account/account-updateRole'
  },
  {
    name: 'updateRoleList',
    method: 'POST',
    path: '/app/account/account-update-role-list'
  },
  {
    name: 'updateRoleListV2',
    method: 'POST',
    path: '/app/xbw/account/updateRoles'
  },

  {
    name: 'accountAuto',
    method: 'GET',
    path: '/app/account/account-auto'// ?username=fanqz
  },
  // {
  //   name: 'updatePasswordByToken',
  //   method: 'POST',
  //   path: '/app/account/update-password-by-token'
  // },
  {
    name: 'updatePasswordByToken',
    method: 'POST',
    path: '/app/account/enhanced/update-password-by-token'
  },
  {
    name: 'resetPassword',
    method: 'GET',
    path: '/app/account/reset-password'
  },
  {
    name: 'saveUserAreaRel', // 保存账户区域关联关系
    method: 'POST',
    path: '/app/ibuilding/userAreaRel/saveUserAreaRel'
  },
  {
    name: 'myAreaList', // 查询我的区域列表
    method: 'GET',
    path: '/app/ibuilding/userAreaRel/myAreaList'
  },
  {
    name: 'syncDeptUserInfo', // 同步某一部门账户
    method: 'POST',
    path: '/app/ibuilding/syncDeptUserInfo'
  },
  {
    name: 'pageWithFaceImg', // 查询账户分页数据(人脸)
    method: 'POST',
    path: '/app/ibuilding/account/pageWithFaceImg'
  },
  // {
  //   name: 'reset-password', // 重置账户密码
  //   method: 'GET',
  //   path: '/app/account/reset-password'
  // },
  {
    name: 'reset-password', // 重置账户密码
    method: 'GET',
    path: '/app/account/enhanced/reset-password'
  },
  {
    name: 'saveAccountApprove', // 设置审批员
    method: 'POST',
    path: '/ics/account/saveAccountApprove'
  }
];


// 权限接口
[
  {
    name: 'getSysPermission',
    method: 'GET',
    path: '/app/auth/permission-get'
  },
  {
    name: 'getPermissionTree',
    method: 'GET',
    path: '/app/auth/permission-getPermissionTree'
  },
  {
    name: 'getSysPermissionList',
    method: 'POST',
    path: '/app/auth/permission-page'
  },
  {
    name: 'saveSysPermission',
    method: 'POST',
    path: '/app/auth/permission-save'
  },
  {
    name: 'deleteSysPermission',
    method: 'POST',
    path: '/app/auth/permission-delete'
  },
  {
    name: 'initPermission',
    method: 'GET',
    path: '/app/auth/permission-getByParentId'
  },
  {
    name: 'loadPermission',
    method: 'GET',
    path: '/app/auth/permission-getByParentId'
  },
  {
    name: 'searchPermission',
    method: 'POST',
    path: '/app/auth/permission-findList'
  },
  {
    name: 'getGrantedButtons',
    method: 'GET',
    path: '/app/auth/grantedButtons'
  },
];


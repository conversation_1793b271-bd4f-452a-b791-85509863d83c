import Vue from 'vue';
import App from './App.vue';
import router from '@/plugins/router';
import store from '@/plugins/store';
import inject from '@/plugins/inject'; // 注意顺序，可能编译错乱，导致线上发布异常
import {AXIOS_DEFAULT_CONFIG} from '@/config';
import echarts from 'echarts';
import axiosOrg from 'axios';
import globalMy from '../src/global/global.js';
import './static/theme/theme.less';
import VueCookies from 'vue-cookies';
Vue.use(VueCookies);
import './plugins/element-ui'; // 引入 Element UI 配置
Vue.prototype.$echarts = echarts;
Vue.prototype.$global = globalMy;
let axiosDefault = axiosOrg.create(AXIOS_DEFAULT_CONFIG);
Vue.prototype.$axios = axiosDefault;
import localCache from '../src/utils/storage';
Vue.prototype.$localCache = localCache;
Vue.config.productionTip = false;
global.vbus = new Vue();
Vue.config.productionTip = false;
import Directives from './directives';
Vue.use(Directives);
Vue.use(inject);
Vue.use(echarts);
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI);
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
NProgress.configure({
  easing: 'ease', // 动画方式
  speed: 500, // 递增进度条的速度
  showSpinner: false, // 是否显示加载ico
  trickleSpeed: 200, // 自动递增间隔
  minimum: 0.3 // 初始化时的最小百分比
});
Vue.use(NProgress);
import './index.less'; // 设置主题文件
import '@/assets/scrollbar.css';
Vue.prototype.$href = location.origin;
Vue.prototype.checkParams = function (param, ruleIndex = 0, msg = '您输入的内容含有特殊字符') {
  let rules = ['^[a-z0-9A-Z-_\\u4e00-\\u9fa5]+$'];
  var flag = new RegExp(rules[ruleIndex]);
  if (Object.prototype.toString.call(param) === '[object String]') {
    console.log('str', flag.test(param));
    let isFlags = flag.test(param);
    if (!isFlags) {
      msg.length && iView.Message.error(msg);
    }
    return isFlags;
  }
  console.warn('检测参数请传入字符串');
};
window.VueInstance = new Vue({
  el: '#app',
  router,
  store,
  ...App
});

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\systems.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\systems.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [\n// menu\n{\n  name: 'resourcePage',\n  method: 'POST',\n  path: '/system/resource/page'\n}, {\n  name: 'resourceGet',\n  method: 'GET',\n  path: '/system/resource/get'\n}, {\n  name: 'resourceDelete',\n  method: 'OTHER',\n  path: '/system/resource/delete'\n}, {\n  name: 'org-user-tree',\n  method: 'POST',\n  path: '/system/user/org-user-tree'\n}, {\n  name: 'getPremissTree',\n  method: 'GET',\n  path: '/system/resource/tree'\n}, {\n  name: 'getParamList',\n  method: 'GET',\n  path: '/base/dict/list'\n}, {\n  name: 'getTreeList',\n  method: 'GET',\n  path: '/system/resource/list'\n}, {\n  name: 'addMenu',\n  method: 'POST',\n  path: '/system/resource/save'\n}, {\n  name: 'changePwd',\n  // 管理员修改用户密码\n  method: 'POST',\n  path: '/system/user/password/reset'\n}, {\n  name: 'changeSelfPwd',\n  // 用户修改密码\n  method: 'POST',\n  path: '/system/user/password/change'\n},\n// role\n{\n  name: 'rolePage',\n  // 角色分页\n  method: 'POST',\n  path: '/system/role/page'\n}, {\n  name: 'roleresourcesave',\n  // 角色绑定资源\n  method: 'POST',\n  path: '/system/role/resource/save'\n}, {\n  name: 'getResourceIds',\n  // 通过角色Id获取对应被绑定资源的ids\n  method: 'OTHERGET',\n  path: '/system/role/resource'\n}, {\n  name: 'roleGet',\n  method: 'GET',\n  path: '/system/role/get'\n}, {\n  name: 'roleList',\n  method: 'GET',\n  path: '/system/role/list'\n}, {\n  name: 'roleDelete',\n  // 删除角色\n  method: 'OTHER',\n  path: '/system/role/delete'\n}, {\n  name: 'addRole',\n  // 添加角色\n  method: 'POST',\n  path: '/system/role/save'\n},\n// user\n{\n  name: 'userPage',\n  // 用户分页\n  method: 'POST',\n  path: '/system/user/page'\n}, {\n  name: 'userList',\n  // 用户分页\n  method: 'POST',\n  path: '/system/user/list'\n}, {\n  name: 'userGet',\n  method: 'GET',\n  path: '/system/user/get'\n}, {\n  name: 'userDelete',\n  // 删除用户\n  method: 'OTHER',\n  path: '/icb/personUser/delete'\n}, {\n  name: 'addUser',\n  // 添加用户\n  method: 'POST',\n  path: '/icb/personUser/save'\n},\n// dept\n{\n  name: 'organizationTree',\n  // 获取组织树\n  method: 'GET',\n  path: '/system/organization/tree'\n}, {\n  name: 'organizationPage',\n  // 组织分页\n  method: 'POST',\n  path: '/system/organization/page'\n}, {\n  name: 'organizationGet',\n  method: 'GET',\n  path: '/system/organization/get'\n}, {\n  name: 'organizationDelete',\n  // 删除组织\n  method: 'OTHER',\n  path: '/system/organization/delete'\n}, {\n  name: 'addOrganization',\n  // 添加组织\n  method: 'POST',\n  path: '/system/organization/save'\n}, {\n  name: 'revoke',\n  // 用户解除角色\n  method: 'POST',\n  path: '/system/user/role/revoke'\n}, {\n  name: 'get-unread-num',\n  method: 'GET',\n  path: '/mc/notification/get-unread-num'\n}, {\n  name: 'lazyorgusertree',\n  method: 'POST_FORM',\n  path: '/system/user/lazy-org-user-tree'\n}, {\n  name: 'datapermission',\n  // 设置数据权限\n  method: 'POST',\n  path: '/system/data/permission/save'\n}, {\n  name: 'permissionquery',\n  method: 'GET',\n  path: '/system/data/permission/query'\n}, {\n  name: 'getAppLists',\n  // 获取应用app\n  method: 'GET',\n  path: '/system/app/list'\n}, {\n  name: 'roleGrantedApps',\n  // 查询角色已授权应用列表\n  method: 'GET',\n  path: '/system/app/role-granted-apps'\n}, {\n  name: 'saveRoleApp',\n  // 角色授权应用\n  method: 'POST',\n  path: '/system/app/save-role-app'\n}, {\n  name: 'getManpowerUrl',\n  // 获取人力资源\n  method: 'GET',\n  path: '/syn/jump/index'\n}, {\n  name: 'get-by-key',\n  method: 'GET',\n  path: '/system/prop/comm/get-by-key'\n}, {\n  name: 'my-granted-apps',\n  // 获得可切换系统\n  method: 'GET',\n  path: '/system/app/my-granted-apps'\n}, {\n  name: 'sim-pas-url',\n  // 获取财务系统\n  method: 'GET',\n  path: '/kingdee/sso/sim-pas-url'\n}, {\n  name: 'batch-save-user-role',\n  // 批量添加用户角色数据\n  method: 'POST',\n  path: '/system/user/batch-save-user-role'\n}, {\n  name: 'system-about',\n  // 获取系统关于\n  method: 'OTHERGET',\n  path: '/system/sys/about/me'\n}, {\n  name: 'costCenter-page',\n  // 查询成本中心管理分页\n  method: 'POST',\n  path: '/syn/financeCostCenter/page'\n}, {\n  name: 'costCenter-list',\n  // 查询成本中心管理分页\n  method: 'POST',\n  path: '/syn/financeCostCenter/list'\n}, {\n  name: 'costCenter-list-new',\n  // 查询成本中心管理列表\n  method: 'GET',\n  path: '/syn/financeCostCenter/get-finance-center'\n}, {\n  name: 'my-cost-center-for-add-monthly-data',\n  // 查询成本中心管理分页\n  method: 'POST',\n  path: '/syn/financeCostCenter/my-cost-center-for-add-monthly-data'\n}, {\n  name: 'costCenter-save',\n  // 保存成本中心管理\n  method: 'POST',\n  path: '/syn/financeCostCenter/save'\n}, {\n  name: 'costCenter-reset',\n  // 重置成本中心管理\n  method: 'OTHERGET',\n  path: '/syn/financeCostCenter/reset'\n}, {\n  name: 'costCenter-sync',\n  // 同步成本中心管理\n  method: 'GET',\n  path: '/syn/financeCostCenter/synchronization'\n}, {\n  name: 'costCenter-dts',\n  // 查询成本中心管理详情\n  method: 'OTHERGET',\n  path: '/syn/financeCostCenter/get'\n}, {\n  name: 'costProject-page',\n  // 查询工号管理分页\n  method: 'POST',\n  path: '/syn/financeCostProject/page'\n}, {\n  name: 'costProject-list',\n  // 查询工号管理列表\n  method: 'POST',\n  path: '/syn/financeCostProject/list'\n}, {\n  name: 'costProject-save',\n  // 保存工号管理\n  method: 'POST',\n  path: '/syn/financeCostProject/save'\n}, {\n  name: 'costProject-async',\n  // 同步工号\n  method: 'POST',\n  path: '/syn/financeCostProject/synchronization'\n}, {\n  name: 'costProject-dts',\n  // 查询工号管理详情\n  method: 'OTHERGET',\n  path: '/syn/financeCostProject/get'\n}, {\n  name: 'costProject-del',\n  // 删除工号管理\n  method: 'OTHER',\n  path: '/syn/financeCostProject/delete'\n}, {\n  name: 'costStaffRel-page',\n  // 查询员工成本中心分页\n  method: 'POST',\n  path: '/syn/financeCostStaffRel/page'\n}, {\n  name: 'costStaffRel-save',\n  // 保存员工成本中心\n  method: 'POST',\n  path: '/syn/financeCostStaffRel/save-staff-cost-rel'\n}, {\n  name: 'costStaffRel-change',\n  // 更改成本中心\n  method: 'POST',\n  path: '/syn/financeCostStaffRel/change-cost-center'\n}, {\n  name: 'costStaffRel-dts',\n  // 查询员工成本中心详情\n  method: 'OTHERGET',\n  path: '/syn/financeCostStaffRel/get'\n}, {\n  name: 'costStaffRel-del',\n  // 删除员工成本中心\n  method: 'OTHER',\n  path: '/syn/financeCostStaffRel/delete'\n}, {\n  name: 'costStaffRel-getBind',\n  // 查询已绑定的用户id列表\n  method: 'GET',\n  path: '/syn/financeCostStaffRel/get-bind-user-ids'\n}, {\n  name: 'financeSubject-page',\n  // 查询财务科目分页\n  method: 'POST',\n  path: '/syn/financeSubject/page'\n}, {\n  name: 'financeSubject-save',\n  // 保存财务科目\n  method: 'POST',\n  path: '/syn/financeSubject/save'\n}, {\n  name: 'financeSubject-dts',\n  // 查询财务科目详情\n  method: 'OTHERGET',\n  path: '/syn/financeSubject/get'\n}, {\n  name: 'financeSubject-del',\n  // 删除财务科目\n  method: 'OTHER',\n  path: '/syn/financeSubject/delete'\n}, {\n  name: 'financeCostMonthly-page',\n  // 查询员工工时管理分页\n  method: 'POST',\n  path: '/syn/financeCostMonthly/page'\n}, {\n  name: 'financeCostMonthly-save',\n  // 保存员工工时管理\n  method: 'POST',\n  path: '/syn/financeCostMonthly/create-new-one'\n}, {\n  name: 'financeCostMonthly-copy',\n  // 复制\n  method: 'POST',\n  path: '/syn/financeCostMonthly/copy-from'\n}, {\n  name: 'financeCostMonthly-dts',\n  // 查询员工工时管理详情\n  method: 'OTHERGET',\n  path: '/syn/financeCostMonthly/get'\n}, {\n  name: 'financeCostMonthly-del',\n  // 删除员工工时管理\n  method: 'OTHER',\n  path: '/syn/financeCostMonthly/delete'\n}, {\n  name: 'financeCostMonthly-getUser',\n  // 获取可配置工时的员工列表\n  method: 'GET',\n  path: '/syn/financeCostMonthly/users-to-add-hours'\n}, {\n  name: 'financeCostHoursDetail-page',\n  // 查询员工工时管理详情分页\n  method: 'POST',\n  path: '/syn/financeCostHoursDetail/page'\n}, {\n  name: 'financeCostHoursDetail-save',\n  // 保存员工工时管理详情\n  method: 'POST',\n  path: '/syn/financeCostHoursDetail/save'\n}, {\n  name: 'financeCostHoursDetail-dts',\n  // 查询员工工时管理详情详情\n  method: 'OTHERGET',\n  path: '/syn/financeCostHoursDetail/get'\n}, {\n  name: 'financeCostHoursDetail-del',\n  // 删除员工工时管理详情\n  method: 'OTHER',\n  path: '/syn/financeCostHoursDetail/delete'\n}, {\n  name: 'deptSync',\n  // 拉取致远部门数据\n  method: 'GET',\n  path: '/syn/zy/fetch/dept'\n}, {\n  name: 'personSync',\n  // 拉取致远人员数据\n  method: 'GET',\n  path: '/syn/zy/fetch/person'\n}, {\n  name: 'unLock',\n  // 解锁用户\n  method: 'POST',\n  path: '/system/user/unlock'\n}, {\n  name: 'userSyc',\n  // 拉取飞书用户数据\n  method: 'GET',\n  path: '/fs/sync/user'\n}, {\n  name: 'module-options',\n  // 获取用户所属模块下拉选项\n  method: 'GET',\n  path: '/syn/financeCostCenter/module-options'\n}, {\n  name: 'module-zhiYuanModuleEnum',\n  // 查询致远模块枚举值列表\n  method: 'POST',\n  path: '/syn/zhiYuanModuleEnum/list'\n}, {\n  name: 'module-get-bound-list',\n  // 查询成本中心已绑定的模块枚举值\n  method: 'GET',\n  path: '/syn/financeCostCenter/get-bound-list'\n}, {\n  name: 'bind-cost-staff',\n  // 同步成本中心与用户模块关系\n  method: 'GET',\n  path: '/syn/financeCostStaffRel/bind-cost-staff'\n}, {\n  name: 'bindData',\n  method: 'GET',\n  path: '/syn/financeCostStaffRel/auto-bind'\n}, {\n  name: 'find-user-module',\n  // 根据用户IDS查询致远模块\n  method: 'POST',\n  path: '/syn/financeCostStaffRel/find-user-module'\n}, {\n  name: 'find-user-now-module',\n  // 根据用户IDS查询致远模块\n  method: 'POST',\n  path: '/syn/financeCostStaffRel/find-user-now-module'\n}, {\n  name: 'find-user-dept',\n  // 根据用户IDS查询致远用户所属部门\n  method: 'POST',\n  path: '/syn/zhiYuanDept/find-user-dept'\n}, {\n  name: 'find-zhi-yuan-person',\n  // 根据用户IDS查询致远用户信息\n  method: 'POST',\n  path: '/syn/zhiYuanPerson/find-zhi-yuan-person'\n}, {\n  name: 'fs-sync-user',\n  // 拉取飞书用户数据\n  method: 'GET',\n  path: '/fs/sync/user'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/systems.js"], "sourcesContent": ["export default [\r\n  // menu\r\n  {\r\n    name: 'resourcePage',\r\n    method: 'POST',\r\n    path: '/system/resource/page'\r\n  },\r\n  {\r\n    name: 'resourceGet',\r\n    method: 'GET',\r\n    path: '/system/resource/get'\r\n  },\r\n  {\r\n    name: 'resourceDelete',\r\n    method: 'OTHER',\r\n    path: '/system/resource/delete'\r\n  },\r\n  {\r\n    name: 'org-user-tree',\r\n    method: 'POST',\r\n    path: '/system/user/org-user-tree'\r\n  },\r\n  {\r\n    name: 'getPremissTree',\r\n    method: 'GET',\r\n    path: '/system/resource/tree'\r\n  },\r\n  {\r\n    name: 'getParamList',\r\n    method: 'GET',\r\n    path: '/base/dict/list'\r\n  },\r\n  {\r\n    name: 'getTreeList',\r\n    method: 'GET',\r\n    path: '/system/resource/list'\r\n  },\r\n  {\r\n    name: 'addMenu',\r\n    method: 'POST',\r\n    path: '/system/resource/save'\r\n  },\r\n  {\r\n    name: 'changePwd', // 管理员修改用户密码\r\n    method: 'POST',\r\n    path: '/system/user/password/reset'\r\n  },\r\n  {\r\n    name: 'changeSelfPwd', // 用户修改密码\r\n    method: 'POST',\r\n    path: '/system/user/password/change'\r\n  },\r\n  // role\r\n  {\r\n    name: 'rolePage', // 角色分页\r\n    method: 'POST',\r\n    path: '/system/role/page'\r\n  },\r\n  {\r\n    name: 'roleresourcesave', // 角色绑定资源\r\n    method: 'POST',\r\n    path: '/system/role/resource/save'\r\n  },\r\n  {\r\n    name: 'getResourceIds', // 通过角色Id获取对应被绑定资源的ids\r\n    method: 'OTHERGET',\r\n    path: '/system/role/resource'\r\n  },\r\n  {\r\n    name: 'roleGet',\r\n    method: 'GET',\r\n    path: '/system/role/get'\r\n  },\r\n  {\r\n    name: 'roleList',\r\n    method: 'GET',\r\n    path: '/system/role/list'\r\n  },\r\n  {\r\n    name: 'roleDelete', // 删除角色\r\n    method: 'OTHER',\r\n    path: '/system/role/delete'\r\n  },\r\n  {\r\n    name: 'addRole', // 添加角色\r\n    method: 'POST',\r\n    path: '/system/role/save'\r\n  },\r\n  // user\r\n  {\r\n    name: 'userPage', // 用户分页\r\n    method: 'POST',\r\n    path: '/system/user/page'\r\n  },\r\n  {\r\n    name: 'userList', // 用户分页\r\n    method: 'POST',\r\n    path: '/system/user/list'\r\n  },\r\n  {\r\n    name: 'userGet',\r\n    method: 'GET',\r\n    path: '/system/user/get'\r\n  },\r\n  {\r\n    name: 'userDelete', // 删除用户\r\n    method: 'OTHER',\r\n    path: '/icb/personUser/delete'\r\n  },\r\n  {\r\n    name: 'addUser', // 添加用户\r\n    method: 'POST',\r\n    path: '/icb/personUser/save'\r\n  },\r\n  // dept\r\n  {\r\n    name: 'organizationTree', // 获取组织树\r\n    method: 'GET',\r\n    path: '/system/organization/tree'\r\n  },\r\n  {\r\n    name: 'organizationPage', // 组织分页\r\n    method: 'POST',\r\n    path: '/system/organization/page'\r\n  },\r\n  {\r\n    name: 'organizationGet',\r\n    method: 'GET',\r\n    path: '/system/organization/get'\r\n  },\r\n  {\r\n    name: 'organizationDelete', // 删除组织\r\n    method: 'OTHER',\r\n    path: '/system/organization/delete'\r\n  },\r\n  {\r\n    name: 'addOrganization', // 添加组织\r\n    method: 'POST',\r\n    path: '/system/organization/save'\r\n  },\r\n  {\r\n    name: 'revoke', // 用户解除角色\r\n    method: 'POST',\r\n    path: '/system/user/role/revoke'\r\n  },\r\n  {\r\n    name: 'get-unread-num',\r\n    method: 'GET',\r\n    path: '/mc/notification/get-unread-num'\r\n  },\r\n  {\r\n    name: 'lazyorgusertree',\r\n    method: 'POST_FORM',\r\n    path: '/system/user/lazy-org-user-tree'\r\n  },\r\n  {\r\n    name: 'datapermission', // 设置数据权限\r\n    method: 'POST',\r\n    path: '/system/data/permission/save'\r\n  },\r\n  {\r\n    name: 'permissionquery',\r\n    method: 'GET',\r\n    path: '/system/data/permission/query'\r\n  },\r\n  {\r\n    name: 'getAppLists', // 获取应用app\r\n    method: 'GET',\r\n    path: '/system/app/list'\r\n  },\r\n  {\r\n    name: 'roleGrantedApps', // 查询角色已授权应用列表\r\n    method: 'GET',\r\n    path: '/system/app/role-granted-apps'\r\n  },\r\n  {\r\n    name: 'saveRoleApp', // 角色授权应用\r\n    method: 'POST',\r\n    path: '/system/app/save-role-app'\r\n  },\r\n  {\r\n    name: 'getManpowerUrl', // 获取人力资源\r\n    method: 'GET',\r\n    path: '/syn/jump/index'\r\n  },\r\n  {\r\n    name: 'get-by-key',\r\n    method: 'GET',\r\n    path: '/system/prop/comm/get-by-key'\r\n  },\r\n  {\r\n    name: 'my-granted-apps', // 获得可切换系统\r\n    method: 'GET',\r\n    path: '/system/app/my-granted-apps'\r\n  },\r\n  {\r\n    name: 'sim-pas-url', // 获取财务系统\r\n    method: 'GET',\r\n    path: '/kingdee/sso/sim-pas-url'\r\n  },\r\n  {\r\n    name: 'batch-save-user-role', // 批量添加用户角色数据\r\n    method: 'POST',\r\n    path: '/system/user/batch-save-user-role'\r\n  },\r\n  {\r\n    name: 'system-about', // 获取系统关于\r\n    method: 'OTHERGET',\r\n    path: '/system/sys/about/me'\r\n  },\r\n  {\r\n    name: 'costCenter-page', // 查询成本中心管理分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostCenter/page'\r\n  },\r\n  {\r\n    name: 'costCenter-list', // 查询成本中心管理分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostCenter/list'\r\n  },\r\n  {\r\n    name: 'costCenter-list-new', // 查询成本中心管理列表\r\n    method: 'GET',\r\n    path: '/syn/financeCostCenter/get-finance-center'\r\n  },\r\n  {\r\n    name: 'my-cost-center-for-add-monthly-data', // 查询成本中心管理分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostCenter/my-cost-center-for-add-monthly-data'\r\n  },\r\n  {\r\n    name: 'costCenter-save', // 保存成本中心管理\r\n    method: 'POST',\r\n    path: '/syn/financeCostCenter/save'\r\n  },\r\n  {\r\n    name: 'costCenter-reset', // 重置成本中心管理\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostCenter/reset'\r\n  },\r\n  {\r\n    name: 'costCenter-sync', // 同步成本中心管理\r\n    method: 'GET',\r\n    path: '/syn/financeCostCenter/synchronization'\r\n  },\r\n  {\r\n    name: 'costCenter-dts', // 查询成本中心管理详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostCenter/get'\r\n  },\r\n  {\r\n    name: 'costProject-page', // 查询工号管理分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostProject/page'\r\n  },\r\n  {\r\n    name: 'costProject-list', // 查询工号管理列表\r\n    method: 'POST',\r\n    path: '/syn/financeCostProject/list'\r\n  },\r\n  {\r\n    name: 'costProject-save', // 保存工号管理\r\n    method: 'POST',\r\n    path: '/syn/financeCostProject/save'\r\n  },\r\n  {\r\n    name: 'costProject-async', // 同步工号\r\n    method: 'POST',\r\n    path: '/syn/financeCostProject/synchronization'\r\n  },\r\n  {\r\n    name: 'costProject-dts', // 查询工号管理详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostProject/get'\r\n  },\r\n  {\r\n    name: 'costProject-del', // 删除工号管理\r\n    method: 'OTHER',\r\n    path: '/syn/financeCostProject/delete'\r\n  },\r\n  {\r\n    name: 'costStaffRel-page', // 查询员工成本中心分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostStaffRel/page'\r\n  },\r\n  {\r\n    name: 'costStaffRel-save', // 保存员工成本中心\r\n    method: 'POST',\r\n    path: '/syn/financeCostStaffRel/save-staff-cost-rel'\r\n  },\r\n  {\r\n    name: 'costStaffRel-change', // 更改成本中心\r\n    method: 'POST',\r\n    path: '/syn/financeCostStaffRel/change-cost-center'\r\n  },\r\n  {\r\n    name: 'costStaffRel-dts', // 查询员工成本中心详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostStaffRel/get'\r\n  },\r\n  {\r\n    name: 'costStaffRel-del', // 删除员工成本中心\r\n    method: 'OTHER',\r\n    path: '/syn/financeCostStaffRel/delete'\r\n  },\r\n  {\r\n    name: 'costStaffRel-getBind', // 查询已绑定的用户id列表\r\n    method: 'GET',\r\n    path: '/syn/financeCostStaffRel/get-bind-user-ids'\r\n  },\r\n  {\r\n    name: 'financeSubject-page', // 查询财务科目分页\r\n    method: 'POST',\r\n    path: '/syn/financeSubject/page'\r\n  },\r\n  {\r\n    name: 'financeSubject-save', // 保存财务科目\r\n    method: 'POST',\r\n    path: '/syn/financeSubject/save'\r\n  },\r\n  {\r\n    name: 'financeSubject-dts', // 查询财务科目详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeSubject/get'\r\n  },\r\n  {\r\n    name: 'financeSubject-del', // 删除财务科目\r\n    method: 'OTHER',\r\n    path: '/syn/financeSubject/delete'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-page', // 查询员工工时管理分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostMonthly/page'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-save', // 保存员工工时管理\r\n    method: 'POST',\r\n    path: '/syn/financeCostMonthly/create-new-one'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-copy', // 复制\r\n    method: 'POST',\r\n    path: '/syn/financeCostMonthly/copy-from'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-dts', // 查询员工工时管理详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostMonthly/get'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-del', // 删除员工工时管理\r\n    method: 'OTHER',\r\n    path: '/syn/financeCostMonthly/delete'\r\n  },\r\n  {\r\n    name: 'financeCostMonthly-getUser', // 获取可配置工时的员工列表\r\n    method: 'GET',\r\n    path: '/syn/financeCostMonthly/users-to-add-hours'\r\n  },\r\n  {\r\n    name: 'financeCostHoursDetail-page', // 查询员工工时管理详情分页\r\n    method: 'POST',\r\n    path: '/syn/financeCostHoursDetail/page'\r\n  },\r\n  {\r\n    name: 'financeCostHoursDetail-save', // 保存员工工时管理详情\r\n    method: 'POST',\r\n    path: '/syn/financeCostHoursDetail/save'\r\n  },\r\n  {\r\n    name: 'financeCostHoursDetail-dts', // 查询员工工时管理详情详情\r\n    method: 'OTHERGET',\r\n    path: '/syn/financeCostHoursDetail/get'\r\n  },\r\n  {\r\n    name: 'financeCostHoursDetail-del', // 删除员工工时管理详情\r\n    method: 'OTHER',\r\n    path: '/syn/financeCostHoursDetail/delete'\r\n  },\r\n  {\r\n    name: 'deptSync', // 拉取致远部门数据\r\n    method: 'GET',\r\n    path: '/syn/zy/fetch/dept'\r\n  },\r\n  {\r\n    name: 'personSync', // 拉取致远人员数据\r\n    method: 'GET',\r\n    path: '/syn/zy/fetch/person'\r\n  },\r\n  {\r\n    name: 'unLock', // 解锁用户\r\n    method: 'POST',\r\n    path: '/system/user/unlock'\r\n  },\r\n  {\r\n    name: 'userSyc', // 拉取飞书用户数据\r\n    method: 'GET',\r\n    path: '/fs/sync/user'\r\n  },\r\n  {\r\n    name: 'module-options', // 获取用户所属模块下拉选项\r\n    method: 'GET',\r\n    path: '/syn/financeCostCenter/module-options'\r\n  },\r\n  {\r\n    name: 'module-zhiYuanModuleEnum', // 查询致远模块枚举值列表\r\n    method: 'POST',\r\n    path: '/syn/zhiYuanModuleEnum/list'\r\n  },\r\n  {\r\n    name: 'module-get-bound-list', // 查询成本中心已绑定的模块枚举值\r\n    method: 'GET',\r\n    path: '/syn/financeCostCenter/get-bound-list'\r\n  },\r\n  {\r\n    name: 'bind-cost-staff', // 同步成本中心与用户模块关系\r\n    method: 'GET',\r\n    path: '/syn/financeCostStaffRel/bind-cost-staff'\r\n  },\r\n  {\r\n    name: 'bindData',\r\n    method: 'GET',\r\n    path: '/syn/financeCostStaffRel/auto-bind'\r\n  },\r\n\r\n  {\r\n    name: 'find-user-module', // 根据用户IDS查询致远模块\r\n    method: 'POST',\r\n    path: '/syn/financeCostStaffRel/find-user-module'\r\n  },\r\n\r\n  {\r\n    name: 'find-user-now-module', // 根据用户IDS查询致远模块\r\n    method: 'POST',\r\n    path: '/syn/financeCostStaffRel/find-user-now-module'\r\n  },\r\n\r\n  {\r\n    name: 'find-user-dept', // 根据用户IDS查询致远用户所属部门\r\n    method: 'POST',\r\n    path: '/syn/zhiYuanDept/find-user-dept'\r\n  },\r\n  {\r\n    name: 'find-zhi-yuan-person', // 根据用户IDS查询致远用户信息\r\n    method: 'POST',\r\n    path: '/syn/zhiYuanPerson/find-zhi-yuan-person'\r\n  },\r\n  {\r\n    name: 'fs-sync-user', // 拉取飞书用户数据\r\n    method: 'GET',\r\n    path: '/fs/sync/user'\r\n  }\r\n];\r\n"], "mappings": "AAAA,eAAe;AACb;AACA;EACEA,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EAAE;EACnBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC;AACD;AACA;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAAE;EACpBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EAAE;EACjBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC;AACD;AACA;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAAE;EACpBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EAAE;EACjBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC;AACD;AACA;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EAAE;EAChBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EAAE;EACtBC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAAE;EAC7BC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qCAAqC;EAAE;EAC7CC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAAE;EAC7BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAAE;EAC7BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAAE;EAC7BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,yBAAyB;EAAE;EACjCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,yBAAyB;EAAE;EACjCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,yBAAyB;EAAE;EACjCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,4BAA4B;EAAE;EACpCC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,6BAA6B;EAAE;EACrCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,6BAA6B;EAAE;EACrCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,4BAA4B;EAAE;EACpCC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,4BAA4B;EAAE;EACpCC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAAE;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EAAE;EAChBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EAAE;EACjBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,0BAA0B;EAAE;EAClCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,uBAAuB;EAAE;EAC/BC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EAAE;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
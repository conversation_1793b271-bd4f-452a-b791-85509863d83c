//@import '~iview/src/styles/index.less';
// 升级4.0.0
.grid_technology{
  //@menu-dark-title: #001D30 !important;
  //@menu-dark-active-bg: #001D30 !important;
  //@menu-dark-group-title-color: rgba(20,62,105,1);
  //@menu-dark-subsidiary-color: #52768C !important;
  //@layout-sider-background: #001D30 !important;
  //@layout-header-background: #153952;
  //@layout-body-background: ;
  //@table-td-highlight-bg: rgba(208,235,249,0.1) !important;
  //@table-td-stripe-bg: rgba(33,72,100,0.7) !important;
  //@table-thead-bg: rgba(208,235,249,0.1) !important;
  //@table-td-hover-bg: rgba(208,235,249,0.2) !important;
  //@border-color-base: @bg-light-tech-right;
  //@card-prefix-cls: @bg-deep-tech-right !important;
  //@btn-primary-bg: @bg-light-tech-right;
  //@btn-primary-color:@font-color-base;
  //@input-bg: @bg-light-tech-right;
  //@primary-color:@bg-light-tech-right;
  //@input-color:rgba(20,62,105,1);
  //@input-placeholder-color:rgba(20,62,105,1);
  @bg-deep-tech-left: rgba(5,46,59,1)!important;
  @bg-deep-tech-right: rgba(20,41,72,1)!important;
  @bg-light-tech-right: rgba(20,62,105,1)!important;
  @font-color-base: #fff!important;
  .main {
    width: 100%;
    height: 100%;
    background:@bg-deep-tech-left;
  }
  .ivu-drawer-content {
    background-color: @bg-light-tech-right;
    //color: @font-color-base;
    .ivu-drawer-header .ivu-drawer-header-inner{
      color: @font-color-base;
    }
  }
  .drawer-footer {
    background-color: @bg-light-tech-right;
    color: @font-color-base;
  }
  //.main .content-wrapper {
  //  background:rgba(30,57,89,0.52)!important;
  //  //opacity:0.68!important;
  //}
  .main .main-content-con {
    background:@bg-deep-tech-left;
  }

  .left-sider.ivu-layout-sider {
    background:rgba(5,46,59,0.6)!important;
    min-width: 10px!important;
    max-width: 210px!important;
  }
  .ivu-menu-dark {
    background:rgba(5,46,59,0.4)!important;
  }

  .left-sider.ivu-layout-sider .logo-con {
    background:@bg-deep-tech-left;
  }
    /* menu样式 */
  .ivu-menu-submenu.ivu-menu-item-active.ivu-menu-child-item-active>div.ivu-menu-submenu-title{
    background:rgba(5,46,59,0.6)!important;
    color: @font-color-base;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
    background:rgba(5,46,59,0.6)!important;
    color: @font-color-base;
  }
  .theme1 .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-child-item-active div.ivu-menu-submenu-title,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-child-item-active{
    background: @bg-light-tech-right;
    color:rgba(0,228,251,1)!important;
  }
  .ivu-menu .ivu-menu-dark.ivu-menu-vertical .ivu-submenu .ivu-menu-item:hover{
    background: @bg-light-tech-right;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover{
    background: @bg-light-tech-right;
    color: rgba(255,255,255,0.8) !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened div.ivu-menu-submenu-title:hover{
    background: @bg-light-tech-right;
    color:rgba(0,228,251,1)!important;
  }
  .ivu-layout-sider{
    background: #001D30 !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-submenu-title {
    background: #001D30 !important;
    color: #52768C !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened div.ivu-menu-submenu-title,.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened li{
    background: #00253F !important;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened>div.ivu-menu-submenu-title{
    background: rgba(17,56,73,0.8) !important;
    color:rgba(0,228,251,1)!important;
    border-top:1px solid rgba(65,103,120,1);
    border-bottom:1px solid rgba(65,103,120,1);
  }
  .ivu-menu-item{
    background: rgba(5,46,59,0.3) !important;
    color: @font-color-base;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu li.ivu-menu-item-active, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu li.ivu-menu-item-active:hover{
    background: @bg-light-tech-right;
    color: rgba(255,255,255,0.8) !important;
  }
  .left-sider.ivu-layout-sider{
    border-right: 2px solid #2E5A6C;
    width: 267px!important;
  }
  /* layout背景图 */
  //.@{layout-prefix-cls}{
  //  background: rgba(0,0,0,0);
  //}
  .ivu-layout{
    background-position: 100% 100%!important;
    background-attachment: fixed!important;
    background-size: cover!important;
    color: @font-color-base;
  }
  .ivu-notice-notice.ivu-notice-notice-closable.ivu-notice-notice-with-desc{
    color: @font-color-base;
  }
  /* table样式 */
  .ivu-table table {
    background-color:rgba(20,41,72,0.6)!important;
  }
  .ivu-table.ivu-table-default{
    background:rgba(20,41,72,0.8)!important;
    border: 1px solid @bg-light-tech-right;
  }
  //.ivu-table-body tr:nth-child(2n) td, .ivu-table-fixed-body tr:nth-child(2n) td{
  //  background-color: @bg-light-tech-right;
  //  color: @font-color-base;
  //  border: 0px !important;
  //}

  .ivu-table-fixed, .ivu-table-fixed-right {
    background-color: rgba(20,41,72,0.8) !important;
    color: @font-color-base;
  }
  .ivu-table-fixed-header {
    border-bottom: 1px solid rgba(18,80,139,1)!important;
  }
  //.ivu-table-fixed-body tr:nth-child(2n) td {
  //  background-color: rgba(20,41,72,0.8) !important;
  //  color: @font-color-base;
  //  border: 0px !important;
  //}
  .ivu-table td{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border:none;
    border-bottom: 1px dashed rgba(119,119,119,0.3) !important;
  }
  td[border-bottom*="1px dashed #777"] {
    opacity: 0.3!important;
  }
  .ivu-table  .ivu-table-header {
    background-color: rgba(20,41,72,0.8) !important;
    border-bottom: 1px solid rgba(18,80,139,1);
  }
  //.ivu-table-body tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td,.ivu-table-fixed-body tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td,.ivu-table .ivu-table-row-hover td{
  //  background-color: rgba(208,235,249,0.1) !important;
  //  color: @font-color-base;
  //  border: 0px !important;
  //}
  .ivu-table-tbody{
    border-bottom: 0px !important;
  }

  .ivu-table thead th{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border: 0px !important;
  }
  .style-table-num{
    background-color: unset !important;
    color: @font-color-base;
  }
  .ivu-table-wrapper {
    border: none;
  }
  .ivu-table {
    border: 0px !important;
  }
  .ivu-table:before{
    width:0% !important;
  }
  .ivu-table-fixed::before, .ivu-table-fixed-right::before{
    width:0% !important;
  }
  .ivu-table:after{
    width:0% !important;
  }
  .ivu-table-fixed-body,.ivu-table-fixed-header{
    background-color: @bg-light-tech-right;
  }
  //.ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n-1).ivu-table-row td:not(.ivu-table-hidden),.ivu-table-fixed-header table thead tr th{
  //  background-color: rgb(29,68,94) !important;
  //}
  //.ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n).ivu-table-row td:not(.ivu-table-hidden),.ivu-table-fixed-header table thead tr th{
  //  background-color: @bg-light-tech-right;
  //}
  //.ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n).ivu-table-row.ivu-table-row-hover td:not(.ivu-table-hidden){
  //  background-color: rgb(40,75,99) !important;
  //}
  //.ivu-table-fixed-body table tbody.ivu-table-tbody tr:nth-child(2n-1).ivu-table-row.ivu-table-row-hover td:not(.ivu-table-hidden){
  //  background-color: rgb(40,75,99) !important;
  //}
  body *{
    color: @font-color-base;
  }

  /* 筛选样式 */
  .condition-container{
    padding: 0px !important;
    border:1px solid rgba(57,109,131,1);
    color: @font-color-base;
  }
  .condition-title,.nav.height40,.nav.heightAuto{
    background:rgba(208,235,249,0.1) !important;
  }
  .condition-title{
    border-top-right-radius: unset !important;
  }
  .condition-container span.active{
    color: #F87A7B !important;
  }
  .condition-box{
    background: rgba(0,0,0,0) !important;
  }
  .mutil-query-title{
    color: #619BB3 !important;
  }
  .more{
    color: @font-color-base;
  }
  .nav.height40,.nav.heightAuto{
    border-color: @bg-light-tech-right;
  }
  .nav.height40:first-child{
    border-top-style: solid;
    border-color: @bg-light-tech-right;
  }
  .header-con{
    background: #153952 !important;
  }
  .table-sort i{
    color: #11758e !important;
  }
  .sort-active span{
    color: #21C2D8 !important;
  }
  .sort-active{
    border-color: #21C2D8 !important;
  }
  .table-sort i:hover{
    color: #21C2D8 !important;
  }
  .ivu-layout-content .ivu-icon{
    color: #11758e ;
  }
  .ivu-layout-content .ivu-icon.iconActive{
    color: #21C2D8 !important;
  }
  /* 面包屑样式 */
  .ivu-breadcrumb-item-link{
    color: @font-color-base;
  }
  .custom-content-con *{
    color: @font-color-base;
  }
  /* card样式 */
  .ivu-card{
    background:rgba(20,41,72,0.3)!important;
    border:0px solid rgba(57,109,131,1) !important;
    color: @font-color-base;
  }
  .ivu-card:hover{
    box-shadow: unset !important;
  }
  .ivu-card-head{
    border:1px solid rgba(57,109,131,1) !important;
    background:@bg-deep-tech-right;
    color: @font-color-base;
  }
  .ivu-card-head *{
    color: @font-color-base;
  }
  .table-sort{
    color: @font-color-base;
  }
  .ivu-card-header{
    background: @bg-deep-tech-right !important;
  }
  .ivu-card-body{
    background:rgba(20,41,72,0.3);
    border:1px solid rgba(57,109,131,1);
  }
  /* 半透明card */
  .ivu-card.translucent{
    background:@bg-deep-tech-right;
    border:0px solid rgba(57,109,131,1) !important;
    color: @font-color-base;
  }
  .ivu-card.translucent .ivu-card-head{
    border:1px solid rgba(57,109,131,1) !important;
    background:rgba(208,235,249,0.1);
    color: @font-color-base;
  }
  .ivu-card.translucent .ivu-card-head *{
    color: @font-color-base;
  }
  .ivu-card.translucent .ivu-card-header{
    background: rgba(208,235,249,0.1) !important;
  }
  .ivu-card.translucent .ivu-card-body{
    background:rgba(208,235,249,0.1);
    border:1px solid rgba(57,109,131,1);
  }
  /* 全透明无边框card */
  .ivu-card.no-border{
    background:@bg-deep-tech-right;
    border:0px solid rgba(57,109,131,0) !important;
    color: @font-color-base;
  }
  .ivu-card.no-border .ivu-card-head{
    border:1px solid rgba(57,109,131,0) !important;
    background:@bg-deep-tech-right;
    color: @font-color-base;
  }
  .ivu-card.no-border .ivu-card-head *{
    color: @font-color-base;
  }
  .ivu-card.no-border .ivu-card-header{
    background: @bg-deep-tech-right !important;
  }
  .ivu-card.no-border .ivu-card-body{
    background:@bg-deep-tech-right;
    border:1px solid rgba(57,109,131,0);
  }
  /* btn */
  .ivu-btn-primary{
    background-color: rgba(15,89,190,1) !important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .ivu-btn-text{
    background-color: rgba(15,89,190,1)!important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .ivu-btn i.ivu-icon{
    color: @font-color-base;
  }
  .ivu-btn span{
    color: @font-color-base;
  }
  .ivu-btn-default{
    background-color: rgba(15,89,190,1)!important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .ivu-btn-error{
    background-color: #F87A7B !important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .actionBtn{
    color: #21C2D8 !important;
  }
  /* 滚动条样式 */
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar
  {
    width: 10px;
    background-color: rgba(20,62,105,1);
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track
  {
    box-shadow: inset 0 0 6px rgba(0,0,0,0.3) !important;
    border-radius: 10px !important;
    background-color: @bg-light-tech-right;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb
  {
    border-radius: 10px !important;
    box-shadow: inset 0 0 6px rgba(0,0,0,.3) !important;
    background-color: #001D30 !important;
  }
  /*定义最上方和最下方的按钮*/
  ::-webkit-scrollbar-button{
    background-color: #001D30 !important;
    border:1px solid #001D30 !important;
  }
  /* input样式 */
  .ivu-input{
    background-color: rgba(57,109,131,0);
    color: @font-color-base;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-input::placeholder {
    color: @font-color-base;
  }
  .style-body .ivu-input{
    background-color: #00253f !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .style-body  .ivu-select{
    background-color: #00253f !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  //input::placeholder{
  //  color: @bg-light-tech-right;
  //}
  .ivu-select-placeholder{
    color: @bg-light-tech-right;
  }
  textarea::placeholder{
    color: @bg-light-tech-right;
  }
  .ivu-input-word-count{
    background-color: rgba(57,109,131,0) !important;
    color: rgba(57,109,131,1) !important;
  }
  .ivu-form-item-error-tip{
    color: #F87A7B !important;
  }
  a{
    color: #21C2D8 !important;
  }
  .ivu-select{
    background-color: rgba(57,109,131,0) !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-selection{
    background-color: rgba(57,109,131,0) !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-dropdown{
    background-color: #284B63 !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-select-item:hover{
    background-color: rgba(208,235,249,0.2) !important;
  }
  .ivu-select-item{
    color: @font-color-base;
  }
  .ivu-select-input{
    color: @font-color-base;
  }
  /* 分页样式 */
  .ivu-page-prev,.ivu-page-item,.ivu-page-next{
    background-color: #001D30 !important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .ivu-page-item.ivu-page-item-active{
    background-color: #284B63 !important;
    color: @font-color-base;
    border-color: #001D30 !important;
  }
  .ivu-page-options input{
    background-color: rgba(57,109,131,0);
    color: rgba(57,109,131,1);
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-spin{
    border-radius: 5px;
    background-color: rgba(57,109,131,0.7);
  }
  .ivu-page.mini *{
    color: @font-color-base;
  }
  /* 树形下拉框样式 */
  .style-poptip .ivu-btn-default{
    background-color: rgba(57,109,131,0) !important;
    color: rgba(57,109,131,1) !important;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px;
  }
  .ivu-poptip{
    width: calc(100% - 200px);
    z-index: 999;
  }
  .ivu-poptip-inner{
    z-index: 999;
  }
  .ivu-poptip-body{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
    max-height: 300px;
    overflow-y: scroll;
  }
  .ivu-poptip-title {
    background-color: @bg-light-tech-right;
    .ivu-poptip-title-inner {
      color: @font-color-base;
    }
  }
  .ivu-poptip-body-content{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
    border-radius:5px !important;
  }
  .ivu-collapse.ivu-collapse-simple{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
  }
  .ivu-collapse.ivu-collapse-simple .ivu-collapse-item .ivu-collapse-header{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border-color:rgba(57,109,131,1) !important;
  }
  .pt-button span{
    display: inline;
    color: @font-color-base;
    width: auto !important;
  }
  .pt-button i.ivu-icon{
    color: #11758e !important;
  }
  .pt-button span div{
    height: 100%;
    line-height: 30px;
  }
  .ivu-tabs{
    height: 100%;
  }
  .ivu-collapse-content{
    border: 0px;
    background-color: @bg-light-tech-right !important;

  }
  .ivu-collapse > .ivu-collapse-item *{
    color: @font-color-base;
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header{
    border-bottom: 0px !important;
  }
  /* tag样式 */
  .ivu-tag-dot{
    background-color: #001D30 !important;
    border-color: #001D30 !important;
  }
  .ivu-tag-text{
    color: white !important;
  }
  .ivu-tag.ivu-tag-default{
    background-color: #001D30 !important;
    border-color: #001D30 !important;
  }
  .ivu-select-multiple .ivu-tag {
    background-color: #001D30 !important;
    border-color: #001D30 !important;
  }
  /* 模态框样式 */
  .ivu-modal-content{
    //background:url('../../assets/rect-background.png') no-repeat fixed 100% !important;
    //border-radius: 0px;
    background: url('../../assets/rect-background.png') no-repeat!important;
    background-position: 100% 100%!important;
    background-attachment: fixed!important;
    background-size: cover!important;
    color: @font-color-base;
  }
  .ivu-modal-header{
    background:rgba(20,41,72,0.6) !important;
    border-color: #153952 !important;
  }
  .ivu-modal-header-inner{
    color: @font-color-base;
  }
  .ivu-modal-body{
    background:rgba(20,41,72,0.6) !important;
  }
  .ivu-modal-body div,ivu-modal-body p{
    color: @font-color-base;
  }
  .ivu-modal-footer{
    background:rgba(208,235,249,0.1) !important;
    border-color: #153952 !important;
  }
  /* message提示样式 */
  .ivu-message-notice-content{
    background-color: #1F2D3D !important;
    color: @font-color-base;
  }
  .ivu-notice-notice{
    margin-top: 120% !important;
  }
  /* 穿梭框样式 */
  .ivu-transfer-list-header{
    background:@bg-deep-tech-right !important;
    border-color: @bg-light-tech-right;
    color: @font-color-base;
  }
  .ivu-transfer-list input{
    background-color: #00253f !important;
    color: @font-color-base;
    border-color:rgba(57,109,131,1);
    border-radius:5px;
  }
  .ivu-transfer-list-body{
    background:@bg-deep-tech-right !important;
    border-color: @bg-light-tech-right;
  }
  .ivu-transfer-list-body *{
    color: @font-color-base;
  }
  .ivu-transfer-list-content-item:hover{
    background: @bg-light-tech-right;
  }
  .ivu-transfer-list-content-item:hover span{
    color: @font-color-base;
  }
  label{
    color: @font-color-base;
  }
  .ivu-form-item-required .ivu-form-item-label:before{
    color: #F87A7B !important;
  }
  .ivu-checkbox-inner{
    border-color: @bg-light-tech-right;
    background-color: #001D30 !important;
  }
  .ivu-checkbox-wrapper.ivu-checkbox-wrapper-checked.ivu-checkbox-default .ivu-checkbox-inner{
    border-color: #001D30 !important;
    background-color: @bg-light-tech-right;
  }
  .ivu-divider.ivu-divider-horizontal.ivu-divider-default.ivu-divider-dashed{
    border-top:1px dashed @bg-light-tech-right;
  }
  /* tabs */
  .ivu-tabs-bar{
    border-color: #2E5A6C !important;
  }
  .ivu-tabs-tab{
    background-color: @bg-light-tech-right;
    color: @font-color-base;
    border-color: @bg-light-tech-right;
  }
  .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
    background-color: #00253F !important;
    border-color: @font-color-base;
    color: @font-color-base;
  }
  .info{
    color: @font-color-base;
    border-color: @bg-light-tech-right;
  }
  .style-card{
    border-color: @bg-light-tech-right;
    color: @font-color-base;
  }
  .style-header{
    border:1px solid @bg-light-tech-right;
    border-radius: 5px 5px 0px 0px;
    background-color: rgba(57,109,131,0.1) !important;
    color: @font-color-base ;
  }
  .style-body{
    border:1px solid @bg-light-tech-right;
    background-color: rgba(57,109,131,0.1) !important;
    color: @font-color-base;
  }
  .style-dashed-left{
    border-left-color: @bg-light-tech-right;
  }
  .style-title{
    color: @font-color-base;
  }
  .style-border{
    border:1px solid @bg-light-tech-right;
    padding: 10px;
    margin: 0;
    margin-bottom: 10px !important;
  }
  .higher-search{
    background-color: rgba(57,109,131,0) !important;
    border:1px solid @bg-light-tech-right;
  }
  /* step步骤条样式 */
  .ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner{
    background-color: #619bb3 !important;
    border-color: #619bb3 !important;
  }
  .ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner>span{
    color: #02253E !important;
    font-weight: bold !important;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner{
    background-color: rgba(0,0,0,0) !important;
    border-color: @bg-light-tech-right;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner>span{
    color: #619bb3 !important;
  }
  .ivu-steps-item.ivu-steps-status-wait .ivu-steps-tail > i,.ivu-steps-item.ivu-steps-status-process .ivu-steps-tail > i{
    background-color: @bg-light-tech-right;
    margin-left: 15px !important;
    width: 95% !important;
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail > i,.ivu-steps-item.ivu-steps-status-process .ivu-steps-tail > i{
    background-color: @bg-light-tech-right;
    margin-left: 15px !important;
    width: 95% !important;
  }
  .theme1 .ivu-layout-content .ivu-icon.ivu-steps-icon{
    color: @font-color-base;
  }
  .ivu-steps .ivu-steps-title{
    color: #619bb3 !important;
    display: block;
    background-color: rgba(0,0,0,0);
  }
  .ivu-steps .ivu-steps-head{
    background-color: rgba(0,0,0,0);
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner{
    background-color: #619BB3 !important;
    border-color: #619BB3 !important;
  }
  .ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner>span{
    color: #02253E !important;
  }
  /* tree样式 */
  .tree-bar > div{
    border-color: @bg-light-tech-right;
  }
  .ivu-tree-title{
    color: @font-color-base;
  }
  .ivu-tree-title:hover{
    color: @font-color-base;
    background: @bg-light-tech-right;
  }
  .ivu-tree-title.ivu-tree-title-selected{
    color: @font-color-base;
    background: @bg-light-tech-right;
  }
  .ivu-select-dropdown *{
    color: @font-color-base;
  }
  a.drop-down,a.drop-down i.ivu-icon{
    color: @font-color-base;
  }
  /*字体颜色*/
  .whiteWord{
    color: @font-color-base;
  }

 .ivu-layout-content .ivu-icon  {
   color:rgba(56,129,229,1);
 }
 .ivu-checkbox-inner {
   background-color:@bg-deep-tech-right;
 }
  .ivu-form-item-content button:nth-child(1), .ivu-form-item-content button:nth-child(2) {
    background:rgba(15,89,190,1)!important;
  }
  //.ivu-card-body .operation button:nth-child(1), .ivu-card-body .operation button:nth-child(2) {
  //  background:rgba(15,89,190,1)!important;
  //}
  // Here are the variables to cover, such as:
  // @primary-color:#8c0776;
  //@background-color:#000000;

  // Base
  /*@body-background        : #001529;
  @component-background   : #001529;
  @input-bg                    : #001529;

  @body-background        : #001529;
  @component-background   : #001529;

  @layout-body-background      : #001529;

  @head-bg                      : #001529;
  @table-thead-bg               : #001529;
  @table-td-stripe-bg           : #001529;*/
}

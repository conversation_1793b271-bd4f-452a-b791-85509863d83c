const debounce = {
  inserted: function (fn, delay) {
    // eslint-disable-next-line no-redeclare
    var delay = delay || 200;
    var timer;
    return function () {
      var th = this;
      // eslint-disable-next-line prefer-rest-params
      var args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(function () {
        timer = null;
        fn.apply(th, args);
      }, delay);
    };
  },
};

export default debounce;
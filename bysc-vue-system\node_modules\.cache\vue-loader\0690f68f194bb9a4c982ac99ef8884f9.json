{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\mixins\\downLoadNotice.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\mixins\\downLoadNotice.vue", "mtime": 1745205562791}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./downLoadNotice.vue?vue&type=template&id=32de1e98&scoped=true\"\nimport script from \"./downLoadNotice.vue?vue&type=script&lang=js\"\nexport * from \"./downLoadNotice.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"32de1e98\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('32de1e98')) {\n      api.createRecord('32de1e98', component.options)\n    } else {\n      api.reload('32de1e98', component.options)\n    }\n    module.hot.accept(\"./downLoadNotice.vue?vue&type=template&id=32de1e98&scoped=true\", function () {\n      api.rerender('32de1e98', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/mixins/downLoadNotice.vue\"\nexport default component.exports"]}
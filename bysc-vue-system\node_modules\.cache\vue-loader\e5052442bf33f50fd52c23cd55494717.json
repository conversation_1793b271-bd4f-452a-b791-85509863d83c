{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\sider-trigger\\sider-trigger.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\sider-trigger\\sider-trigger.vue", "mtime": 1745205562764}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./sider-trigger.vue?vue&type=template&id=d9250ed8\"\nimport script from \"./sider-trigger.vue?vue&type=script&lang=js\"\nexport * from \"./sider-trigger.vue?vue&type=script&lang=js\"\nimport style0 from \"./sider-trigger.vue?vue&type=style&index=0&id=d9250ed8&lang=less\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('d9250ed8')) {\n      api.createRecord('d9250ed8', component.options)\n    } else {\n      api.reload('d9250ed8', component.options)\n    }\n    module.hot.accept(\"./sider-trigger.vue?vue&type=template&id=d9250ed8\", function () {\n      api.rerender('d9250ed8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/main/components/header-bar/sider-trigger/sider-trigger.vue\"\nexport default component.exports"]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\store.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\store.js", "mtime": 1745205562800}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport Vuex from 'vuex';\nimport commonStore from '@/service/store/common';\nVue.use(Vuex);\nvar vuexInstance = new Vuex.Store({\n  modules: {\n    common: commonStore\n  }\n  // ...commonStore,\n  // ...VUEX_DEFAULT_CONFIG\n});\nexport default vuexInstance;", {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "commonStore", "use", "vuexInstance", "Store", "modules", "common"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/store.js"], "sourcesContent": ["import Vue from 'vue';\r\nimport Vuex from 'vuex';\r\nimport commonStore from '@/service/store/common';\r\n\r\nVue.use(Vuex);\r\n\r\nconst vuexInstance = new Vuex.Store({\r\n  modules: {\r\n    common: commonStore\r\n  }\r\n  // ...commonStore,\r\n  // ...VUEX_DEFAULT_CONFIG\r\n});\r\nexport default vuexInstance;\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,WAAW,MAAM,wBAAwB;AAEhDF,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;AAEb,IAAMG,YAAY,GAAG,IAAIH,IAAI,CAACI,KAAK,CAAC;EAClCC,OAAO,EAAE;IACPC,MAAM,EAAEL;EACV;EACA;EACA;AACF,CAAC,CAAC;AACF,eAAeE,YAAY", "ignoreList": []}]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\element-ui.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\element-ui.js", "mtime": 1745205562794}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es7.object.values\";\nimport Vue from 'vue';\nimport { getCurrentVud } from '@/utils/vud';\n\n// 全局配置 el-upload 的默认 headers\nVue.prototype.$uploadHeaders = {\n  get headers() {\n    return {\n      Authorization: \"Bearer \" + Vue.prototype.$cookies.get(\"userToken\"),\n      TenantId: 0,\n      Vud: getCurrentVud()\n    };\n  }\n};\n\n// 全局 mixin 自动为 el-upload 添加 headers\nVue.mixin({\n  mounted: function mounted() {\n    var _this = this;\n    // 查找所有 el-upload 组件\n    var uploads = this.$refs ? Object.values(this.$refs).filter(function (ref) {\n      return ref && ref.$options && ref.$options.name === 'ElUpload';\n    }) : [];\n    uploads.forEach(function (upload) {\n      if (upload && !upload.headers) {\n        upload.headers = _this.$uploadHeaders.headers;\n      }\n    });\n  }\n});", {"version": 3, "names": ["<PERSON><PERSON>", "getCurrentVud", "prototype", "$uploadHeaders", "headers", "Authorization", "$cookies", "get", "TenantId", "<PERSON><PERSON>", "mixin", "mounted", "_this", "uploads", "$refs", "Object", "values", "filter", "ref", "$options", "name", "for<PERSON>ach", "upload"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/element-ui.js"], "sourcesContent": ["import Vue from 'vue';\r\nimport {getCurrentVud} from '@/utils/vud';\r\n\r\n// 全局配置 el-upload 的默认 headers\r\nVue.prototype.$uploadHeaders = {\r\n  get headers() {\r\n    return {\r\n      Authorization: \"Bearer \" + Vue.prototype.$cookies.get(\"userToken\"),\r\n      TenantId: 0,\r\n      Vud: getCurrentVud()\r\n    };\r\n  }\r\n};\r\n\r\n// 全局 mixin 自动为 el-upload 添加 headers\r\nVue.mixin({\r\n  mounted() {\r\n    // 查找所有 el-upload 组件\r\n    const uploads = this.$refs ? Object.values(this.$refs).filter(ref => ref && ref.$options && ref.$options.name === 'ElUpload') : [];\r\n    uploads.forEach(upload => {\r\n      if (upload && !upload.headers) {\r\n        upload.headers = this.$uploadHeaders.headers;\r\n      }\r\n    });\r\n  }\r\n});"], "mappings": ";;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,SAAQC,aAAa,QAAO,aAAa;;AAEzC;AACAD,GAAG,CAACE,SAAS,CAACC,cAAc,GAAG;EAC7B,IAAIC,OAAOA,CAAA,EAAG;IACZ,OAAO;MACLC,aAAa,EAAE,SAAS,GAAGL,GAAG,CAACE,SAAS,CAACI,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;MAClEC,QAAQ,EAAE,CAAC;MACXC,GAAG,EAAER,aAAa,CAAC;IACrB,CAAC;EACH;AACF,CAAC;;AAED;AACAD,GAAG,CAACU,KAAK,CAAC;EACRC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR;IACA,IAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACF,KAAK,CAAC,CAACG,MAAM,CAAC,UAAAC,GAAG;MAAA,OAAIA,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACC,QAAQ,CAACC,IAAI,KAAK,UAAU;IAAA,EAAC,GAAG,EAAE;IAClIP,OAAO,CAACQ,OAAO,CAAC,UAAAC,MAAM,EAAI;MACxB,IAAIA,MAAM,IAAI,CAACA,MAAM,CAAClB,OAAO,EAAE;QAC7BkB,MAAM,CAAClB,OAAO,GAAGQ,KAAI,CAACT,cAAc,CAACC,OAAO;MAC9C;IACF,CAAC,CAAC;EACJ;AACF,CAAC,CAAC", "ignoreList": []}]}
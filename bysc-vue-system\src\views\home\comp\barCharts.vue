<template>
  <div>
    <div :key="timer" ref="bar" :style="{width: width,height:height}"></div>
  </div>
</template>
<script>
import {on, off} from '@/utils/tools';
export default {
  name: 'barcharts',
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '35vh'
    },
    barXdata: Array,
    barYdata: Array
  },
  data() {
    return {
      timer: '',
      myChart: null,
    };
  },
  beforeDestroy() {
    off(window, 'resize', this.resize);
  },
  methods: {
    resize() {
      this.myChart.resize();
    },
    drawChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.bar);
      // 指定图表的配置项和数据
      let option = {
        tooltip: {
          showContent: true,
          trigger: 'axis',
          backgroundColor: 'rgba(8,36,68,.7)',
          color: '#fff',
          textStyle: {
            color: '#fff'
          },
        },
        textStyle: {
          'color': '#c0c3cd',
          'fontSize': 14
        },
        legend: {
          top: '4%',
          left: '75%',
          'itemWidth': 13,
          'itemHeight': 13,
          itemStyle: {
            color: '#18A4FF'
          },
          'icon': 'rect',
          'padding': 0,
          textStyle: {
            'color': '#c0c3cd',
            fontSize: 13,
            'padding': [2, 0, 0, 0]
          }
        },
        grid: {
          top: '23%',
          left: '14%',
          bottom: '25%',
          right: '10%'
        },
        xAxis: {
          splitLine: {show: false},
          nameTextStyle: {
            'color': '#c0c3cd',
            'padding': [0, 0, -10, 0],
            'fontSize': 14
          },
          'axisLine': {
            'show': false // 隐藏x轴线
          },
          axisLabel: {
            interval: 0,
            rotate: '0',
            formatter: function (value) {
              // if (value.length > 3) {
              //   return `${value.slice(0, 2)}...`;
              // }
              return value;
            },
            textStyle: {
              color: '#ccc' // 坐标轴字颜色
            },
            margin: 15
          },
          axisTick: {
            show: false // 隐藏X轴刻度
          },
          data: this.barXdata,
          type: 'category'
        },
        yAxis: {
          axisLine: {
            show: false, // 隐藏X轴轴线
            lineStyle: {
              color: '#11417a'
            }
          },
          axisTick: {
            show: false // 隐藏X轴刻度
          },
          axisLabel: {
            textStyle: {
              color: '#ccc'
            }
          },
          splitLine: { // 网格线
            'show': false
          }
        },
        series: [
          {
            data: this.barYdata,
            'type': 'bar',
            'showBackground': true,
            backgroundStyle: {
              color: '#fff'
            },
            'barMaxWidth': 'auto',
            // "barWidth": 7,
            'barWidth': 27,
            'itemStyle': {
              barBorderRadius: [3, 3, 2, 2], // 圆角（左上、右上、右下、左下）
              'color': {
                'x': 0,
                'y': 0,
                'x2': 0,
                'y2': 1,
                'type': 'linear',
                'global': false,
                'colorStops': [{
                  'offset': 0,
                  'color': '#78AEF9'
                }, {
                  'offset': 1,
                  'color': '#78AEF9'
                }]
              }
            },
            'label': {
              'show': true,
              'position': 'top',
              'distance': 10,
              'color': '#fff',
              'fontSize': 13
            }
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
      on(window, 'resize', this.resize);
    }
  },
  mounted() {
    // const that = this;
    // window.onresize = () => {
    //   return (() => {
    //     that.timer = new Date().getTime();
    //     setTimeout(() => {
    //       that.drawChart();
    //     }, 1000);
    //   })();
    // };
    // 加延迟防止图表溢出盒子
    setTimeout(() => {
      this.drawChart();
    }, 10);
  }
};
</script>

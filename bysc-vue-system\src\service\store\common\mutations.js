import _ from 'lodash';
const defaultState = {
  userInfo: {},
  department: {},
  unreadCount: 0,
  menuList: [],
  permissions: [],
  permissionList: [],
  systemConfig: {},
  isAdmin: false,
  isTenantMode: true,
  fullScreen: true,
  emediaModalVisibles: false,
  loadingShow: false,
  sysVersion: '',
  progressList: [],
  processConditions: [], // processConditions 用于传递流程图需要的条件
  formItemList: [], // 流程节点表单权限控制——组件列表,
};
let hasCondition = (state, formId, needIndex = false) => {
  let index = state.processConditions.findIndex(d => d.formId === formId);
  return needIndex ? index : index > -1;
};
export const state = _.cloneDeep(defaultState);
export const mutations = {
  // 所有mutations中的方法的第一个参数一定是state变量，用来进行对state中的状态的操作
  // 第二个参数是可选参数，用于调用该 mutations 方法的时候传参
  initPConditions(state, data) {
    state.processConditions = data;
  },
  addPCondition(state, data) {
    if (data.formId === null || data.formId === undefined) {
      return;
    }
    if (!hasCondition(state, data.formId)) {
      state.processConditions.unshift(data);
    }
  },
  delPCondition(state, formId) {
    if (formId === null || formId === undefined) {
      return;
    }
    let index = hasCondition(state, formId, true);
    let cons = state.processConditions;
    index > -1 && cons.splice(index, 1);
  },
  //  * 清除所有的条件
  clearPCondition(state) {
    state.processConditions = [];
  },
  updateFormItemList(state, list) {
    state.formItemList = list;
  },
  setUserInfo(state, userInfo) {
    state.userInfo = userInfo;
  },
  setDepartment(state, department) {
    state.department = department;
  },
  setMenuList(state, menuList) {
    state.menuList = menuList;
  },
  setPermissions(state, permissions) {
    state.permissions = permissions;
  },
  setPermissionList(state, permissionList) {
    state.permissionList = permissionList;
  },
  setVersion(state, version) {
    state.sysVersion = version;
  },
  setSystemConfig(state, config) {
    state.systemConfig = config;
  },
  setEmediaModalVisibles(state, emediaModalVisibles) {
    state.emediaModalVisibles = emediaModalVisibles;
  },
  setIsAdmin(state, roleList) {
    if (Array.isArray(roleList)) {
      state.isAdmin = roleList.some(e => {
        return e.code === 'ROLE_ADMIN';
      });
    }
  },
  setFullScreen(state, fullScreen) {
    state.fullScreen = fullScreen;
  },
  setProgressList(state, progressList) {
    state.progressList = progressList;
  },
  clearStore(state, exceptList) {
    Object.keys(state).every(e => {
      if (Array.isArray(exceptList) && exceptList.indexOf(e) !== -1) {
        return true;
      }
      if (typeof defaultState[e] !== 'undefined') {
        state[e] = defaultState[e];
      } else {
        state[e] = null;
      }
      return true;
    });
  },

  setProgress(state, progressObj) { // 修改进度列表
    if (state.progressList.length) { // 如果进度列表存在
      if (state.progressList.find(item => item.path == progressObj.path)) { // 前面说的path时间戳是唯一存在的，所以如果在进度列表中找到当前的进度对象
        state.progressList.find(item => item.path == progressObj.path).progress = progressObj.progress; // 改变当前进度对象的progress
      }
    } else {
      state.progressList.push(progressObj); // 当前进度列表为空，没有下载任务，直接将该进度对象添加到进度数组内
    }
  },
  delProgress(state, props) {
    state.progressList.splice(state.progressList.findIndex(item => item.path == props), 1); // 删除进度列表中的进度对象
  },
};

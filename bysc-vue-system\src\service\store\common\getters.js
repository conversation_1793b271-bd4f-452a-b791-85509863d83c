import {cloneDeep} from 'lodash';

export default {
  userInfo: state => cloneDeep(state.userInfo),
  department: state => cloneDeep(state.department),
  menuList: state => cloneDeep(state.menuList),
  isAdmin: state => cloneDeep(state.isAdmin),
  fullScreen: state => cloneDeep(state.fullScreen),
  systemConfig: state => cloneDeep(state.systemConfig),
  havePermission: state => routeName => {
    return state.permissionList.some(permission => {
      return permission.code.toUpperCase() === routeName.toUpperCase();
    });
  }
};

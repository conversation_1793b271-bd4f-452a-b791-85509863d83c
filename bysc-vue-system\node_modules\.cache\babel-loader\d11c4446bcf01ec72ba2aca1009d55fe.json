{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\codeGeneration.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\codeGeneration.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'table-list-v2',\n  // 搜索\n  method: 'GET',\n  path: '/generator/code/generator/engine/table/list/v2'\n}, {\n  name: 'generate-code',\n  // 生成\n  method: 'POST',\n  path: '/generator/code/generator/engine/generate/code'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/codeGeneration.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'table-list-v2', // 搜索\r\n    method: 'GET',\r\n    path: '/generator/code/generator/engine/table/list/v2'\r\n  },\r\n  {\r\n    name: 'generate-code', // 生成\r\n    method: 'POST',\r\n    path: '/generator/code/generator/engine/generate/code'\r\n  },\r\n];\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
<template>
  <div ref="tabs" class="tab-slider" v-if="tabList.length">
    <div v-for="(item, index) in tabList" :key="'tab' + index" class="tabChild" :class="[current===index?'tabChildBorder':'']" @click="changeCurrent(item,index)">
      <span>{{ item.label }}</span>
    </div>
  </div>
</template>
<script>
export default {
  // tablist格式为[{value:'',label:'',其他字段...}],点击完tab之后将会将对应对象传到父组件
  name: 'tabSlider',
  props: {
    tabList: Array
  },
  data() {
    return {
      current: 0
    };
  },
  mounted() {
  },
  watch: {},
  methods: {
    changeCurrent(item, index) {
      this.current = index;
      this.$emit('tabInfo', item);
    }
  }
};
</script>
<style scoped>
.tab-slider {
  width: 100%;
  height: 44px;
  padding: 0 5px;
  white-space: nowrap;
  border-bottom: 1px solid #ebe8e8;
  overflow-x: scroll;
  overflow-y: hidden;
}
.tabChild {
  width: auto;
  padding: 10px 15px;
  display: inline-block;
}
.tabChildBorder{
  color: #2d8cf0;
  border-bottom: 2px solid #2d8cf0;
}
.tabChild1 {
  width: auto;
  padding: 10px 15px;
  color: #2d8cf0;
  display: inline-block;
}
/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 16px;
  height: 0px;
  background-color: #f5f5f5;
}
/*定义滚动条轨道
 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}
/*定义滑块
 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #555;
}
</style>

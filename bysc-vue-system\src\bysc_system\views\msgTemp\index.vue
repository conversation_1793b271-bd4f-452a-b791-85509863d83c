
<template>
  <div>
    <el-card class="box-card">
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
      <div v-for="(item,index) in body" :key="index+'form'">
        <el-divider content-position="left"><span style="font-weight:bold">{{ item.title }}</span></el-divider>
        <div v-for="(fitem,findex) in item.body" :key="'fitem'+findex">
          <el-form-item label-width="126px" :label="fitem.label" :prop="fitem.name" v-if="getStatus(fitem.visibleOn)" :rules="[{ required: !!fitem.required, message: '此项为必填项', trigger: 'blur' }]">
            <div v-if="fitem.type==='input-text'">
              <el-input style="width:300px" :placeholder="fitem.placeholder" :disabled="fitem.disabled?fitem.disabled:false" size="small" v-model="formData[fitem.name]"></el-input>
            </div>
            <div v-if="fitem.type==='radios'">
              <el-radio-group v-model="formData[fitem.name]">
                <el-radio v-for="(option,oindex) in fitem.options" :key="oindex+'op'" :label="option.value">{{ option.label}}</el-radio>
              </el-radio-group>
            </div>
            <div v-if="fitem.type==='select'">
              <el-select style="width:300px" size="small" :disabled="fitem.disabled?fitem.disabled:false" v-model="formData[fitem.name]" :placeholder="fitem.placeholder">
                <el-option
                  v-for="item in getnewoptions(fitem)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div v-if="fitem.type==='input-table'">
              <el-table
                :data="formData[fitem.name]"
                style="width: 100%">
                <el-table-column
                  v-for="(column,index) in fitem.columns"
                  :prop="column.name"
                  :label="column.label"
                  min-width="180">
                  <template slot-scope="scope">
                    <el-input v-if="formData[fitem.name]" size="small" v-model="formData[fitem.name][tableIndex][column.name]" :placeholder="column.placeholder"></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="action"
                  min-width="100"
                  label="操作">
                    <template slot="header" slot-scope="scope">
                      <el-button size="small" type="primary" @click="addTables(fitem)">添加</el-button>
                    </template>
                    <a style="margin-left:8px">编辑</a>
                    <a style="margin-left:8px">保存</a>
                    <a style="margin-left:8px">删除</a>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </div>
      </div>
      <el-form-item>
        <el-button style="float:right" size="small" type="primary" @click="submitForm('ruleForm')">保存</el-button>
      </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      tableData:[],
      tableField:'',
      tableIndex:0,
      ruleForm: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        rules: {
        },
      body: [
        {
          type: "fieldSet",
          title: "基本配置",
          body: [
            {
              type: "input-text",
              label: "模板名称",
              name: "name",
              size: "md",
              required: true,
            },
            {
              type: "input-text",
              label: "模板业务方",
              name: "proposer",
              size: "md",
              mode: "",
              description: "",
            },
            {
              type: "radios",
              label: "接收者Id类型",
              name: "idType",
              options: [
                {
                  label: "用户ID",
                  value: "10",
                },
                {
                  label: "设备号",
                  value: "20",
                },
                {
                  label: "手机号",
                  value: "30",
                },
                {
                  label: "openID",
                  value: "40",
                },
                {
                  label: "邮箱地址",
                  value: "50",
                },
                {
                  label: "企业微信userId",
                  value: "60",
                },
                {
                  label: "钉钉userId",
                  value: "70",
                },
                {
                  label: "推送通知栏cid",
                  value: "80",
                },
                {
                  label: "飞书userId",
                  value: "90",
                },
              ],
              required: true,
            },
            {
              type: "radios",
              label: "消息类型",
              name: "msgType",
              options: [
                {
                  label: "通知类",
                  value: "10",
                },
                {
                  label: "营销类",
                  value: "20",
                },
                {
                  label: "验证码",
                  value: "30",
                },
              ],
              required: true,
            },
            {
              type: "radios",
              label: "屏蔽类型",
              name: "shieldType",
              options: [
                {
                  label: "夜间不屏蔽",
                  value: "10",
                },
                {
                  label: "夜间屏蔽",
                  value: "20",
                },
                {
                  label: "夜间屏蔽(次日早上9点发送)",
                  value: "30",
                },
              ],
              required: true,
            },
            {
              type: "radios",
              label: "消息模板类型",
              name: "templateType",
              options: [
                {
                  label: "定时",
                  value: "10",
                },
                {
                  label: "实时",
                  value: "20",
                },
              ],
              required: true,
            },
            {
              type: "input-text",
              label: "cron表达式",
              name: "expectPushTime",
              size: "md",
              visibleOn: "this.formData.templateType == 10",
              clearValueOnHidden: false,
              visible: "",
              required: true,
              description:
                '1、<font color="#008252">生成cron表达式页面：<a href="https://www.matools.com/cron" target="_blank">https://www.matools.com/cron</a></font>    2、<font color="#008252">需要【立即发送】时填0</font>',
              placeholder: "需要【立即发送】时填0",
            },
            {
              type: "input-file",
              name: "file",
              visibleOn: "this.formData.templateType == 10",
              required: true,
              label: "人群文件上传",
              accept: ".csv",
              receiver: "${ls:backend_url}/messageTemplate/upload",
              remark: null,
              labelRemark: null,
              mode: "",
              description:
                '1、<font color="#008252">文件格式：csv文件</font> 2、<font color="#008252">内容格式：列名userId,占位符变量1,占位符变量2</font>',
              autoFill: {
                cronCrowdPath: "${value}",
              },
            },
            {
              type: "input-text",
              name: "cronCrowdPath",
              size: "md",
              visibleOn: "this.formData.templateType == 10",
              clearValueOnHidden: false,
              visible: "",
              required: true,
              readOnly: true,
              label: "人群文件路径",
              placeholder: "上传成功后自动填写(无须编辑)",
            },
          ],
          bodyClassName: "r-3x w-full text-lg r font-bold",
          className: "",
        },
        {
          type: "fieldSet",
          title: "渠道信息",
          body: [
            {
              name: "sendChannel",
              label: "发送渠道",
              type: "radios",
              options: [
                {
                  label: "PUSH通知栏",
                  value: "20",
                },
                {
                  label: "短信",
                  value: "30",
                },
                {
                  label: "邮箱",
                  value: "40",
                },
                {
                  label: "微信服务号（模板消息）",
                  value: "50",
                },
                {
                  label: "微信小程序（订阅消息）",
                  value: "60",
                },
                {
                  label: "企业微信",
                  value: "70",
                },
                {
                  label: "钉钉群机器人",
                  value: "80",
                },
                {
                  label: "钉钉工作消息",
                  value: "90",
                },
                {
                  label: "企业微信机器人",
                  value: "100",
                },
                {
                  label: "飞书机器人",
                  value: "110",
                },
              ],
              required: true,
            },
            {
              type: "hidden",
              name: "sendAccount",
              value: "0",
              visibleOn: "this.formData.sendChannel == 30",
            },
            {
              type: "input-text",
              label: "短信账号",
              disabled: true,
              value: "根据【消息类型】按流量分配到对应短信渠道",
              placeholder: "根据【消息类型】按流量分配到对应短信渠道",
              visibleOn: "this.formData.sendChannel == 30",
              clearValueOnHidden: false,
              required: false,
              size: "lg",
            },
            {
              name: "content",
              type: "input-text",
              label: "短信内容",
              required: true,
              visibleOn: "this.formData.sendChannel == 30",
              mode: "",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "input-text",
              label: "短信链接",
              name: "url",
              visibleOn: "this.formData.sendChannel == 30",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}，最好输入短链接",
            },
            {
              type: "select",
              label: "发送账号",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=20",
              visibleOn: "this.formData.sendChannel == 20",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              name: "title",
              type: "input-text",
              label: "发送标题",
              required: true,
              visibleOn: "this.formData.sendChannel == 20",
              mode: "",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$title}",
            },
            {
              name: "content",
              type: "input-text",
              label: "发送内容",
              required: true,
              visibleOn: "this.formData.sendChannel == 20",
              mode: "",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "input-text",
              label: "发送链接",
              name: "url",
              required: true,
              visibleOn: "this.formData.sendChannel == 20",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "select",
              label: "服务号账号",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=50",
              visibleOn: "this.formData.sendChannel == 50",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "select",
              label: "选择模板",
              name: "templateId",
              source:
                "${ls:backend_url}/officialAccount/template/list?id=${sendAccount}",
              visibleOn: "this.formData.sendChannel == 50",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
              initFetchOn: "data.sendAccount",
            },
            {
              type: "radios",
              name: "linkType",
              label: "跳转落地页",
              visibleOn: "this.formData.sendChannel == 50",
              options: [
                {
                  label: "HTTP链接",
                  value: "10",
                },
                {
                  label: "小程序",
                  value: "20",
                },
              ],
              required: true,
            },
            {
              type: "input-text",
              label: "发送链接",
              name: "url",
              required: true,
              visibleOn: "this.formData.sendChannel == 50 && this.formData.linkType==10",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "select",
              label: "跳转小程序",
              name: "miniProgramId",
              required: true,
              visibleOn: "this.formData.sendChannel == 50 && this.formData.linkType==20",
              options: [
                {
                  label: "暂无可用小程序",
                  value: "10",
                },
              ],
              size: "lg",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "发送链接",
              name: "path",
              required: true,
              visibleOn: "this.formData.sendChannel == 50 && this.formData.linkType==20",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "service",
              schemaApi:
                "${ls:backend_url}/officialAccount/detailTemplate?id=${sendAccount}&wxTemplateId=${templateId}",
              visibleOn: "this.formData.sendChannel == 50",
              description:
                '<font color="#990000">请点击【新增】填入文案</font>',
            },

            {
              type: "select",
              label: "小程序账号",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=60",
              visibleOn: "this.formData.sendChannel == 60",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "select",
              label: "选择模板",
              name: "templateId",
              source:
                "${ls:backend_url}/miniProgram/template/list?id=${sendAccount}",
              visibleOn: "this.formData.sendChannel == 60",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
              initFetchOn: "data.sendAccount",
            },
            {
              type: "input-text",
              label: "发送链接",
              name: "page",
              required: true,
              visibleOn: "this.formData.sendChannel == 60 ",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "service",
              schemaApi:
                "${ls:backend_url}/miniProgram/detailTemplate?id=${sendAccount}&wxTemplateId=${templateId}",
              visibleOn: "this.formData.sendChannel == 60",
              description:
                '<font color="#990000">请点击【新增】填入文案</font>',
            },

            {
              type: "select",
              label: "邮件账号",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=40",
              visibleOn: "this.formData.sendChannel == 40",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "input-text",
              label: "邮件标题",
              name: "title",
              required: true,
              visibleOn: "this.formData.sendChannel == 40",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$title}",
            },
            {
              type: "input-text",
              label: "邮件内容",
              name: "content",
              required: true,
              visibleOn: "this.formData.sendChannel == 40",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}，可输入HTML",
            },
            {
              type: "select",
              label: "飞书机器人",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=110",
              visibleOn: "this.formData.sendChannel == 110",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              name: "sendType",
              label: "发送类型",
              type: "radios",
              options: [
                {
                  label: "文本",
                  value: "10",
                },

                {
                  label: "图片",
                  value: "100",
                },
                {
                  label: "跳转卡片",
                  value: "120",
                },
                {
                  label: "富文本",
                  value: "150",
                },
                {
                  label: "群卡片",
                  value: "160",
                },
              ],
              visibleOn: "this.formData.sendChannel == 110",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "input-text",
              label: "飞书内容",
              name: "content",
              required: true,
              visibleOn: "this.formData.sendChannel == 110 && this.formData.sendType==10 ",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "input-text",
              label: "图片标识",
              name: "mediaId",
              required: true,
              visibleOn: "this.formData.sendChannel == 110 && this.formData.sendType==100 ",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$mediaId}",
            },
            {
              type: "select",
              label: "企业微信应用",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=70",
              visibleOn: "this.formData.sendChannel == 70",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "select",
              label: "钉钉群机器人",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=80",
              visibleOn: "this.formData.sendChannel == 80",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "select",
              label: "钉钉应用",
              name: "sendAccount",
              source:
                "${ls:backend_url}/account/queryByChannelType?channelType=90",
              visibleOn: "this.formData.sendChannel == 90",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              name: "sendType",
              label: "发送类型",
              type: "radios",
              options: [
                {
                  label: "文本",
                  value: "10",
                },
                {
                  label: "语音",
                  value: "20",
                },
                {
                  label: "视频",
                  value: "30",
                },
                {
                  label: "图文",
                  value: "40",
                },
                {
                  label: "文本卡片",
                  value: "50",
                },
                {
                  label: "文件",
                  value: "60",
                },
                {
                  label: "小程序通知",
                  value: "70",
                },
                {
                  label: "markdown类型",
                  value: "80",
                },
                {
                  label: "模板卡片",
                  value: "90",
                },
                {
                  label: "图片",
                  value: "100",
                },
                {
                  label: "链接消息",
                  value: "110",
                },
              ],
              visibleOn: "this.formData.sendChannel == 70  ",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              name: "sendType",
              label: "发送类型",
              type: "radios",
              options: [
                {
                  label: "文本(text)",
                  value: "10",
                },
                {
                  label: "语音(voice)",
                  value: "20",
                },
                {
                  label: "文件(file)",
                  value: "60",
                },
                {
                  label: "markdown类型(markdown)",
                  value: "80",
                },
                {
                  label: "图片(image)",
                  value: "100",
                },
                {
                  label: "链接消息(link)",
                  value: "110",
                },
                {
                  label: "卡片消息(action_card)",
                  value: "120",
                },
                {
                  label: "OA消息(oa)",
                  value: "130",
                },
              ],
              visibleOn: "this.formData.sendChannel==90 ",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              name: "sendType",
              label: "发送类型",
              type: "radios",
              options: [
                {
                  label: "文本(text)",
                  value: "10",
                },
                {
                  label: "图文(FeedCard)",
                  value: "40",
                },
                {
                  label: "markdown类型(markdown)",
                  value: "80",
                },
                {
                  label: "链接消息(links)",
                  value: "110",
                },
                {
                  label: "卡片消息(actionCard)",
                  value: "120",
                },
              ],
              visibleOn: "this.formData.sendChannel == 80",
              clearValueOnHidden: false,
              required: true,
              size: "lg",
            },
            {
              type: "input-text",
              label: "企业微信内容",
              name: "content",
              required: true,
              visibleOn: "this.formData.sendChannel == 70 && this.formData.sendType== 10",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "input-text",
              label: "钉钉标题",
              name: "title",
              required: true,
              visibleOn:
                "this.formData.sendChannel == 80 && (this.formData.sendType== 80 || this.formData.sendType== 110 || this.formData.sendType== 120 )",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$title}",
            },
            {
              type: "input-text",
              label: "钉钉发送内容",
              name: "content",
              required: true,
              visibleOn:
                "this.formData.sendChannel == 80 && (this.formData.sendType== 10 || this.formData.sendType== 80|| this.formData.sendType== 110 || this.formData.sendType== 120)",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "radios",
              label: "按钮布局",
              name: "btnOrientation",
              visibleOn: "this.formData.sendChannel == 80 && (this.formData.sendType== 120)",
              options: [
                {
                  label: "按钮竖直排列",
                  value: "0",
                },
                {
                  label: "按钮横向排列",
                  value: "1",
                },
              ],
            },
            {
              type: "input-table",
              name: "btns",
              addable: true,
              editable: true,
              visibleOn: "this.formData.sendChannel == 80 && ( this.formData.sendType== 120)",
              columns: [
                {
                  name: "title",
                  label: "标题",
                  placeholder: "可用占位符{$title}",
                },
                {
                  name: "actionURL",
                  label: "跳转链接",
                  placeholder: "可用占位符{$actionURL}",
                },
              ],
            },
            {
              type: "input-text",
              label: "钉钉发送链接",
              name: "url",
              required: false,
              visibleOn: "this.formData.sendChannel == 80 && ( this.formData.sendType== 110)",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "input-text",
              label: "钉钉图片链接",
              name: "picUrl",
              visibleOn: "this.formData.sendChannel == 80 && ( this.formData.sendType== 110)",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$picUrl}",
            },
            {
              type: "input-table",
              name: "feedCards",
              addable: true,
              editable: true,
              visibleOn: "this.formData.sendChannel == 80 && ( this.formData.sendType== 40)",
              columns: [
                {
                  name: "title",
                  label: "标题",
                  placeholder: "可用占位符{$content}",
                },
                {
                  name: "messageURL",
                  label: "跳转链接",
                  placeholder: "可用占位符{$messageURL}",
                },
                {
                  name: "picURL",
                  label: "图片链接",
                  placeholder: "可用占位符{$picURL}",
                },
              ],
            },
            {
              type: "input-text",
              label: "消息标题",
              name: "title",
              visibleOn:
                "this.formData.sendChannel == 90 && (this.formData.sendType== 110 || this.formData.sendType== 80 ||this.formData.sendType== 120 )",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$title}",
            },
            {
              type: "input-text",
              label: "消息内容",
              name: "content",
              required: true,
              visibleOn:
                "this.formData.sendChannel == 90 && (this.formData.sendType== 10 || this.formData.sendType== 110 || this.formData.sendType== 80 ||this.formData.sendType== 120)",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$content}",
            },
            {
              type: "input-text",
              label: "语音Id",
              name: "mediaId",
              required: true,
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 20",
              size: "lg",
              placeholder: "可用占位符{$mediaId}",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "语音时长",
              name: "duration",
              required: true,
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 20",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "正整数，小于60，表示音频时长",
            },
            {
              type: "input-text",
              label: "文件Id",
              name: "mediaId",
              required: true,
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 60",
              size: "lg",
              placeholder: "可用占位符{$mediaId}",
              clearValueOnHidden: false,
            },
            {
              type: "radios",
              label: "按钮布局",
              name: "btnOrientation",
              visibleOn: "this.formData.sendChannel == 90 && (this.formData.sendType== 120)",
              options: [
                {
                  label: "按钮竖直排列",
                  value: "0",
                },
                {
                  label: "按钮横向排列",
                  value: "1",
                },
              ],
            },
            {
              type: "input-table",
              name: "btns",
              addable: true,
              editable: true,
              visibleOn: "this.formData.sendChannel == 90 && ( this.formData.sendType== 120)",
              columns: [
                {
                  name: "title",
                  label: "标题",
                  placeholder: "可用占位符{$title}",
                },
                {
                  name: "action_url",
                  label: "跳转链接",
                  placeholder: "可用占位符{$action_url}",
                },
              ],
            },
            {
              type: "input-text",
              label: "头部标题",
              name: "dingDingOaHeadTitle",
              required: false,
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 130",
              size: "lg",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "头部标题颜色",
              name: "dingDingOaHeadBgColor",
              required: true,
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 130",
              size: "lg",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "页面跳转链接",
              name: "url",
              required: true,
              visibleOn:
                "this.formData.sendChannel == 90 && (this.formData.sendType== 110 || this.formData.sendType== 130)",
              size: "lg",
              clearValueOnHidden: false,
              placeholder: "可用占位符{$url}",
            },
            {
              type: "input-text",
              label: "正文标题",
              name: "dingDingOaTitle",
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 130",
              size: "lg",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "正文内容",
              name: "dingDingOaContent",
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 130",
              size: "lg",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "图片Id",
              name: "mediaId",
              visibleOn:
                "this.formData.sendChannel == 90 && (this.formData.sendType== 100 || this.formData.sendType== 110|| this.formData.sendType== 130)",
              size: "lg",
              placeholder: "可用占位符{$mediaId}",
              clearValueOnHidden: false,
            },
            {
              type: "input-text",
              label: "作者名称",
              name: "dingDingOaAuthor",
              visibleOn: "this.formData.sendChannel == 90 && this.formData.sendType== 130",
              size: "lg",
              clearValueOnHidden: false,
            },
          ],
          className: "",
          bodyClassName: "r-3x w-full text-lg r font-bold",
        },
      ],
      formData:{}
    };
  },
  watch: {},
  mounted() {
    this.formData = {}
    this.body.forEach(e=>{
      e.body.forEach(item=>{
        this.$set(this.formData,item.name,null)
      })
    })
  },
  methods: {
    change(){
    	this.$forceUpdate();  //强制刷新
    },
    addTables(e){
      console.log(e,this.formData[e.name],'这是啥');
      if (!this.formData[e.name]) {
        this.formData[e.name]=[]
        this.formData[e.name].push({title:'',actionURL:''})
      }else{
        this.formData[e.name].push({title:'',actionURL:''})
      }
      this.tableIndex = this.formData[e.name].length-1
    },
    getnewoptions(e){
      console.log(e,'获取select下拉选项--后期从接口获取');
      return e.options?e.options:[{
        label:'账号1',
        value:1
      }]
    },
    getStatus(e){
      if (!e) {
        return true
      }else{
        return eval(e)
      }
    },
    submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            console.log(this.formData);
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
  },
};
</script>
<style lang="less" scoped></style>

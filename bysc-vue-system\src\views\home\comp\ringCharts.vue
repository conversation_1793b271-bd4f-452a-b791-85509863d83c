<!--
 * @Author: your name
 * @Date: 2022-04-22 11:04:11
 * @LastEditTime: 2022-06-06 11:12:23
 * @LastEditors: czw
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \kdsp_vue\src\views\home\comp\ringCharts.vue
-->
<template>
  <div>
    <div ref="ring" :key="timer" :style="{width: width,height:height}"></div>
  </div>
</template>
<script>
import {on, off} from '@/utils/tools';
export default {
  name: 'RingCharts',
  props: {
    title: String,
    id: String,
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    ringData: Array,
    allCount: Number,
    name: String
  },
  data() {
    return {
      timer: '',
      myChart: null
    };
  },
  beforeD<PERSON><PERSON>() {
    off(window, 'resize', this.resize);
  },
  methods: {
    resize() {
      this.myChart.resize();
    },
    drawChart() {
      let showData = [];
      if (!this.ringData.length) {
        return false;
      }
      this.ringData.forEach(e => {
        if (Number(e.value)) {
          showData.push(e);
        }
      });
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.ring);
      // 指定图表的配置项和数据
      let option = {
        title: [
          {
            text: this.title,
            x: 'center',
            top: '52%',
            textStyle: {
              color: '#333',
              fontSize: 16,
              fontWeight: '400',
            },
          },
          {
            text: this.allCount,
            x: 'center',
            top: '40%',
            textStyle: {
              fontSize: 16,
              color: '#333',
              foontWeight: '400',
            },
          },
        ],
        color: ['#62A7FF', '#CD9DFF', '#58DEB6', '#FFB35E', '#CD9DFF', '#62A7FF', '#FBC8C1', '#EEEEEE', '#d48722', '#4fd437', '#3d6bd4', '#a720d4', '#d415a7', '#6ebbd4', '#81ecec'],
        tooltip: {
          trigger: 'item',
        },
        // legend: {
        //   top: '5%',
        //   left: 'center'
        // },
        series: [
          {
            name: this.name,
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: true, // 避免label重叠
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: showData.length > 1 ? '2' : '0'
            },
            label: {
              show: true,
              formatter: function (value) {
                if (value.value == 0) {
                  return '';
                }
                return value.name;
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.ringData
          }
        ]
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
      on(window, 'resize', this.resize);
    }
  },
  mounted() {
    // 加延迟防止图表溢出盒子
    setTimeout(() => {
      this.drawChart();
    }, 10);
  }
};
</script>

export default [
  // menu
  {
    name: 'resourcePage',
    method: 'POST',
    path: '/system/resource/page'
  },
  {
    name: 'resourceGet',
    method: 'GET',
    path: '/system/resource/get'
  },
  {
    name: 'resourceDelete',
    method: 'OTHER',
    path: '/system/resource/delete'
  },
  {
    name: 'org-user-tree',
    method: 'POST',
    path: '/system/user/org-user-tree'
  },
  {
    name: 'getPremissTree',
    method: 'GET',
    path: '/system/resource/tree'
  },
  {
    name: 'getParamList',
    method: 'GET',
    path: '/base/dict/list'
  },
  {
    name: 'getTreeList',
    method: 'GET',
    path: '/system/resource/list'
  },
  {
    name: 'addMenu',
    method: 'POST',
    path: '/system/resource/save'
  },
  {
    name: 'changePwd', // 管理员修改用户密码
    method: 'POST',
    path: '/system/user/password/reset'
  },
  {
    name: 'changeSelfPwd', // 用户修改密码
    method: 'POST',
    path: '/system/user/password/change'
  },
  // role
  {
    name: 'rolePage', // 角色分页
    method: 'POST',
    path: '/system/role/page'
  },
  {
    name: 'roleresourcesave', // 角色绑定资源
    method: 'POST',
    path: '/system/role/resource/save'
  },
  {
    name: 'getResourceIds', // 通过角色Id获取对应被绑定资源的ids
    method: 'OTHERGET',
    path: '/system/role/resource'
  },
  {
    name: 'roleGet',
    method: 'GET',
    path: '/system/role/get'
  },
  {
    name: 'roleList',
    method: 'GET',
    path: '/system/role/list'
  },
  {
    name: 'roleDelete', // 删除角色
    method: 'OTHER',
    path: '/system/role/delete'
  },
  {
    name: 'addRole', // 添加角色
    method: 'POST',
    path: '/system/role/save'
  },
  // user
  {
    name: 'userPage', // 用户分页
    method: 'POST',
    path: '/system/user/page'
  },
  {
    name: 'userList', // 用户分页
    method: 'POST',
    path: '/system/user/list'
  },
  {
    name: 'userGet',
    method: 'GET',
    path: '/system/user/get'
  },
  {
    name: 'userDelete', // 删除用户
    method: 'OTHER',
    path: '/icb/personUser/delete'
  },
  {
    name: 'addUser', // 添加用户
    method: 'POST',
    path: '/icb/personUser/save'
  },
  // dept
  {
    name: 'organizationTree', // 获取组织树
    method: 'GET',
    path: '/system/organization/tree'
  },
  {
    name: 'organizationPage', // 组织分页
    method: 'POST',
    path: '/system/organization/page'
  },
  {
    name: 'organizationGet',
    method: 'GET',
    path: '/system/organization/get'
  },
  {
    name: 'organizationDelete', // 删除组织
    method: 'OTHER',
    path: '/system/organization/delete'
  },
  {
    name: 'addOrganization', // 添加组织
    method: 'POST',
    path: '/system/organization/save'
  },
  {
    name: 'revoke', // 用户解除角色
    method: 'POST',
    path: '/system/user/role/revoke'
  },
  {
    name: 'get-unread-num',
    method: 'GET',
    path: '/mc/notification/get-unread-num'
  },
  {
    name: 'lazyorgusertree',
    method: 'POST_FORM',
    path: '/system/user/lazy-org-user-tree'
  },
  {
    name: 'datapermission', // 设置数据权限
    method: 'POST',
    path: '/system/data/permission/save'
  },
  {
    name: 'permissionquery',
    method: 'GET',
    path: '/system/data/permission/query'
  },
  {
    name: 'getAppLists', // 获取应用app
    method: 'GET',
    path: '/system/app/list'
  },
  {
    name: 'roleGrantedApps', // 查询角色已授权应用列表
    method: 'GET',
    path: '/system/app/role-granted-apps'
  },
  {
    name: 'saveRoleApp', // 角色授权应用
    method: 'POST',
    path: '/system/app/save-role-app'
  },
  {
    name: 'getManpowerUrl', // 获取人力资源
    method: 'GET',
    path: '/syn/jump/index'
  },
  {
    name: 'get-by-key',
    method: 'GET',
    path: '/system/prop/comm/get-by-key'
  },
  {
    name: 'my-granted-apps', // 获得可切换系统
    method: 'GET',
    path: '/system/app/my-granted-apps'
  },
  {
    name: 'sim-pas-url', // 获取财务系统
    method: 'GET',
    path: '/kingdee/sso/sim-pas-url'
  },
  {
    name: 'batch-save-user-role', // 批量添加用户角色数据
    method: 'POST',
    path: '/system/user/batch-save-user-role'
  },
  {
    name: 'system-about', // 获取系统关于
    method: 'OTHERGET',
    path: '/system/sys/about/me'
  },
  {
    name: 'costCenter-page', // 查询成本中心管理分页
    method: 'POST',
    path: '/syn/financeCostCenter/page'
  },
  {
    name: 'costCenter-list', // 查询成本中心管理分页
    method: 'POST',
    path: '/syn/financeCostCenter/list'
  },
  {
    name: 'costCenter-list-new', // 查询成本中心管理列表
    method: 'GET',
    path: '/syn/financeCostCenter/get-finance-center'
  },
  {
    name: 'my-cost-center-for-add-monthly-data', // 查询成本中心管理分页
    method: 'POST',
    path: '/syn/financeCostCenter/my-cost-center-for-add-monthly-data'
  },
  {
    name: 'costCenter-save', // 保存成本中心管理
    method: 'POST',
    path: '/syn/financeCostCenter/save'
  },
  {
    name: 'costCenter-reset', // 重置成本中心管理
    method: 'OTHERGET',
    path: '/syn/financeCostCenter/reset'
  },
  {
    name: 'costCenter-sync', // 同步成本中心管理
    method: 'GET',
    path: '/syn/financeCostCenter/synchronization'
  },
  {
    name: 'costCenter-dts', // 查询成本中心管理详情
    method: 'OTHERGET',
    path: '/syn/financeCostCenter/get'
  },
  {
    name: 'costProject-page', // 查询工号管理分页
    method: 'POST',
    path: '/syn/financeCostProject/page'
  },
  {
    name: 'costProject-list', // 查询工号管理列表
    method: 'POST',
    path: '/syn/financeCostProject/list'
  },
  {
    name: 'costProject-save', // 保存工号管理
    method: 'POST',
    path: '/syn/financeCostProject/save'
  },
  {
    name: 'costProject-async', // 同步工号
    method: 'POST',
    path: '/syn/financeCostProject/synchronization'
  },
  {
    name: 'costProject-dts', // 查询工号管理详情
    method: 'OTHERGET',
    path: '/syn/financeCostProject/get'
  },
  {
    name: 'costProject-del', // 删除工号管理
    method: 'OTHER',
    path: '/syn/financeCostProject/delete'
  },
  {
    name: 'costStaffRel-page', // 查询员工成本中心分页
    method: 'POST',
    path: '/syn/financeCostStaffRel/page'
  },
  {
    name: 'costStaffRel-save', // 保存员工成本中心
    method: 'POST',
    path: '/syn/financeCostStaffRel/save-staff-cost-rel'
  },
  {
    name: 'costStaffRel-change', // 更改成本中心
    method: 'POST',
    path: '/syn/financeCostStaffRel/change-cost-center'
  },
  {
    name: 'costStaffRel-dts', // 查询员工成本中心详情
    method: 'OTHERGET',
    path: '/syn/financeCostStaffRel/get'
  },
  {
    name: 'costStaffRel-del', // 删除员工成本中心
    method: 'OTHER',
    path: '/syn/financeCostStaffRel/delete'
  },
  {
    name: 'costStaffRel-getBind', // 查询已绑定的用户id列表
    method: 'GET',
    path: '/syn/financeCostStaffRel/get-bind-user-ids'
  },
  {
    name: 'financeSubject-page', // 查询财务科目分页
    method: 'POST',
    path: '/syn/financeSubject/page'
  },
  {
    name: 'financeSubject-save', // 保存财务科目
    method: 'POST',
    path: '/syn/financeSubject/save'
  },
  {
    name: 'financeSubject-dts', // 查询财务科目详情
    method: 'OTHERGET',
    path: '/syn/financeSubject/get'
  },
  {
    name: 'financeSubject-del', // 删除财务科目
    method: 'OTHER',
    path: '/syn/financeSubject/delete'
  },
  {
    name: 'financeCostMonthly-page', // 查询员工工时管理分页
    method: 'POST',
    path: '/syn/financeCostMonthly/page'
  },
  {
    name: 'financeCostMonthly-save', // 保存员工工时管理
    method: 'POST',
    path: '/syn/financeCostMonthly/create-new-one'
  },
  {
    name: 'financeCostMonthly-copy', // 复制
    method: 'POST',
    path: '/syn/financeCostMonthly/copy-from'
  },
  {
    name: 'financeCostMonthly-dts', // 查询员工工时管理详情
    method: 'OTHERGET',
    path: '/syn/financeCostMonthly/get'
  },
  {
    name: 'financeCostMonthly-del', // 删除员工工时管理
    method: 'OTHER',
    path: '/syn/financeCostMonthly/delete'
  },
  {
    name: 'financeCostMonthly-getUser', // 获取可配置工时的员工列表
    method: 'GET',
    path: '/syn/financeCostMonthly/users-to-add-hours'
  },
  {
    name: 'financeCostHoursDetail-page', // 查询员工工时管理详情分页
    method: 'POST',
    path: '/syn/financeCostHoursDetail/page'
  },
  {
    name: 'financeCostHoursDetail-save', // 保存员工工时管理详情
    method: 'POST',
    path: '/syn/financeCostHoursDetail/save'
  },
  {
    name: 'financeCostHoursDetail-dts', // 查询员工工时管理详情详情
    method: 'OTHERGET',
    path: '/syn/financeCostHoursDetail/get'
  },
  {
    name: 'financeCostHoursDetail-del', // 删除员工工时管理详情
    method: 'OTHER',
    path: '/syn/financeCostHoursDetail/delete'
  },
  {
    name: 'deptSync', // 拉取致远部门数据
    method: 'GET',
    path: '/syn/zy/fetch/dept'
  },
  {
    name: 'personSync', // 拉取致远人员数据
    method: 'GET',
    path: '/syn/zy/fetch/person'
  },
  {
    name: 'unLock', // 解锁用户
    method: 'POST',
    path: '/system/user/unlock'
  },
  {
    name: 'userSyc', // 拉取飞书用户数据
    method: 'GET',
    path: '/fs/sync/user'
  },
  {
    name: 'module-options', // 获取用户所属模块下拉选项
    method: 'GET',
    path: '/syn/financeCostCenter/module-options'
  },
  {
    name: 'module-zhiYuanModuleEnum', // 查询致远模块枚举值列表
    method: 'POST',
    path: '/syn/zhiYuanModuleEnum/list'
  },
  {
    name: 'module-get-bound-list', // 查询成本中心已绑定的模块枚举值
    method: 'GET',
    path: '/syn/financeCostCenter/get-bound-list'
  },
  {
    name: 'bind-cost-staff', // 同步成本中心与用户模块关系
    method: 'GET',
    path: '/syn/financeCostStaffRel/bind-cost-staff'
  },
  {
    name: 'bindData',
    method: 'GET',
    path: '/syn/financeCostStaffRel/auto-bind'
  },

  {
    name: 'find-user-module', // 根据用户IDS查询致远模块
    method: 'POST',
    path: '/syn/financeCostStaffRel/find-user-module'
  },

  {
    name: 'find-user-now-module', // 根据用户IDS查询致远模块
    method: 'POST',
    path: '/syn/financeCostStaffRel/find-user-now-module'
  },

  {
    name: 'find-user-dept', // 根据用户IDS查询致远用户所属部门
    method: 'POST',
    path: '/syn/zhiYuanDept/find-user-dept'
  },
  {
    name: 'find-zhi-yuan-person', // 根据用户IDS查询致远用户信息
    method: 'POST',
    path: '/syn/zhiYuanPerson/find-zhi-yuan-person'
  },
  {
    name: 'fs-sync-user', // 拉取飞书用户数据
    method: 'GET',
    path: '/fs/sync/user'
  }
];

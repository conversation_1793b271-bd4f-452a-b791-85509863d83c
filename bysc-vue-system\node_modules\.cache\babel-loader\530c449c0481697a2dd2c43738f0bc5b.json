{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\user\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\user\\index.js", "mtime": 1745205562766}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import User from \"./user.vue\";\nexport default User;", {"version": 3, "names": ["User"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/components/main/components/user/index.js"], "sourcesContent": ["import User from './user.vue';\r\nexport default User;\r\n"], "mappings": "AAAA,OAAOA,IAAI;AACX,eAAeA,IAAI", "ignoreList": []}]}
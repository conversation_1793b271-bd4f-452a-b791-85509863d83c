<!--
 * @Author: czw
 * @Date: 2024-01-01 00:00:00
 * @LastEditors: czw
 * @LastEditTime: 2024-01-01 00:00:00
 * @FilePath: \bysc-vue-system\src\bysc_system\views\tenant\components\AdminManageDialog.vue
 * @Description: 维护管理员弹窗组件
 *
 * Copyright (c) 2024 by czw/bysc, All Rights Reserved.
-->
<template>
  <el-drawer
    title="维护管理员"
    :visible.sync="dialogVisible"
    direction="rtl"
    size="80%"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    @close="handleClose"
  >
    <div class="admin-manage-container">
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-button type="primary" size="small" @click="handleAdd">添加</el-button>
      </div>

      <!-- 管理员列表表格 -->
      <el-table
        :data="currentPageData"
        stripe
        style="width: 100%"
        v-loading="tableLoading"
        height="400"
        border
      >
        <el-table-column
          prop="username"
          label="用户名"
          width="150"
          align="center"
        />
        <el-table-column
          prop="nickname"
          label="昵称"
          width="150"
          align="center"
        />
        <el-table-column
          prop="roleName"
          label="角色"
          width="150"
          align="center"
        />
        <el-table-column
          prop="orgName"
          label="组织"
          min-width="200"
          align="center"
        />
        <el-table-column
          label="操作"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
            >
              修改
            </el-button>
            <el-popconfirm
              title="确定要删除该管理员吗？"
              @confirm="handleDelete(scope.row)"
            >
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                slot="reference"
              >
                删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>

    <!-- 添加/编辑管理员表单弹窗 -->
    <el-dialog
      :title="formTitle"
      :visible.sync="formDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        :model="adminForm"
        :rules="formRules"
        ref="adminForm"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="adminForm.nickname"
            placeholder="请输入昵称"
          />
        </el-form-item>

        <!-- 添加模式下显示的字段 -->
        <template v-if="!isEdit">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="adminForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="再次确认密码" prop="confirmPassword">
            <el-input
              v-model="adminForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="角色" prop="roleId">
            <el-select
              v-model="adminForm.roleId"
              placeholder="请选择角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="组织" prop="orgId">
            <el-select
              v-model="adminForm.orgId"
              placeholder="请选择组织"
              style="width: 100%"
            >
              <el-option
                v-for="org in orgList"
                :key="org.id"
                :label="org.name"
                :value="org.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleFormCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleFormSubmit"
          :loading="formLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
export default {
  name: 'AdminManageDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tenantInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      formDialogVisible: false,
      tableLoading: false,
      formLoading: false,
      isEdit: false,
      formTitle: '添加管理员',
      // 分页相关数据
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      adminList: [
        {
          id: 1,
          username: 'admin001',
          nickname: '系统管理员',
          roleId: 1,
          roleName: '超级管理员',
          orgId: 1,
          orgName: '总部',
          tenantId: 1
        },
        {
          id: 2,
          username: 'manager001',
          nickname: '业务经理',
          roleId: 2,
          roleName: '业务管理员',
          orgId: 2,
          orgName: '业务部',
          tenantId: 1
        },
        {
          id: 3,
          username: 'user001',
          nickname: '普通用户1',
          roleId: 3,
          roleName: '普通用户',
          orgId: 3,
          orgName: '技术部',
          tenantId: 1
        },
        {
          id: 4,
          username: 'user002',
          nickname: '普通用户2',
          roleId: 3,
          roleName: '普通用户',
          orgId: 1,
          orgName: '总部',
          tenantId: 1
        },
        {
          id: 5,
          username: 'admin002',
          nickname: '系统管理员2',
          roleId: 1,
          roleName: '超级管理员',
          orgId: 2,
          orgName: '业务部',
          tenantId: 1
        },
        {
          id: 6,
          username: 'manager002',
          nickname: '业务经理2',
          roleId: 2,
          roleName: '业务管理员',
          orgId: 3,
          orgName: '技术部',
          tenantId: 1
        },
        {
          id: 7,
          username: 'user003',
          nickname: '普通用户3',
          roleId: 3,
          roleName: '普通用户',
          orgId: 1,
          orgName: '总部',
          tenantId: 1
        },
        {
          id: 8,
          username: 'user004',
          nickname: '普通用户4',
          roleId: 3,
          roleName: '普通用户',
          orgId: 2,
          orgName: '业务部',
          tenantId: 1
        },
        {
          id: 9,
          username: 'admin003',
          nickname: '系统管理员3',
          roleId: 1,
          roleName: '超级管理员',
          orgId: 3,
          orgName: '技术部',
          tenantId: 1
        },
        {
          id: 10,
          username: 'manager003',
          nickname: '业务经理3',
          roleId: 2,
          roleName: '业务管理员',
          orgId: 1,
          orgName: '总部',
          tenantId: 1
        },
        {
          id: 11,
          username: 'user005',
          nickname: '普通用户5',
          roleId: 3,
          roleName: '普通用户',
          orgId: 2,
          orgName: '业务部',
          tenantId: 1
        },
        {
          id: 12,
          username: 'user006',
          nickname: '普通用户6',
          roleId: 3,
          roleName: '普通用户',
          orgId: 3,
          orgName: '技术部',
          tenantId: 1
        }
      ],
      roleList: [
        {
          id: 1,
          name: '超级管理员',
          code: 'super_admin'
        },
        {
          id: 2,
          name: '业务管理员',
          code: 'business_admin'
        },
        {
          id: 3,
          name: '普通用户',
          code: 'normal_user'
        }
      ],
      orgList: [
        {
          id: 1,
          name: '总部',
          code: 'headquarters'
        },
        {
          id: 2,
          name: '业务部',
          code: 'business_dept'
        },
        {
          id: 3,
          name: '技术部',
          code: 'tech_dept'
        }
      ],
      adminForm: {
        id: null,
        username: '',
        nickname: '',
        password: '',
        confirmPassword: '',
        roleId: null,
        orgId: null,
        tenantId: null
      },
      formRules: {
        username: [
          {required: true, message: '请输入用户名', trigger: 'blur'}
        ],
        nickname: [
          {required: true, message: '请输入昵称', trigger: 'blur'}
        ],
        password: [
          {required: true, message: '请输入密码', trigger: 'blur'},
          {min: 6, message: '密码长度不能少于6位', trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '请再次输入密码', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value !== this.adminForm.password) {
                callback(new Error('两次输入的密码不一致'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        roleId: [
          {required: true, message: '请选择角色', trigger: 'change'}
        ],
        orgId: [
          {required: true, message: '请选择组织', trigger: 'change'}
        ]
      }
    };
  },
  computed: {
    // 当前页显示的数据
    currentPageData() {
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
      const end = start + this.pagination.pageSize;
      return this.adminList.slice(start, end);
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadAdminList();
        this.loadRoleList();
        this.loadOrgList();
        // 更新分页总数
        this.pagination.total = this.adminList.length;
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 加载管理员列表
    loadAdminList() {
      if (!this.tenantInfo.id) {
        return;
      }

      this.tableLoading = true;
      // 使用假数据模拟API调用
      setTimeout(() => {
        // 假数据已经在data中定义，这里不需要重新赋值
        // 更新分页总数
        this.pagination.total = this.adminList.length;
        this.tableLoading = false;
      }, 500);
    },

    // 加载角色列表
    loadRoleList() {
      // 使用假数据，已在data中定义
      console.log('角色列表已加载');
    },

    // 加载组织列表
    loadOrgList() {
      // 使用假数据，已在data中定义
      console.log('组织列表已加载');
    },

    // 添加管理员
    handleAdd() {
      this.isEdit = false;
      this.formTitle = '添加管理员';
      this.adminForm = {
        id: null,
        username: '',
        nickname: '',
        password: '',
        confirmPassword: '',
        roleId: null,
        orgId: null,
        tenantId: this.tenantInfo.id
      };
      this.formDialogVisible = true;
    },

    // 编辑管理员
    handleEdit(row) {
      this.isEdit = true;
      this.formTitle = '编辑管理员';
      this.adminForm = {
        id: row.id,
        username: row.username,
        nickname: row.nickname,
        password: '', // 编辑时不显示密码字段
        confirmPassword: '',
        roleId: row.roleId,
        orgId: row.orgId,
        tenantId: this.tenantInfo.id
      };
      this.formDialogVisible = true;
    },

    // 删除管理员
    handleDelete(row) {
      // 模拟删除操作
      const index = this.adminList.findIndex(item => item.id === row.id);
      if (index !== -1) {
        this.adminList.splice(index, 1);
        this.$message.success('删除成功');
      } else {
        this.$message.error('删除失败');
      }
    },

    // 表单提交
    handleFormSubmit() {
      this.$refs.adminForm.validate(valid => {
        if (valid) {
          this.formLoading = true;

          // 模拟API调用
          setTimeout(() => {
            if (this.isEdit) {
              // 编辑模式：更新现有数据
              const index = this.adminList.findIndex(item => item.id === this.adminForm.id);
              if (index !== -1) {
                const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);
                const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);

                this.adminList.splice(index, 1, {
                  ...this.adminForm,
                  roleName: roleInfo ? roleInfo.name : '',
                  orgName: orgInfo ? orgInfo.name : ''
                });
              }
            } else {
              // 添加模式：添加新数据
              const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);
              const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);

              const newAdmin = {
                ...this.adminForm,
                id: Date.now(), // 使用时间戳作为临时ID
                roleName: roleInfo ? roleInfo.name : '',
                orgName: orgInfo ? orgInfo.name : ''
              };
              this.adminList.push(newAdmin);
            }

            this.$message.success(this.isEdit ? '修改成功' : '添加成功');
            this.formDialogVisible = false;
            this.formLoading = false;
          }, 500);
        }
      });
    },

    // 表单取消
    handleFormCancel() {
      this.formDialogVisible = false;
      this.$refs.adminForm.resetFields();
    },

    // 关闭主弹窗
    handleClose() {
      this.dialogVisible = false;
    },

    // 分页相关方法
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1; // 重置到第一页
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
    }
  }
};
</script>

<style lang="less" scoped>
.admin-manage-container {
  padding: 20px;
  height: calc(100vh - 120px); // 最大化利用屏幕高度
  display: flex;
  flex-direction: column;

  .action-bar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0; // 不缩放
  }

  .el-table {
    border: 1px solid #ebeef5;
    flex: 1; // 占据剩余空间
    overflow: auto;
  }

  .pagination-container {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    text-align: right;
    flex-shrink: 0; // 不缩放
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

// 抽屉样式优化
:deep(.el-drawer) {
  .el-drawer__header {
    padding: 20px 20px 0 20px;
    margin-bottom: 0;
  }

  .el-drawer__body {
    padding: 0;
    overflow-y: auto;
  }
}
</style>

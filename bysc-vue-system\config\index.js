var path = require('path')

module.exports = {
  development: require('./vue.config.dev'),
  joint: require('./vue.config.joint'),
  production: require('./vue.config.prd'),
  test: require('./vue.config.test')

  /* build: {
    // env: require('./prod.env'),
    index: path.resolve(__dirname, '../dist/index.html'),
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    // assetsPublicPath: './', // tomcat部署
    productionSourceMap: true,
    productionGzip: false,
    productionGzipExtensions: ['js', 'css']
  } */
  /* dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/vue': {
        changeOrigin: true, // 如果接口跨域，需要进行这个参数配置
        target: 'http://**************:2080',
        source: false
      },
      onProxyReq: function (proxyReq, req, res) {
        // 实在不知道代理后的路径，可以在这里打印出出来看看
        console.log('old path:' + req.originalUrl, 'new path:' + req.path)
      }
    },

    // Various Dev Server settings
    host: 'localhost', // can be overwritten by process.env.HOST
    port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-
    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  } */
}

import Vue from 'vue';
import {getCurrentVud} from '@/utils/vud';

// 全局配置 el-upload 的默认 headers
Vue.prototype.$uploadHeaders = {
  get headers() {
    return {
      Authorization: "Bearer " + Vue.prototype.$cookies.get("userToken"),
      TenantId: 0,
      Vud: getCurrentVud()
    };
  }
};

// 全局 mixin 自动为 el-upload 添加 headers
Vue.mixin({
  mounted() {
    // 查找所有 el-upload 组件
    const uploads = this.$refs ? Object.values(this.$refs).filter(ref => ref && ref.$options && ref.$options.name === 'ElUpload') : [];
    uploads.forEach(upload => {
      if (upload && !upload.headers) {
        upload.headers = this.$uploadHeaders.headers;
      }
    });
  }
});
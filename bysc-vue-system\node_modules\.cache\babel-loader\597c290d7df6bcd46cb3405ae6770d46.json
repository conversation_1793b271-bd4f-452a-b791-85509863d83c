{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\axios.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\axios.js", "mtime": 1745205562793}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"regenerator-runtime/runtime\";\nimport _asyncToGenerator from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _Message2 from \"element-ui/lib/message\";\nimport _Message from \"element-ui/lib/message\";\nimport axios from 'axios';\nimport { AXIOS_DEFAULT_CONFIG } from '@/config';\nimport createAuthRefreshInterceptor from 'axios-auth-refresh';\nimport store from '@/plugins/store';\nimport api from '@/plugins/api';\nimport localCache from '@/utils/storage';\nimport { getCurrentVud } from '@/utils/vud';\n\n// 避免其他接口同时请求\nvar isRefreshToken = false;\nvar timers = null;\nvar isUseing = false;\nfunction requestSuccessFunc(requestObj) {\n  // 判断当前是否租户模式，如果是就带上租户Id\n  if (store.state.common.isTenantMode) {\n    // 租户id后面由后端获取\n    requestObj.headers.TenantId = 0;\n  }\n\n  // 确保所有请求都带上 Vud 请求头\n  var vud = getCurrentVud();\n  console.log(vud, 'vud');\n  if (vud) {\n    requestObj.headers.Vud = vud;\n  }\n\n  // 在发起请求前做一次拦截，当一个浏览器同时登录两个账号的时候前一个账号会被踢出\n  if (window.location.pathname !== '/adminLogin' && window.location.pathname !== '/' && window.location.pathname !== '/errorPage' && window.location.pathname !== '/nofound') {\n    if (localCache.getLocal('nowUserName') != localCache.getSession('username') && localCache.getSession('username')) {\n      // localStorage.clear();\n      location.reload();\n    }\n  }\n  return requestObj;\n}\nfunction requestFailFunc(requestError) {\n  // 发送请求失败处理\n\n  return Promise.reject(requestError);\n}\n\n// 响应成功\nfunction responseSuccessFunc(responseObj) {\n  if (!responseObj || !responseObj.data) {\n    return responseObj;\n  }\n  if (responseObj.data.code != 'S00000') {\n    _Message.error(responseObj.data.msg);\n    return Promise.reject(responseObj.data.msg);\n  }\n  return responseObj.data.data;\n}\nvar sleep = function sleep() {\n  var delaytime = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;\n  return new Promise(function (resolve) {\n    return setTimeout(resolve, delaytime);\n  });\n};\n\n// 本文件是实际使用的axios\nfunction responseFailFunc(responseError) {\n  if (responseError.response) {\n    switch (responseError.response.status) {\n      // case 403:\n      //   location.reload()\n      //   break\n      case 201:\n      case 202:\n        break;\n      case 400:\n        _Message.error(responseError.response.data.msg ? responseError.response.data.msg : '参数错误');\n        break;\n      case 401:\n        console.log(responseError.response, '401');\n        if (responseError.response.config.url == '/api/oauth/oauth/token' || responseError.response.config.url == '/api/system/account/user/info') {\n          toLogin();\n          return;\n        }\n        return sleep(1000).then(function () {\n          if (isRefreshToken) {\n            responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\n            return axios(responseError.response.config).then(function (res) {\n              return res.data.data;\n            });\n          }\n          return sleep(2000).then(function () {\n            if (isRefreshToken) {\n              responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\n              return axios(responseError.response.config).then(function (res) {\n                return res.data.data;\n              });\n            }\n            return sleep(4000).then(function () {\n              if (isRefreshToken) {\n                responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\n                return axios(responseError.response.config).then(function (res) {\n                  return res.data.data;\n                });\n              }\n            });\n          });\n        });\n      case 403:\n        _Message.error(responseError.response.data.msg ? responseError.response.data.msg : '暂无权限');\n        // toLogin();\n        alert('您无该接口权限');\n        window.history.go(-1);\n        throw '无权限';\n      case 404:\n        _Message({\n          message: responseError.response.data.msg ? responseError.response.data.msg : '接口错误',\n          type: 'error'\n        });\n        break;\n      case 503:\n        _Message.error(responseError.response.data.msg ? responseError.response.data.msg : '服务挂了');\n        throw '服务器异常';\n      case 500:\n        _Message({\n          message: responseError.response.data.msg ? responseError.response.data.msg : '服务错误',\n          type: 'error'\n        });\n        break;\n      default:\n    }\n  } else {\n    _Message.error('接口请求失败，请刷新重试');\n    // localStorage.clear();\n    // MessageBox.alert('登录超时，请重新登录！', '登录超时', {\n    //   confirmButtonText: '确定',\n    //   callback: action => {\n    //     toLogin();\n    //   }\n    // });\n  }\n  // 自定义错误码，弹窗显示错误内容\n  return Promise.reject(responseError);\n}\n\n/**\r\n * 跳转登录页面\r\n */\nfunction toLogin() {\n  if (!isUseing) {\n    isUseing = true;\n    alert('登录超时', \"提示\");\n    timers = setTimeout(function () {\n      isUseing = false;\n      clearTimeout(timers);\n    }, 3000);\n    localStorage.getItem('loginPageUrl') && (location.href = localStorage.getItem('loginPageUrl'));\n    if (!localStorage.getItem('loginPageUrl')) {\n      window.close();\n    }\n  }\n  // if (localStorage.getItem('userToken')) {\n  //   store.dispatch('handleLogOut');\n  // }\n  // localCache.removeLocal('userToken');\n  // localCache.removeLocal('refreshToken');\n  // sessionStorage.removeItem(process.env.VUE_APP_LOCAL_PREFIX + 'username');\n  // location.href = '/adminLogin';\n}\nvar axiosInstance = axios.create(AXIOS_DEFAULT_CONFIG);\n// 当token失效时，需要调用的刷新token的方法\nvar refreshAuthLogic = function refreshAuthLogic(failedRequest) {\n  return api['account/getToken']({\n    grant_type: 'refresh_token',\n    refresh_token: $cookies.get('refreshToken')\n  }).then(/*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/regeneratorRuntime.mark(function _callee(data) {\n      return regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            localCache.setLocal('userToken', data.token);\n            localCache.setLocal('refreshToken', data.refreshToken);\n            localCache.setLocal('userExpir', data.exp);\n            isRefreshToken = true;\n            setTimeout(function () {\n              isRefreshToken = false;\n            }, 30000);\n            failedRequest.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\n            return _context.abrupt(\"return\", Promise.resolve());\n          case 7:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }()).catch(function (e) {\n    toLogin();\n  });\n};\n\n// 初始化刷新token拦截器;\ncreateAuthRefreshInterceptor(axiosInstance, refreshAuthLogic, {\n  pauseInstanceWhileRefreshing: true\n});\n// 注入请求拦截\naxiosInstance.interceptors.request.use(requestSuccessFunc, requestFailFunc);\n// 注入失败拦截\naxiosInstance.interceptors.response.use(responseSuccessFunc, responseFailFunc);\nexport default axiosInstance;", {"version": 3, "names": ["axios", "AXIOS_DEFAULT_CONFIG", "createAuthRefreshInterceptor", "store", "api", "localCache", "getCurrentVud", "isRefreshToken", "timers", "isUseing", "requestSuccessFunc", "requestObj", "state", "common", "isTenantMode", "headers", "TenantId", "vud", "console", "log", "<PERSON><PERSON>", "window", "location", "pathname", "getLocal", "getSession", "reload", "requestFailFunc", "requestError", "Promise", "reject", "responseSuccessFunc", "responseObj", "data", "code", "_Message", "error", "msg", "sleep", "delaytime", "arguments", "length", "undefined", "resolve", "setTimeout", "responseFailFunc", "responseError", "response", "status", "config", "url", "<PERSON><PERSON><PERSON><PERSON>", "then", "Authorization", "$cookies", "get", "res", "alert", "history", "go", "message", "type", "clearTimeout", "localStorage", "getItem", "href", "close", "axiosInstance", "create", "refreshAuthLog<PERSON>", "failedRequest", "grant_type", "refresh_token", "_ref", "_asyncToGenerator", "regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "setLocal", "token", "refreshToken", "exp", "abrupt", "stop", "_x", "apply", "catch", "e", "pauseInstanceWhileRefreshing", "interceptors", "request", "use"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/axios.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {AXIOS_DEFAULT_CONFIG} from '@/config';\r\nimport createAuthRefreshInterceptor from 'axios-auth-refresh';\r\nimport store from '@/plugins/store';\r\nimport api from '@/plugins/api';\r\nimport localCache from '@/utils/storage';\r\nimport {Message} from 'element-ui';\r\nimport {getCurrentVud} from '@/utils/vud';\r\n\r\n// 避免其他接口同时请求\r\nlet isRefreshToken = false;\r\n\r\nlet timers = null;\r\nlet isUseing = false;\r\n\r\n\r\nfunction requestSuccessFunc(requestObj) {\r\n  // 判断当前是否租户模式，如果是就带上租户Id\r\n  if (store.state.common.isTenantMode) {\r\n    // 租户id后面由后端获取\r\n    requestObj.headers.TenantId = 0;\r\n  }\r\n\r\n  // 确保所有请求都带上 Vud 请求头\r\n  const vud = getCurrentVud();\r\n  console.log(vud, 'vud');\r\n\r\n  if (vud) {\r\n    requestObj.headers.Vud = vud;\r\n  }\r\n\r\n  // 在发起请求前做一次拦截，当一个浏览器同时登录两个账号的时候前一个账号会被踢出\r\n  if (window.location.pathname !== '/adminLogin' && window.location.pathname !== '/' && window.location.pathname !== '/errorPage' && window.location.pathname !== '/nofound') {\r\n    if ((localCache.getLocal('nowUserName') != localCache.getSession('username')) && localCache.getSession('username')) {\r\n      // localStorage.clear();\r\n      location.reload();\r\n    }\r\n  }\r\n  return requestObj;\r\n}\r\n\r\nfunction requestFailFunc(requestError) {\r\n  // 发送请求失败处理\r\n\r\n  return Promise.reject(requestError);\r\n}\r\n\r\n// 响应成功\r\nfunction responseSuccessFunc(responseObj) {\r\n  if (!responseObj || !responseObj.data) {\r\n    return responseObj;\r\n  }\r\n  if (responseObj.data.code != 'S00000') {\r\n    Message.error(responseObj.data.msg);\r\n    return Promise.reject(responseObj.data.msg);\r\n  }\r\n  return responseObj.data.data;\r\n}\r\nconst sleep = (delaytime = 1000) => {\r\n  return new Promise(resolve => setTimeout(resolve, delaytime));\r\n};\r\n\r\n// 本文件是实际使用的axios\r\nfunction responseFailFunc(responseError) {\r\n  if (responseError.response) {\r\n    switch (responseError.response.status) {\r\n      // case 403:\r\n      //   location.reload()\r\n      //   break\r\n      case 201:\r\n      case 202:\r\n        break;\r\n      case 400:\r\n        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '参数错误');\r\n        break;\r\n      case 401:\r\n        console.log(responseError.response, '401');\r\n        if (responseError.response.config.url == '/api/oauth/oauth/token' || responseError.response.config.url == '/api/system/account/user/info') {\r\n          toLogin();\r\n          return;\r\n        }\r\n\r\n        return sleep(1000).then(() => {\r\n          if (isRefreshToken) {\r\n            responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\r\n            return axios(responseError.response.config).then(res => res.data.data);\r\n          }\r\n          return sleep(2000).then(() => {\r\n            if (isRefreshToken) {\r\n              responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\r\n              return axios(responseError.response.config).then(res => res.data.data);\r\n            }\r\n            return sleep(4000).then(() => {\r\n              if (isRefreshToken) {\r\n                responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\r\n                return axios(responseError.response.config).then(res => res.data.data);\r\n              }\r\n            });\r\n          });\r\n        });\r\n      case 403:\r\n        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '暂无权限');\r\n        // toLogin();\r\n        alert('您无该接口权限');\r\n        window.history.go(-1);\r\n        throw '无权限';\r\n      case 404:\r\n        Message({\r\n          message: responseError.response.data.msg ? responseError.response.data.msg : '接口错误',\r\n          type: 'error'\r\n        });\r\n        break;\r\n      case 503:\r\n        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '服务挂了');\r\n        throw '服务器异常';\r\n      case 500:\r\n        Message({\r\n          message: responseError.response.data.msg ? responseError.response.data.msg : '服务错误',\r\n          type: 'error'\r\n        });\r\n        break;\r\n      default:\r\n    }\r\n  } else {\r\n    Message.error('接口请求失败，请刷新重试');\r\n    // localStorage.clear();\r\n    // MessageBox.alert('登录超时，请重新登录！', '登录超时', {\r\n    //   confirmButtonText: '确定',\r\n    //   callback: action => {\r\n    //     toLogin();\r\n    //   }\r\n    // });\r\n  }\r\n  // 自定义错误码，弹窗显示错误内容\r\n  return Promise.reject(responseError);\r\n}\r\n\r\n/**\r\n * 跳转登录页面\r\n */\r\nfunction toLogin() {\r\n  if (!isUseing) {\r\n    isUseing = true;\r\n    alert('登录超时', \"提示\");\r\n    timers = setTimeout(() => {\r\n      isUseing = false;\r\n      clearTimeout(timers);\r\n    }, 3000);\r\n    localStorage.getItem('loginPageUrl') && (location.href = localStorage.getItem('loginPageUrl'));\r\n    if (!localStorage.getItem('loginPageUrl')) {\r\n      window.close();\r\n    }\r\n  }\r\n  // if (localStorage.getItem('userToken')) {\r\n  //   store.dispatch('handleLogOut');\r\n  // }\r\n  // localCache.removeLocal('userToken');\r\n  // localCache.removeLocal('refreshToken');\r\n  // sessionStorage.removeItem(process.env.VUE_APP_LOCAL_PREFIX + 'username');\r\n  // location.href = '/adminLogin';\r\n}\r\n\r\nlet axiosInstance = axios.create(AXIOS_DEFAULT_CONFIG);\r\n// 当token失效时，需要调用的刷新token的方法\r\nconst refreshAuthLogic = failedRequest => api['account/getToken']({grant_type: 'refresh_token', refresh_token: $cookies.get('refreshToken')}).then(async data => {\r\n  localCache.setLocal('userToken', data.token);\r\n  localCache.setLocal('refreshToken', data.refreshToken);\r\n  localCache.setLocal('userExpir', data.exp);\r\n  isRefreshToken = true;\r\n  setTimeout(() => {\r\n    isRefreshToken = false;\r\n  }, 30000);\r\n  failedRequest.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');\r\n  return Promise.resolve();\r\n}).catch(e => {\r\n  toLogin();\r\n});\r\n\r\n// 初始化刷新token拦截器;\r\ncreateAuthRefreshInterceptor(axiosInstance, refreshAuthLogic, {pauseInstanceWhileRefreshing: true});\r\n// 注入请求拦截\r\naxiosInstance\r\n  .interceptors.request.use(requestSuccessFunc, requestFailFunc);\r\n// 注入失败拦截\r\naxiosInstance\r\n  .interceptors.response.use(responseSuccessFunc, responseFailFunc);\r\n\r\nexport default axiosInstance;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,oBAAoB,QAAO,UAAU;AAC7C,OAAOC,4BAA4B,MAAM,oBAAoB;AAC7D,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,UAAU,MAAM,iBAAiB;AAExC,SAAQC,aAAa,QAAO,aAAa;;AAEzC;AACA,IAAIC,cAAc,GAAG,KAAK;AAE1B,IAAIC,MAAM,GAAG,IAAI;AACjB,IAAIC,QAAQ,GAAG,KAAK;AAGpB,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EACtC;EACA,IAAIR,KAAK,CAACS,KAAK,CAACC,MAAM,CAACC,YAAY,EAAE;IACnC;IACAH,UAAU,CAACI,OAAO,CAACC,QAAQ,GAAG,CAAC;EACjC;;EAEA;EACA,IAAMC,GAAG,GAAGX,aAAa,CAAC,CAAC;EAC3BY,OAAO,CAACC,GAAG,CAACF,GAAG,EAAE,KAAK,CAAC;EAEvB,IAAIA,GAAG,EAAE;IACPN,UAAU,CAACI,OAAO,CAACK,GAAG,GAAGH,GAAG;EAC9B;;EAEA;EACA,IAAII,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,aAAa,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,GAAG,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,YAAY,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,UAAU,EAAE;IAC1K,IAAKlB,UAAU,CAACmB,QAAQ,CAAC,aAAa,CAAC,IAAInB,UAAU,CAACoB,UAAU,CAAC,UAAU,CAAC,IAAKpB,UAAU,CAACoB,UAAU,CAAC,UAAU,CAAC,EAAE;MAClH;MACAH,QAAQ,CAACI,MAAM,CAAC,CAAC;IACnB;EACF;EACA,OAAOf,UAAU;AACnB;AAEA,SAASgB,eAAeA,CAACC,YAAY,EAAE;EACrC;;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,YAAY,CAAC;AACrC;;AAEA;AACA,SAASG,mBAAmBA,CAACC,WAAW,EAAE;EACxC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,EAAE;IACrC,OAAOD,WAAW;EACpB;EACA,IAAIA,WAAW,CAACC,IAAI,CAACC,IAAI,IAAI,QAAQ,EAAE;IACrCC,QAAA,CAAQC,KAAK,CAACJ,WAAW,CAACC,IAAI,CAACI,GAAG,CAAC;IACnC,OAAOR,OAAO,CAACC,MAAM,CAACE,WAAW,CAACC,IAAI,CAACI,GAAG,CAAC;EAC7C;EACA,OAAOL,WAAW,CAACC,IAAI,CAACA,IAAI;AAC9B;AACA,IAAMK,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAyB;EAAA,IAArBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC7B,OAAO,IAAIX,OAAO,CAAC,UAAAc,OAAO;IAAA,OAAIC,UAAU,CAACD,OAAO,EAAEJ,SAAS,CAAC;EAAA,EAAC;AAC/D,CAAC;;AAED;AACA,SAASM,gBAAgBA,CAACC,aAAa,EAAE;EACvC,IAAIA,aAAa,CAACC,QAAQ,EAAE;IAC1B,QAAQD,aAAa,CAACC,QAAQ,CAACC,MAAM;MACnC;MACA;MACA;MACA,KAAK,GAAG;MACR,KAAK,GAAG;QACN;MACF,KAAK,GAAG;QACNb,QAAA,CAAQC,KAAK,CAACU,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAGS,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAG,MAAM,CAAC;QACzF;MACF,KAAK,GAAG;QACNnB,OAAO,CAACC,GAAG,CAAC2B,aAAa,CAACC,QAAQ,EAAE,KAAK,CAAC;QAC1C,IAAID,aAAa,CAACC,QAAQ,CAACE,MAAM,CAACC,GAAG,IAAI,wBAAwB,IAAIJ,aAAa,CAACC,QAAQ,CAACE,MAAM,CAACC,GAAG,IAAI,+BAA+B,EAAE;UACzIC,OAAO,CAAC,CAAC;UACT;QACF;QAEA,OAAOb,KAAK,CAAC,IAAI,CAAC,CAACc,IAAI,CAAC,YAAM;UAC5B,IAAI7C,cAAc,EAAE;YAClBuC,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAClC,OAAO,CAACsC,aAAa,GAAG,SAAS,GAAGC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;YAC3F,OAAOvD,KAAK,CAAC8C,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAC,CAACG,IAAI,CAAC,UAAAI,GAAG;cAAA,OAAIA,GAAG,CAACvB,IAAI,CAACA,IAAI;YAAA,EAAC;UACxE;UACA,OAAOK,KAAK,CAAC,IAAI,CAAC,CAACc,IAAI,CAAC,YAAM;YAC5B,IAAI7C,cAAc,EAAE;cAClBuC,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAClC,OAAO,CAACsC,aAAa,GAAG,SAAS,GAAGC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;cAC3F,OAAOvD,KAAK,CAAC8C,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAC,CAACG,IAAI,CAAC,UAAAI,GAAG;gBAAA,OAAIA,GAAG,CAACvB,IAAI,CAACA,IAAI;cAAA,EAAC;YACxE;YACA,OAAOK,KAAK,CAAC,IAAI,CAAC,CAACc,IAAI,CAAC,YAAM;cAC5B,IAAI7C,cAAc,EAAE;gBAClBuC,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAClC,OAAO,CAACsC,aAAa,GAAG,SAAS,GAAGC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;gBAC3F,OAAOvD,KAAK,CAAC8C,aAAa,CAACC,QAAQ,CAACE,MAAM,CAAC,CAACG,IAAI,CAAC,UAAAI,GAAG;kBAAA,OAAIA,GAAG,CAACvB,IAAI,CAACA,IAAI;gBAAA,EAAC;cACxE;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,KAAK,GAAG;QACNE,QAAA,CAAQC,KAAK,CAACU,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAGS,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAG,MAAM,CAAC;QACzF;QACAoB,KAAK,CAAC,SAAS,CAAC;QAChBpC,MAAM,CAACqC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,KAAK;MACb,KAAK,GAAG;QACNxB,QAAA,CAAQ;UACNyB,OAAO,EAAEd,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAGS,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAG,MAAM;UACnFwB,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,GAAG;QACN1B,QAAA,CAAQC,KAAK,CAACU,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAGS,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAG,MAAM,CAAC;QACzF,MAAM,OAAO;MACf,KAAK,GAAG;QACNF,QAAA,CAAQ;UACNyB,OAAO,EAAEd,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAGS,aAAa,CAACC,QAAQ,CAACd,IAAI,CAACI,GAAG,GAAG,MAAM;UACnFwB,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;IACF;EACF,CAAC,MAAM;IACL1B,QAAA,CAAQC,KAAK,CAAC,cAAc,CAAC;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACA;EACA,OAAOP,OAAO,CAACC,MAAM,CAACgB,aAAa,CAAC;AACtC;;AAEA;AACA;AACA;AACA,SAASK,OAAOA,CAAA,EAAG;EACjB,IAAI,CAAC1C,QAAQ,EAAE;IACbA,QAAQ,GAAG,IAAI;IACfgD,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC;IACnBjD,MAAM,GAAGoC,UAAU,CAAC,YAAM;MACxBnC,QAAQ,GAAG,KAAK;MAChBqD,YAAY,CAACtD,MAAM,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;IACRuD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK1C,QAAQ,CAAC2C,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC9F,IAAI,CAACD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;MACzC3C,MAAM,CAAC6C,KAAK,CAAC,CAAC;IAChB;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAEA,IAAIC,aAAa,GAAGnE,KAAK,CAACoE,MAAM,CAACnE,oBAAoB,CAAC;AACtD;AACA,IAAMoE,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,aAAa;EAAA,OAAIlE,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAACmE,UAAU,EAAE,eAAe;IAAEC,aAAa,EAAElB,QAAQ,CAACC,GAAG,CAAC,cAAc;EAAC,CAAC,CAAC,CAACH,IAAI;IAAA,IAAAqB,IAAA,GAAAC,iBAAA,cAAAC,kBAAA,CAAAC,IAAA,CAAC,SAAAC,QAAM5C,IAAI;MAAA,OAAA0C,kBAAA,CAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAC3J7E,UAAU,CAAC8E,QAAQ,CAAC,WAAW,EAAElD,IAAI,CAACmD,KAAK,CAAC;YAC5C/E,UAAU,CAAC8E,QAAQ,CAAC,cAAc,EAAElD,IAAI,CAACoD,YAAY,CAAC;YACtDhF,UAAU,CAAC8E,QAAQ,CAAC,WAAW,EAAElD,IAAI,CAACqD,GAAG,CAAC;YAC1C/E,cAAc,GAAG,IAAI;YACrBqC,UAAU,CAAC,YAAM;cACfrC,cAAc,GAAG,KAAK;YACxB,CAAC,EAAE,KAAK,CAAC;YACT+D,aAAa,CAACvB,QAAQ,CAACE,MAAM,CAAClC,OAAO,CAACsC,aAAa,GAAG,SAAS,GAAGC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;YAAC,OAAAyB,QAAA,CAAAO,MAAA,WACrF1D,OAAO,CAACc,OAAO,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAqC,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA,CACzB;IAAA,iBAAAY,EAAA;MAAA,OAAAhB,IAAA,CAAAiB,KAAA,OAAAlD,SAAA;IAAA;EAAA,IAAC,CAACmD,KAAK,CAAC,UAAAC,CAAC,EAAI;IACZzC,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;AAAA;;AAEF;AACAjD,4BAA4B,CAACiE,aAAa,EAAEE,gBAAgB,EAAE;EAACwB,4BAA4B,EAAE;AAAI,CAAC,CAAC;AACnG;AACA1B,aAAa,CACV2B,YAAY,CAACC,OAAO,CAACC,GAAG,CAACtF,kBAAkB,EAAEiB,eAAe,CAAC;AAChE;AACAwC,aAAa,CACV2B,YAAY,CAAC/C,QAAQ,CAACiD,GAAG,CAACjE,mBAAmB,EAAEc,gBAAgB,CAAC;AAEnE,eAAesB,aAAa", "ignoreList": []}]}
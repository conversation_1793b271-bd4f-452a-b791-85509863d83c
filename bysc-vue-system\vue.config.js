const buildConfig = require('./config');
const path = require('path');
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  /*
   * productionSourceMap:
   * 生产环境是否生成 sourceMap 文件
   * true: 构建时会生成source map文件，方便调试
   * false: 不生成source map，减少构建体积，提高构建速度
   * 建议生产环境设置为false
   */
  productionSourceMap: false,
  // 禁用ESLint校验
  lintOnSave: false,
  assetsDir: 'static',
  outputDir: 'systemdist',
  publicPath: process.env.NODE_ENV ==='production'?'/system':'/',
  devServer: {
    https: false,
    open: false, // 设置自动打开
    port: 8888,
    disableHostCheck: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    proxy: {
      '/api/': {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/api/': '/'
        }
      },
      '/websocket': {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/websocket': '/'
        },
      },
      '/extension': {
        target: process.env.VUE_APP_API_BASE_URL
      }
    }
  },
  configureWebpack: {
    // devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
  },
  chainWebpack(config) {
    config.plugins.delete('prefetch');
    /* config.plugin('html') */

    config.plugin('html')
      .tap(args => {
        args[0].template = resolve('index.html');
        return args;
      });

    /* 设置 resolve.alias */
    config.resolve.alias
      .set('components', resolve('src/components'))
      .set('api', resolve('src/api'))
      .set('assets', resolve('src/assets'))
      .set('mixins', resolve('src/mixins'));

    buildConfig[process.env.NODE_ENV].chainWebpack(config);
  },
  css: {
    loaderOptions: { // 向 CSS 相关的 loader 传递选项
      less: {
        javascriptEnabled: true
      }
    }
  }
};

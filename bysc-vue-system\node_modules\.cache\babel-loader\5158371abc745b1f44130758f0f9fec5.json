{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\debounce.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\debounce.js", "mtime": 1745205562783}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["var debounce = {\n  inserted: function inserted(fn, delay) {\n    // eslint-disable-next-line no-redeclare\n    var delay = delay || 200;\n    var timer;\n    return function () {\n      var th = this;\n      // eslint-disable-next-line prefer-rest-params\n      var args = arguments;\n      if (timer) {\n        clearTimeout(timer);\n      }\n      timer = setTimeout(function () {\n        timer = null;\n        fn.apply(th, args);\n      }, delay);\n    };\n  }\n};\nexport default debounce;", {"version": 3, "names": ["debounce", "inserted", "fn", "delay", "timer", "th", "args", "arguments", "clearTimeout", "setTimeout", "apply"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/debounce.js"], "sourcesContent": ["const debounce = {\r\n  inserted: function (fn, delay) {\r\n    // eslint-disable-next-line no-redeclare\r\n    var delay = delay || 200;\r\n    var timer;\r\n    return function () {\r\n      var th = this;\r\n      // eslint-disable-next-line prefer-rest-params\r\n      var args = arguments;\r\n      if (timer) {\r\n        clearTimeout(timer);\r\n      }\r\n      timer = setTimeout(function () {\r\n        timer = null;\r\n        fn.apply(th, args);\r\n      }, delay);\r\n    };\r\n  },\r\n};\r\n\r\nexport default debounce;"], "mappings": "AAAA,IAAMA,QAAQ,GAAG;EACfC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,EAAE,EAAEC,KAAK,EAAE;IAC7B;IACA,IAAIA,KAAK,GAAGA,KAAK,IAAI,GAAG;IACxB,IAAIC,KAAK;IACT,OAAO,YAAY;MACjB,IAAIC,EAAE,GAAG,IAAI;MACb;MACA,IAAIC,IAAI,GAAGC,SAAS;MACpB,IAAIH,KAAK,EAAE;QACTI,YAAY,CAACJ,KAAK,CAAC;MACrB;MACAA,KAAK,GAAGK,UAAU,CAAC,YAAY;QAC7BL,KAAK,GAAG,IAAI;QACZF,EAAE,CAACQ,KAAK,CAACL,EAAE,EAAEC,IAAI,CAAC;MACpB,CAAC,EAAEH,KAAK,CAAC;IACX,CAAC;EACH;AACF,CAAC;AAED,eAAeH,QAAQ", "ignoreList": []}]}
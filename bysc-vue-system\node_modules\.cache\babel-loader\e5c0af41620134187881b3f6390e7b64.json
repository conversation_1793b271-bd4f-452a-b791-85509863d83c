{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\uuid.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\uuid.js", "mtime": 1745205562827}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.regexp.to-string\";\nimport { random } from 'lodash';\nvar RFC4122_TEMPLATE = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';\nfunction replacePlaceholders(placeholder) {\n  var value = random(15);\n  value = placeholder === 'x' ? value : value & 0x3 | 0x8;\n  return value.toString(16);\n}\nexport default (function () {\n  return RFC4122_TEMPLATE.replace(/[xy]/g, replacePlaceholders);\n});", {"version": 3, "names": ["random", "RFC4122_TEMPLATE", "replacePlaceholders", "placeholder", "value", "toString", "replace"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/utils/uuid.js"], "sourcesContent": ["import {random} from 'lodash';\r\n\r\nconst RFC4122_TEMPLATE = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';\r\n\r\nfunction replacePlaceholders(placeholder) {\r\n  let value = random(15);\r\n  value = placeholder === 'x' ? value : (value & 0x3 | 0x8);\r\n  return value.toString(16);\r\n}\r\n\r\nexport default () => {\r\n  return RFC4122_TEMPLATE.replace(/[xy]/g, replacePlaceholders);\r\n};\r\n"], "mappings": ";AAAA,SAAQA,MAAM,QAAO,QAAQ;AAE7B,IAAMC,gBAAgB,GAAG,sCAAsC;AAE/D,SAASC,mBAAmBA,CAACC,WAAW,EAAE;EACxC,IAAIC,KAAK,GAAGJ,MAAM,CAAC,EAAE,CAAC;EACtBI,KAAK,GAAGD,WAAW,KAAK,GAAG,GAAGC,KAAK,GAAIA,KAAK,GAAG,GAAG,GAAG,GAAI;EACzD,OAAOA,KAAK,CAACC,QAAQ,CAAC,EAAE,CAAC;AAC3B;AAEA,gBAAe,YAAM;EACnB,OAAOJ,gBAAgB,CAACK,OAAO,CAAC,OAAO,EAAEJ,mBAAmB,CAAC;AAC/D,CAAC", "ignoreList": []}]}
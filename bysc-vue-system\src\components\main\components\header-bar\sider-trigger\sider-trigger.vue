<template>
  <a @click="handleChange" type="text" id="header_bar_a" :class="['sider-trigger-a', collapsed ? '' : '']">
    <i v-if="!collapsed" style="font-size:18px" class="el-icon-s-fold"></i>
    <i v-else style="font-size:18px" class="el-icon-s-unfold"></i>
  </a>
</template>
<script>
export default {
  name: 'siderTrigger',
  props: {
    collapsed: Boolean,
    icon: {
      type: String,
      default: 'navicon-round'
    },
    size: {
      type: Number,
      default: 26
    }
  },
  methods: {
    handleChange() {
      this.$emit('on-change', !this.collapsed);
    }
  }
};
</script>
<style lang="less">
@import './sider-trigger.less';
</style>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue", "mtime": 1753782569058}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["export default {\n  name: 'commonTree',\n  props: {\n    treeData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    highlightcurrent: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    defaultexpandall: {\n      type: Boolean,\n      default: function _default() {\n        return false;\n      }\n    },\n    treeProps: {\n      type: Object,\n      default: function _default() {\n        return {\n          children: 'children',\n          label: 'label'\n        };\n      }\n    }\n  },\n  data: function data() {\n    return {\n      filterText: '',\n      selectedNode: null\n    };\n  },\n  mounted: function mounted() {\n    this.selectedNode = null;\n  },\n  watch: {\n    filterText: function filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  methods: {\n    filterNode: function filterNode(value, data) {\n      if (!value) {\n        return true;\n      }\n      return data[this.treeProps.label].indexOf(value) !== -1;\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      if (data.id === this.selectedNode) {\n        this.$refs.tree.setCurrentKey(null);\n      } else {\n        this.selectedNode = data.id;\n      }\n      this.$emit('treeNode', data);\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "treeData", "type", "Array", "default", "highlightcurrent", "Boolean", "defaultexpandall", "treeProps", "Object", "children", "label", "data", "filterText", "selectedNode", "mounted", "watch", "val", "$refs", "tree", "filter", "methods", "filterNode", "value", "indexOf", "handleNodeClick", "id", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "$emit"], "sources": ["src/components/treeComp/commonTree.vue"], "sourcesContent": ["<!--\r\n * @Author: czw\r\n * @Date: 2022-11-04 09:34:34\r\n * @LastEditors: czw\r\n * @LastEditTime: 2022-11-04 09:54:34\r\n * @FilePath: \\kdsp_vue_clear\\src\\components\\treeComp\\commonTree.vue\r\n * @Description:\r\n *\r\n * Copyright (c) 2022 by czw/bysc, All Rights Reserved.\r\n-->\r\n<!--  -->\r\n<template>\r\n  <div>\r\n    <el-input\r\n  placeholder=\"输入关键字进行过滤\"\r\n  v-model=\"filterText\"\r\n  size=\"small\" style=\"width:90%;margin-bottom: 10px;\">\r\n</el-input>\r\n    <el-tree\r\n      :default-expand-all=\"defaultexpandall\"\r\n      :data=\"treeData\"\r\n      :props=\"treeProps\"\r\n      :highlight-current=\"highlightcurrent\"\r\n      accordion\r\n      @node-click=\"handleNodeClick\"\r\n      :filter-node-method=\"filterNode\"\r\n      ref=\"tree\"\r\n      node-key=\"id\"\r\n    >\r\n    </el-tree>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'commonTree',\r\n  props: {\r\n    treeData: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    highlightcurrent: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    defaultexpandall: {\r\n      type: Boolean,\r\n      default() {\r\n        return false;\r\n      },\r\n    },\r\n    treeProps: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n        };\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      filterText: '',\r\n      selectedNode:null,\r\n    };\r\n  },\r\n  mounted(){\r\n    this.selectedNode = null\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data[this.treeProps.label].indexOf(value) !== -1;\r\n    },\r\n    handleNodeClick(data) {\r\n      if(data.id===this.selectedNode){\r\n        this.$refs.tree.setCurrentKey(null);\r\n      }else{\r\n        this.selectedNode = data.id\r\n      }\r\n      this.$emit('treeNode', data);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n</style>\r\n"], "mappings": "AAiCA;EACAA,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,gBAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAG,gBAAA;MACAL,IAAA,EAAAI,OAAA;MACAF,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAI,SAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA,WAAAA,SAAA;QACA;UACAM,QAAA;UACAC,KAAA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,YAAA;EACA;EACAE,KAAA;IACAH,UAAA,WAAAA,WAAAI,GAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAH,GAAA;IACA;EACA;EACAI,OAAA;IACAC,UAAA,WAAAA,WAAAC,KAAA,EAAAX,IAAA;MACA,KAAAW,KAAA;QACA;MACA;MACA,OAAAX,IAAA,MAAAJ,SAAA,CAAAG,KAAA,EAAAa,OAAA,CAAAD,KAAA;IACA;IACAE,eAAA,WAAAA,gBAAAb,IAAA;MACA,IAAAA,IAAA,CAAAc,EAAA,UAAAZ,YAAA;QACA,KAAAI,KAAA,CAAAC,IAAA,CAAAQ,aAAA;MACA;QACA,KAAAb,YAAA,GAAAF,IAAA,CAAAc,EAAA;MACA;MACA,KAAAE,KAAA,aAAAhB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}
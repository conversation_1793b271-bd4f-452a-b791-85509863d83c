<template>
  <div class="header-bar">
    <el-row>
      <sider-trigger :collapsed="collapsed" icon="md-menu" @on-change="handleCollpasedChange"></sider-trigger>
      <el-col :span="14">
        <el-breadcrumb style="margin-top:20px" separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="(breadcrumb,index) in levelList" :key="breadcrumb.path+index" :to="{ path: breadcrumb.path }">{{breadcrumb.meta.title}}</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <el-col>
        <div class="custom-content-con">
          <slot>
          </slot>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import siderTrigger from './sider-trigger';
import './header-bar.less';
// eslint-disable-next-line no-unused-vars
import _ from 'lodash';
export default {
  name: 'HeaderBar',
  components: {
    siderTrigger
  },
  props: {
    collapsed: Boolean
  },
  created() {
    this.getBreadcrumb();
  },
  data() {
    return {
      levelList: []
    };
  },
  watch: {
    $route() {
      this.getBreadcrumb();
    }
  },
  methods: {
    /**
     * 获取当前路径（面包屑）
     */
    getBreadcrumb() {
      let matched = this.$route.matched.filter(item => item.name);
      this.levelList = matched;
    },
    handleCollpasedChange(state) {
      this.$emit('on-coll-change', state);
    },
    handleLargeScreenDisplay() {
      let {name, params, query} = {};
      name = 'datav';
      let routeData = this.$router.resolve({
        name: name,
        query: query,
        params: params
      });
      window.open(routeData.href, '_blank');
    },
    handleLargeScreenDisplay2() {
      let {name, params, query} = {};
      name = 'datav2';
      let routeData = this.$router.resolve({
        name: name,
        query: query,
        params: params
      });
      window.open(routeData.href, '_blank');
    },
    handleLargeScreenDisplay3() {
      let {name, params, query} = {};
      name = 'datav3';
      let routeData = this.$router.resolve({
        name: name,
        query: query,
        params: params
      });
      window.open(routeData.href, '_blank');
    },
    handleLargeScreenDisplay4() {
      let {name, params, query} = {};
      name = 'datav4';
      let routeData = this.$router.resolve({
        name: name,
        query: query,
        params: params
      });
      window.open(routeData.href, '_blank');
    }
  }
};
</script>

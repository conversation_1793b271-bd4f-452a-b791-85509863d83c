{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\user\\notice.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\user\\notice.vue", "mtime": 1745205562768}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./notice.vue?vue&type=template&id=25419ed9&scoped=true\"\nimport script from \"./notice.vue?vue&type=script&lang=js\"\nexport * from \"./notice.vue?vue&type=script&lang=js\"\nimport style0 from \"./notice.vue?vue&type=style&index=0&id=25419ed9&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"25419ed9\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('25419ed9')) {\n      api.createRecord('25419ed9', component.options)\n    } else {\n      api.reload('25419ed9', component.options)\n    }\n    module.hot.accept(\"./notice.vue?vue&type=template&id=25419ed9&scoped=true\", function () {\n      api.rerender('25419ed9', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/main/components/user/notice.vue\"\nexport default component.exports"]}
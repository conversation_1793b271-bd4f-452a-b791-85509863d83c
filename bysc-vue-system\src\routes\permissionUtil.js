
function findPermission(url, permissions) {
  console.log(url, permissions);
  return permissions.find(permission => {
    // return permission.url === url //鉴权改为通过拓展字段
    return permission.resourcePath == url;
  });
}

export default function filterRoutes(routes, permissions) {
  console.log(routes, permissions, '---');
  let index = 0;
  while (routes.length > index) {
    let route = routes[index];
    let permission = findPermission((route.meta && route.meta.path) || route.path, permissions);
    if (permission) {
      route.meta.title = permission.resourceName; // fanqz add 重写菜单名称，采用数据库的
      route.meta = {
        ...route.meta,
        ...permission
      };
      ++index;
      if (route.children) {
        filterRoutes(route.children, permissions);
      }
    } else {
      routes.splice(index, 1);
    }
  }
}

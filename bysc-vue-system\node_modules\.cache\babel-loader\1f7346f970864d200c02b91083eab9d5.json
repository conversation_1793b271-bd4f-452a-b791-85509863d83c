{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\index.js", "mtime": 1745205562779}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["// 环境\nexport var NODE_ENV = process.env.NODE_ENV || 'production';\n\n// 路由默认配置，路由表并不从此注入\nexport var ROUTER_DEFAULT_CONFIG = {\n  waitForData: true,\n  transitionOnLoad: true,\n  mode: 'history',\n  // nginx 使用history模式\n  base: process.env.BASE_URL\n};\n\n// axios 默认配置\nexport var AXIOS_DEFAULT_CONFIG = {\n  timeout: 300000,\n  // 访问超时时间\n  maxContentLength: 2000,\n  withCredentials: true,\n  // 服务器端要允许跨域请求，否则cookie无法使用\n  headers: {\n    'content-type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest'\n  },\n  baseURL: window.globalConfig.baseURL\n};\n\n// vuex 默认配置\nexport var VUEX_DEFAULT_CONFIG = {\n  strict: process.env.NODE_ENV !== 'production'\n};\n\n// API 默认配置\nexport var API_DEFAULT_CONFIG = {\n  debug: process.env.NODE_ENV !== 'production',\n  sep: '/'\n};\n\n// CONST 默认配置\nexport var CONST_DEFAULT_CONFIG = {\n  sep: '/'\n};\n\n// 还有一些方便开发的配置\nexport var CONSOLE_REQUEST_ENABLE = process.env.NODE_ENV !== 'production'; // 开启请求参数打印\nexport var CONSOLE_RESPONSE_ENABLE = process.env.NODE_ENV !== 'production'; // 开启响应参数打印", {"version": 3, "names": ["NODE_ENV", "process", "env", "ROUTER_DEFAULT_CONFIG", "waitForData", "transitionOnLoad", "mode", "base", "BASE_URL", "AXIOS_DEFAULT_CONFIG", "timeout", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "withCredentials", "headers", "baseURL", "window", "globalConfig", "VUEX_DEFAULT_CONFIG", "strict", "API_DEFAULT_CONFIG", "debug", "sep", "CONST_DEFAULT_CONFIG", "CONSOLE_REQUEST_ENABLE", "CONSOLE_RESPONSE_ENABLE"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/config/index.js"], "sourcesContent": ["// 环境\r\nexport const NODE_ENV = process.env.NODE_ENV || 'production';\r\n\r\n// 路由默认配置，路由表并不从此注入\r\nexport const ROUTER_DEFAULT_CONFIG = {\r\n  waitForData: true,\r\n  transitionOnLoad: true,\r\n  mode: 'history', // nginx 使用history模式\r\n  base: process.env.BASE_URL\r\n};\r\n\r\n// axios 默认配置\r\nexport const AXIOS_DEFAULT_CONFIG = {\r\n  timeout: 300000, // 访问超时时间\r\n  maxContentLength: 2000,\r\n  withCredentials: true, // 服务器端要允许跨域请求，否则cookie无法使用\r\n  headers: {\r\n    'content-type': 'application/json',\r\n    'X-Requested-With': 'XMLHttpRequest'\r\n  },\r\n  baseURL: window.globalConfig.baseURL\r\n};\r\n\r\n// vuex 默认配置\r\nexport const VUEX_DEFAULT_CONFIG = {\r\n  strict: process.env.NODE_ENV !== 'production'\r\n};\r\n\r\n// API 默认配置\r\nexport const API_DEFAULT_CONFIG = {\r\n  debug: process.env.NODE_ENV !== 'production',\r\n  sep: '/'\r\n};\r\n\r\n// CONST 默认配置\r\nexport const CONST_DEFAULT_CONFIG = {\r\n  sep: '/'\r\n};\r\n\r\n// 还有一些方便开发的配置\r\nexport const CONSOLE_REQUEST_ENABLE = process.env.NODE_ENV !== 'production'; // 开启请求参数打印\r\nexport const CONSOLE_RESPONSE_ENABLE = process.env.NODE_ENV !== 'production'; // 开启响应参数打印\r\n"], "mappings": "AAAA;AACA,OAAO,IAAMA,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACF,QAAQ,IAAI,YAAY;;AAE5D;AACA,OAAO,IAAMG,qBAAqB,GAAG;EACnCC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,IAAI,EAAE,SAAS;EAAE;EACjBC,IAAI,EAAEN,OAAO,CAACC,GAAG,CAACM;AACpB,CAAC;;AAED;AACA,OAAO,IAAMC,oBAAoB,GAAG;EAClCC,OAAO,EAAE,MAAM;EAAE;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE,IAAI;EAAE;EACvBC,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,kBAAkB,EAAE;EACtB,CAAC;EACDC,OAAO,EAAEC,MAAM,CAACC,YAAY,CAACF;AAC/B,CAAC;;AAED;AACA,OAAO,IAAMG,mBAAmB,GAAG;EACjCC,MAAM,EAAEjB,OAAO,CAACC,GAAG,CAACF,QAAQ,KAAK;AACnC,CAAC;;AAED;AACA,OAAO,IAAMmB,kBAAkB,GAAG;EAChCC,KAAK,EAAEnB,OAAO,CAACC,GAAG,CAACF,QAAQ,KAAK,YAAY;EAC5CqB,GAAG,EAAE;AACP,CAAC;;AAED;AACA,OAAO,IAAMC,oBAAoB,GAAG;EAClCD,GAAG,EAAE;AACP,CAAC;;AAED;AACA,OAAO,IAAME,sBAAsB,GAAGtB,OAAO,CAACC,GAAG,CAACF,QAAQ,KAAK,YAAY,CAAC,CAAC;AAC7E,OAAO,IAAMwB,uBAAuB,GAAGvB,OAAO,CAACC,GAAG,CAACF,QAAQ,KAAK,YAAY,CAAC,CAAC", "ignoreList": []}]}
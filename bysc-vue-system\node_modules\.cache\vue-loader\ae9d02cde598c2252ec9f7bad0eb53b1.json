{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue", "mtime": 1753782569058}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\nexport default {\r\n  name: 'common<PERSON><PERSON>',\r\n  props: {\r\n    treeData: {\r\n      type: <PERSON><PERSON>y,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    highlightcurrent: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    defaultexpandall: {\r\n      type: Boolean,\r\n      default() {\r\n        return false;\r\n      },\r\n    },\r\n    treeProps: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n        };\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      filterText: '',\r\n      selectedNode:null,\r\n    };\r\n  },\r\n  mounted(){\r\n    this.selectedNode = null\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data[this.treeProps.label].indexOf(value) !== -1;\r\n    },\r\n    handleNodeClick(data) {\r\n      if(data.id===this.selectedNode){\r\n        this.$refs.tree.setCurrentKey(null);\r\n      }else{\r\n        this.selectedNode = data.id\r\n      }\r\n      this.$emit('treeNode', data);\r\n    },\r\n  },\r\n};\r\n", {"version": 3, "sources": ["commonTree.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "commonTree.vue", "sourceRoot": "src/components/treeComp", "sourcesContent": ["<!--\r\n * @Author: czw\r\n * @Date: 2022-11-04 09:34:34\r\n * @LastEditors: czw\r\n * @LastEditTime: 2022-11-04 09:54:34\r\n * @FilePath: \\kdsp_vue_clear\\src\\components\\treeComp\\commonTree.vue\r\n * @Description:\r\n *\r\n * Copyright (c) 2022 by czw/bysc, All Rights Reserved.\r\n-->\r\n<!--  -->\r\n<template>\r\n  <div>\r\n    <el-input\r\n  placeholder=\"输入关键字进行过滤\"\r\n  v-model=\"filterText\"\r\n  size=\"small\" style=\"width:90%;margin-bottom: 10px;\">\r\n</el-input>\r\n    <el-tree\r\n      :default-expand-all=\"defaultexpandall\"\r\n      :data=\"treeData\"\r\n      :props=\"treeProps\"\r\n      :highlight-current=\"highlightcurrent\"\r\n      accordion\r\n      @node-click=\"handleNodeClick\"\r\n      :filter-node-method=\"filterNode\"\r\n      ref=\"tree\"\r\n      node-key=\"id\"\r\n    >\r\n    </el-tree>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'commonTree',\r\n  props: {\r\n    treeData: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    highlightcurrent: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    defaultexpandall: {\r\n      type: Boolean,\r\n      default() {\r\n        return false;\r\n      },\r\n    },\r\n    treeProps: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n        };\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      filterText: '',\r\n      selectedNode:null,\r\n    };\r\n  },\r\n  mounted(){\r\n    this.selectedNode = null\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data[this.treeProps.label].indexOf(value) !== -1;\r\n    },\r\n    handleNodeClick(data) {\r\n      if(data.id===this.selectedNode){\r\n        this.$refs.tree.setCurrentKey(null);\r\n      }else{\r\n        this.selectedNode = data.id\r\n      }\r\n      this.$emit('treeNode', data);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n</style>\r\n"]}]}
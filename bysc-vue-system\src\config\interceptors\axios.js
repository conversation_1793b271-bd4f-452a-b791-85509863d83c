import {CONSOLE_REQUEST_ENABLE, CONSOLE_RESPONSE_ENABLE} from '../index';
import {get} from 'lodash';
import router from '@/plugins/router';

export function requestSuccessFunc(requestObj) {
  CONSOLE_REQUEST_ENABLE && console.info('requestInterceptorFunc', `url: ${requestObj.url}`, requestObj);
  // 处理权限，请求发送监控

  return requestObj;
}

export function requestFailFunc(requestError) {
  // 发送请求失败处理

  return Promise.reject(requestError);
}

export function responseSuccessFunc(responseObj) {
  CONSOLE_RESPONSE_ENABLE && console.log('requestInterceptorFunc', 'data: ', responseObj.data);
  return responseObj.data.data;
}

export function responseFailFunc(responseError) {
  if (responseError.response) {
    switch (responseError.response.status) {
      // case 403:
      //   location.reload()
      //   break
      case 400:
        // responseError.message = '未授权，请重新登录'
        break;
      case 401:
        // responseError.message = get(responseError, 'response.data.message', '鉴权失败')
        router.push('loginPage'); // Unauthorized 鉴权失败，跳转到登录页面
        break;
      case 403:
        responseError.message = get(responseError, 'response.data.message', '您没有访问权限');
        router.push('loginPage'); // Forbidden没有权限，跳转到登录页面
        break;
      case 404:
        responseError.message = get(responseError, 'response.data.message', '接口错误');
        break;
      case 500:
        responseError.message = get(responseError, 'response.data.message', errorMsg);
        break;
      default:
        responseError.message = get(responseError, 'response.data.message', '出错啦');
    }
  }
  responseError.config && !responseError.config.noShowDefaultError && global.vbus.$emit('global.$Message.show', responseError.message);

  return Promise.reject(responseError);
}

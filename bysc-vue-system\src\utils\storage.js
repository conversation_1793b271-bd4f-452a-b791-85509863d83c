/** 本地存储 */
// 注：每个子项目必须要设置不一样的前缀
const prefix = 'bw_';
export default {
  session: window.sessionStorage,
  local: window.localStorage,
  set(type, key, value) {
    if (this.isString(value)) {
      return this[type].setItem(key, value);
    }
    if (this.isObject(value)) {
      try {
        value = JSON.stringify(value);
      } catch (error) {
        console.log('err');
      }
      return this[type].setItem(key, value);
    }
    return this[type].setItem(key, value);
  },
  get(type, key) {
    let value = this[type].getItem(key);
    if (this.isParse(value)) {
      try {
        value = JSON.parse(value);
      } catch (error) {
        value = this[type].getItem(key);
      }
    }
    return value;
  },
  setSession(key, value) {
    this.set('session', prefix + key, value);
  },
  getSession(key) {
    return this.get('session', prefix + key);
  },
  setLocal(key, value) {
    if (key == 'userToken' || key == 'refreshToken') {
      $cookies.set(key, value);
    }
    this.set('local', prefix + key, value);
  },
  getLocal(key) {
    return this.get('local', prefix + key);
  },
  isString(value) {
    return typeof value === 'string';
  },
  isObject(value) {
    return typeof value === 'object';
  },
  removeLocal(key) {
    $cookies.remove(key);
    this.local.removeItem(key);
  },
  removeSession(key) {
    this.session.removeItem(key);
  },
  clearLocal() {
    this.local.clear();
  },
  clearSession() {
    this.session.clear();
  },
  isParse(value) {
    if (!value) {
      return false;
    }
    return !!(value.indexOf('{') !== -1
      || value.indexOf('[') !== -1
      || value.indexOf('('));
  }
};

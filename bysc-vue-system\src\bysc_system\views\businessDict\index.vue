<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 10:11:20
 * @FilePath: \kdsp_vue_clear\src\systemView\views\menus\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="sysDict/dict-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          ref="grid"
        >
          <div slot="search">
            <el-input
              style="width: 200px;margin:0 10px 0 0;"
              v-model.trim="searchForm.dictName"
              size="small"
              placeholder="请输入字典名称"
            ></el-input>
            <el-input
              style="width: 200px;"
              v-model.trim="searchForm.dictCode"
              size="small"
              placeholder="请输入字典代码"
            ></el-input>
            <el-button size="small" type="primary" style="margin:0 0 0 10px;" @click="searchTable">搜索</el-button>
            <el-button size="small" @click="resetTable">重置</el-button>
          </div>
          <div slot="action">
            <el-button size="small" type="primary" @click="handleAdd"
              >添加</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading"  :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{scope.row[item.slot]?'启用':'禁用'}}
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="150"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                    @click="handleEdit(scope.row)"
                    type="text"
                    size="small"
                    >编辑</el-button
                  >
                </template>
                <template>
                  <el-button
                    style="margin-right:6px"
                    @click="handleConfig(scope.row)"
                    type="text"
                    size="small"
                    >配置</el-button
                  >
                </template>
                <template>
                  <el-popconfirm
                  @confirm="handleDelete(scope.row.id)"
                    title="您确定要删除该字典吗？"
                  >
                    <el-button type="text" size="small" slot="reference">删除</el-button>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="字典名称" prop="dictName">
            <el-input
              size="small"
              placeholder="请输入字典名称"
              maxlength="32"
              v-model.trim="ruleForm.dictName"
            ></el-input>
          </el-form-item>
          <el-form-item label="字典代码" prop="dictCode">
            <el-input
              size="small"
              placeholder="请输入字典代码"
              maxlength="32"
              v-model.trim="ruleForm.dictCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="字典排序" prop="dictOrder">
            <el-input-number v-model="ruleForm.dictOrder" controls-position="right" size="small" placeholder="请输入字典排序" :min="0" :max="999" style="width:200px;"></el-input-number>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch v-model.trim="status"></el-switch>
          </el-form-item>
          <el-form-item label="备注" prop="comments">
            <el-input
              size="small"
              placeholder="请输入备注"
              maxlength="200"
              type="textarea"
              v-model.trim="ruleForm.comments"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              size="small"
              type="primary"
              @click="submitForm('ruleForm')"
              >保存</el-button
            >
            <el-button size="small" @click="resetForm('ruleForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <el-dialog :title="configDialogName" fullscreen :visible.sync="configDialog" width="30%">
      <div>
        <el-row>
          <el-col :span="24">
            <Grid
              api="sysDict/dict-item-page"
              :event-bus="searchConfigEventBus"
              :search-params="configSearchForm"
              :newcolumn="configColumns"
              @datas="getConfigDatas"
              @columnChange="getConfigColumn"
              ref="configGrid"
            >
              <div slot="search">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="configSearchForm.dictName"
                  size="small"
                  placeholder="请输入字典名称"
                ></el-input>
                <el-input
                  style="width: 200px;"
                  v-model.trim="configSearchForm.dictCode"
                  size="small"
                  placeholder="请输入字典代码"
                ></el-input>
                <el-button size="small" type="primary" style="margin:0 0 0 10px;" @click="searchConfigTable">搜索</el-button>
                <el-button size="small" @click="resetConfigTable">重置</el-button>
              </div>
              <div slot="action">
                <el-button size="small" type="primary" @click="handleAddItem"
                  >添加</el-button
                >
              </div>
              <el-table slot="table" slot-scope="{loading}" v-loading="loading"  :data="configTableData" stripe style="width: 100%">
                <el-table-column fixed="left" :align="'center'" type="selection" width="55">
                </el-table-column>
                <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
                </el-table-column>
                <template v-for="(item, index) in configColumns">
                  <el-table-column
                    v-if="item.slot"
                    :show-overflow-tooltip="true"
                    :align="item.align ? item.align : 'center'"
                    :key="index"
                    :prop="item.key"
                    :label="item.title"
                    min-width="180"
                  >
                    <template slot-scope="scope">
                      {{scope.row[item.slot]?'启用':'禁用'}}
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else
                    :show-overflow-tooltip="true"
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :min-width="item.width ? item.width : '150'"
                    :align="item.align ? item.align : 'center'"
                  >
                  </el-table-column>
                </template>
                <el-table-column
                  fixed="right"
                  align="center"
                  label="操作"
                  type="action"
                  width="150"
                >
                  <template slot-scope="scope">
                    <el-button
                      @click="handleConfigEdit(scope.row)"
                      type="text"
                      size="small"
                      >编辑</el-button
                    >
                    <template>
                      <el-popconfirm
                      @confirm="handleConfigDelete(scope.row.id)"
                        title="您确定要删除该字典吗？"
                      >
                        <el-button type="text" size="small" slot="reference">删除</el-button>
                      </el-popconfirm>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </Grid>
          </el-col>
        </el-row>
        <el-drawer
          size="50%"
          :title="configDrawerName"
          :visible.sync="configDrawer"
          :direction="direction"
          :wrapperClosable="false"
          :append-to-body="true"
        >
          <div style="width: 100%; padding: 0 10px">
            <el-form
              :model="configRuleForm"
              :rules="configRules"
              ref="configRuleForm"
              label-width="100px"
              class="demo-ruleForm"
            >
              <el-form-item label="字典名称" prop="dictName">
                <el-input
                  size="small"
                  placeholder="请输入字典名称"
                  maxlength="32"
                  v-model.trim="configRuleForm.dictName"
                ></el-input>
              </el-form-item>
              <el-form-item label="字典代码" prop="dictCode">
                <el-input
                  size="small"
                  placeholder="请输入字典代码"
                  maxlength="32"
                  v-model.trim="configRuleForm.dictCode"
                ></el-input>
              </el-form-item>
              <el-form-item label="字典排序" prop="dictOrder">
                <el-input-number v-model="configRuleForm.dictOrder" controls-position="right" size="small" placeholder="请输入字典排序" :min="0" :max="999" style="width:200px;"></el-input-number>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-switch v-model.trim="configStatus"></el-switch>
              </el-form-item>
              <el-form-item label="备注" prop="comments">
                <el-input
                  size="small"
                  placeholder="请输入备注"
                  maxlength="200"
                  type="textarea"
                  v-model.trim="configRuleForm.comments"
                ></el-input>
              </el-form-item>

              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  @click="submitConfigForm('configRuleForm')"
                  >保存</el-button
                >
                <el-button size="small" @click="resetForm('configRuleForm')"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="configDialog = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {
  dictName: '',
  dictCode: ''
};
const defaultForm = {
  id: null,
  dictName: '',
  dictCode: '',
  dictOrder: null,
  dictStatus: 1,
  comments: ''
};
const defaultConfigSearchForm = {
  dictId: null,
  dictName: null,
  dictCode: null
};
const defaultConfigForm = {
  id: null,
  dictName: '',
  dictCode: '',
  dictOrder: null,
  dictStatus: 1,
  comments: ''
};
export default {
  components: {Grid},
  destroyed() {
    this.searchEventBus.$off();
    this.searchConfigEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    this.searchConfigEventBus = new Vue();
    return {
      configDrawer: false,
      configDrawerName: '添加',
      configStatus: true,
      configRuleForm: _.cloneDeep(defaultConfigForm),
      configRules: {
        dictName: [
          {required: true, message: '请输入字典名称', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
        dictCode: [
          {required: true, message: '请输入字典代码', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
        dictOrder: [
          {required: true, message: '请输入字典排序', trigger: 'change,blur'},
        ],
      },
      configSearchForm: _.cloneDeep(defaultConfigSearchForm),
      configColumns: [
        {
          title: '字典名称',
          key: 'dictName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '字典代码',
          key: 'dictCode',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '字典排序',
          key: 'dictOrder',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '字典状态',
          slot: 'dictStatus',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '创建时间',
          key: 'createTime',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
      ],
      configTableData: [],
      itemId: null,
      configDialogName: '',
      configDialog: false,

      status: true,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        dictName: [
          {required: true, message: '请输入字典名称', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
        dictCode: [
          {required: true, message: '请输入字典代码', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
        dictOrder: [
          {required: true, message: '请输入字典排序', trigger: 'change,blur'},
        ],
      },
      drawerName: '添加',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '字典名称',
          key: 'dictName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '字典代码',
          key: 'dictCode',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '字典排序',
          key: 'dictOrder',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '字典状态',
          slot: 'dictStatus',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '创建时间',
          key: 'createTime',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
      ],
      tableData: [],
    };
  },
  watch: {
    status(val) {
      this.ruleForm.dictStatus = val ? 1 : 0;
    },
    configStatus(val) {
      this.configRuleForm.dictStatus = val ? 1 : 0;
    }
  },
  mounted() {
  },

  methods: {
    handleAdd() {
      this.drawer = true;
      this.drawerName = '添加';
      this.ruleForm = _.cloneDeep(defaultForm);
    },
    handleEdit(row) {
      this.status = !!row.dictStatus;
      this.ruleForm = Object.assign({}, row);
      this.drawer = true;
      this.drawerName = '编辑';
    },
    handleDelete(e) {
      this.$api['sysDict/dict-delete']({id: e}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$api['sysDict/dict-save'](this.ruleForm).then(data => {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            this.drawer = false;
            this.$refs.grid.query();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getColumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },

    handleConfig(row) {
      this.configSearchForm = _.cloneDeep(defaultConfigSearchForm);
      this.itemId = row.id;
      this.configSearchForm.dictId = this.itemId;
      this.$nextTick(() => {
        this.$refs.configGrid.query();
      });
      this.configDialog = true;
      this.configDialogName = '[' + row.dictName + ']字典值列表';
    },
    getConfigColumn(e) {
      this.configColumns = e;
    },
    getConfigDatas(e) {
      this.configTableData = e;
    },
    searchConfigTable() {
      this.$refs.configGrid.query();
    },
    resetConfigTable() {
      this.configSearchForm = _.cloneDeep(defaultConfigSearchForm);
      this.configSearchForm.dictId = this.itemId;
      this.$nextTick(() => {
        this.$refs.configGrid.query();
      });
    },
    handleAddItem() {
      this.configDrawer = true;
      this.configDrawerName = '添加';
      this.configRuleForm = _.cloneDeep(defaultConfigForm);
    },
    handleConfigEdit(row) {
      this.configStatus = !!row.dictStatus;
      this.configRuleForm = Object.assign({}, row);
      this.configDrawer = true;
      this.configDrawerName = '编辑';
    },
    handleConfigDelete(e) {
      this.$api['sysDict/dict-item-delete']({ids: [e]}).then(data => {
        this.$refs.configGrid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    submitConfigForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.configRuleForm.dictId = this.itemId;
          this.$api['sysDict/dict-item-save'](this.configRuleForm).then(data => {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            this.configDrawer = false;
            this.$refs.configGrid.query();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped></style>

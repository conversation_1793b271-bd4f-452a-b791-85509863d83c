{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\Main.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\Main.vue", "mtime": 1745205562749}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Main.vue?vue&type=template&id=61fb690f&scoped=true\"\nimport script from \"./Main.vue?vue&type=script&lang=js\"\nexport * from \"./Main.vue?vue&type=script&lang=js\"\nimport style0 from \"./Main.vue?vue&type=style&index=0&id=61fb690f&scoped=true&lang=less\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61fb690f\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('61fb690f')) {\n      api.createRecord('61fb690f', component.options)\n    } else {\n      api.reload('61fb690f', component.options)\n    }\n    module.hot.accept(\"./Main.vue?vue&type=template&id=61fb690f&scoped=true\", function () {\n      api.rerender('61fb690f', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/main/Main.vue\"\nexport default component.exports"]}
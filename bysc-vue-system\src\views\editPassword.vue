<template>
  <Card>
    <br/><br/>
    <Form ref="formValidate" :model="formData" :rules="ruleValidate"  :label-width="80">
      <Row justify="center" type="flex">
        <Col span="14">
          <FormItem label="旧密码" prop="oldPassword">
            <Input type="password" v-model="formData.oldPassword" placeholder="请输入旧密码"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row justify="center" type="flex">
        <Col span="14">
          <FormItem label="新密码" prop="newPassword">
            <Input type="password" v-model="formData.newPassword" placeholder="请输入新密码"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row justify="center" type="flex">
        <Col span="14">
          <FormItem label="再次输入" prop="confirmPassword">
            <Input type="password" v-model="formData.confirmPassword" placeholder="请再次输入新密码"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row justify="center" type="flex">
        <Col span="8">
          <Form-item >
            <Button
              type="primary"
              @click="submitForm()"
              style="margin-right: 40px"
            >
              保存
            </Button>
            <Button @click="handleCancel">重置</Button>
          </Form-item>
        </Col>
      </Row>
    </Form>
  </Card>
</template>

<script>
import {mapActions} from 'vuex';
export default {
  name: 'editPassword',
  inject: ['reload'],
  data() {
    return {
      formData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      systemData: {},
      ruleValidate: {
        oldPassword: [
          {required: true, message: '旧密码不能为空', trigger: 'blur'}
        ],
        newPassword: [
          {required: true, message: '新密码不能为空', trigger: 'blur'},
          // {message: '必须包含数字、字母、特殊字符三种，长度8-16位，支持的特殊字符范围：^$./,;:\'!@#%&*|?+()[]{}', pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)(?=.*[./,;^$:'!@#%&*|?+()[\]{}])[0-9A-Za-z./,;:'!@#%&*|?+()[\]{}^$]{8,16}$/, trigger: 'blur,change'},
          // { min: 6, message: '请输入最少6位', trigger: 'blur' },
          // { max: 20, message: '最多只能输入20位', trigger: 'blur' }
        ],
        confirmPassword: [
          {required: true, message: '请再次输入密码', trigger: 'blur'},
          {validator: this.validatePassCheck, trigger: 'blur'}
        ]
      }
    };
  },
  created() {
    this.getSysDetail();
  },
  methods: {
    ...mapActions(['handleLogOut', 'onLogout']),
    getSysDetail() {
      this.$api['config/getSysConfig']({id: 1}).then(data => {
        this.systemData = data;
      });
    },
    submitForm() {
      this.$refs.formValidate.validate(valid => {
        if (valid) {
          let pwdLength = '{' + this.systemData.minlength + ',12}';
          if (this.systemData.complex == 1) {
            let vali = new RegExp('^(?=.*[0-9])(?=.*[a-zA-Z]).' + pwdLength + '$', '');
            if (!vali.test(this.formData.newPassword)) {
              this.$Message.error('密码请输入数字加字母，长度为' + this.systemData.minlength + '-12');
              return false;
            }
          } else if (this.systemData.complex == 2) {
            let vali = new RegExp('^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).' + pwdLength + '$', '');
            if (!vali.test(this.formData.newPassword)) {
              this.$Message.error('密码请输入数字加大小写字母，长度为' + this.systemData.minlength + '-12');
              return false;
            }
          } else {
            let vali = new RegExp('^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*]).' + pwdLength + '$', '');
            if (!vali.test(this.formData.newPassword)) {
              this.$Message.error('密码请输入数字加大小写字母加特殊字符，长度为' + this.systemData.minlength + '-12，支持的特殊字符范围：!@#$%^&*');
              return false;
            }
          }
          this.$api['account/updatePasswordByToken'](this.formData).then(res => {
            // this.$Message.info('修改密码成功');
            // this.handleCancel()
            // this.$Modal.confirm({
            //   title: '登录过期',
            //   content: '您的密码已被修改，请重新登录',
            //   cancelText: ' ',
            //   onOk: () => {
            //     this.handleLogOut().then(() => {
            //       location.href = '/adminLogin';
            //       localStorage.clear();
            //     });
            //   },
            //   onCancel: () => {
            //     this.handleLogOut().then(() => {
            //       location.href = '/adminLogin';
            //       localStorage.clear();
            //     });
            //   }
            // });
          });
        }
      });
    },
    validatePassCheck(rule, value, callback) {
      if (value !== this.formData.newPassword) {
        callback(new Error('两次密码输入不同'));
      } else {
        callback();
      }
    },
    handleCancel() {
      this.formData = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.$refs.formValidate.resetFields();
      // this.$router.go(-1);
      // this.$router.push({
      //   name: 'adminLogin'
      // })
    }
  }
};
</script>

<style scoped>

</style>

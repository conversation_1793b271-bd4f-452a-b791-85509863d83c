const AdminLogin = () => import('@/views/login/index');
// const datav = () => import('@/components/datav/datav.vue')

// const Register = () => import('@/views/Register.vue')

export default [
  {
    path: '/adminLogin', // 为了转发nginx，避免重名，故而加一个index地址TODO 改进
    name: 'adminLogin',
    component: AdminLogin,
    meta: {
      showInMenu: false
    }
  },
  {
    name: 'errorPage',
    path: '/errorPage',
    // TODO 这里变更首页
    component: () => import('@/views/errorPage'),
    meta: {
      showInMenu: false
    }
  },
  {
    name: 'nofound',
    path: '/nofound',
    // TODO 这里变更首页
    component: () => import('@/views/nofound'),
    meta: {
      showInMenu: false
    }
  },
  {
    name: 'social',
    path: '/social',
    // TODO 这里变更首页
    component: () => import('@/views/social'),
    meta: {
      showInMenu: false
    }
  }
  // {
  //   path: '/register',
  //   name: 'register',
  //   component: Register
  // },
  // {
  //   path: '/about',
  //   name: 'about',
  //   // route level code-splitting
  //   // this generates a separate chunk (about.[hash].js) for this route
  //   // which is lazy-loaded when the route is visited.
  //   component: () => import(/* webpackChunkName: "about" */ '@/views/About.vue')
  // }
];

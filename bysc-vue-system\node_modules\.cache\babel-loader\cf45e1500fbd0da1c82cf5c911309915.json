{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\mutations.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\mutations.js", "mtime": 1745205562808}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.array.find\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es6.array.find-index\";\nimport _ from 'lodash';\nvar defaultState = {\n  userInfo: {},\n  department: {},\n  unreadCount: 0,\n  menuList: [],\n  permissions: [],\n  permissionList: [],\n  systemConfig: {},\n  isAdmin: false,\n  isTenantMode: true,\n  fullScreen: true,\n  emediaModalVisibles: false,\n  loadingShow: false,\n  sysVersion: '',\n  progressList: [],\n  processConditions: [],\n  // processConditions 用于传递流程图需要的条件\n  formItemList: [] // 流程节点表单权限控制——组件列表,\n};\nvar hasCondition = function hasCondition(state, formId) {\n  var needIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var index = state.processConditions.findIndex(function (d) {\n    return d.formId === formId;\n  });\n  return needIndex ? index : index > -1;\n};\nexport var state = _.cloneDeep(defaultState);\nexport var mutations = {\n  // 所有mutations中的方法的第一个参数一定是state变量，用来进行对state中的状态的操作\n  // 第二个参数是可选参数，用于调用该 mutations 方法的时候传参\n  initPConditions: function initPConditions(state, data) {\n    state.processConditions = data;\n  },\n  addPCondition: function addPCondition(state, data) {\n    if (data.formId === null || data.formId === undefined) {\n      return;\n    }\n    if (!hasCondition(state, data.formId)) {\n      state.processConditions.unshift(data);\n    }\n  },\n  delPCondition: function delPCondition(state, formId) {\n    if (formId === null || formId === undefined) {\n      return;\n    }\n    var index = hasCondition(state, formId, true);\n    var cons = state.processConditions;\n    index > -1 && cons.splice(index, 1);\n  },\n  //  * 清除所有的条件\n  clearPCondition: function clearPCondition(state) {\n    state.processConditions = [];\n  },\n  updateFormItemList: function updateFormItemList(state, list) {\n    state.formItemList = list;\n  },\n  setUserInfo: function setUserInfo(state, userInfo) {\n    state.userInfo = userInfo;\n  },\n  setDepartment: function setDepartment(state, department) {\n    state.department = department;\n  },\n  setMenuList: function setMenuList(state, menuList) {\n    state.menuList = menuList;\n  },\n  setPermissions: function setPermissions(state, permissions) {\n    state.permissions = permissions;\n  },\n  setPermissionList: function setPermissionList(state, permissionList) {\n    state.permissionList = permissionList;\n  },\n  setVersion: function setVersion(state, version) {\n    state.sysVersion = version;\n  },\n  setSystemConfig: function setSystemConfig(state, config) {\n    state.systemConfig = config;\n  },\n  setEmediaModalVisibles: function setEmediaModalVisibles(state, emediaModalVisibles) {\n    state.emediaModalVisibles = emediaModalVisibles;\n  },\n  setIsAdmin: function setIsAdmin(state, roleList) {\n    if (Array.isArray(roleList)) {\n      state.isAdmin = roleList.some(function (e) {\n        return e.code === 'ROLE_ADMIN';\n      });\n    }\n  },\n  setFullScreen: function setFullScreen(state, fullScreen) {\n    state.fullScreen = fullScreen;\n  },\n  setProgressList: function setProgressList(state, progressList) {\n    state.progressList = progressList;\n  },\n  clearStore: function clearStore(state, exceptList) {\n    Object.keys(state).every(function (e) {\n      if (Array.isArray(exceptList) && exceptList.indexOf(e) !== -1) {\n        return true;\n      }\n      if (typeof defaultState[e] !== 'undefined') {\n        state[e] = defaultState[e];\n      } else {\n        state[e] = null;\n      }\n      return true;\n    });\n  },\n  setProgress: function setProgress(state, progressObj) {\n    // 修改进度列表\n    if (state.progressList.length) {\n      // 如果进度列表存在\n      if (state.progressList.find(function (item) {\n        return item.path == progressObj.path;\n      })) {\n        // 前面说的path时间戳是唯一存在的，所以如果在进度列表中找到当前的进度对象\n        state.progressList.find(function (item) {\n          return item.path == progressObj.path;\n        }).progress = progressObj.progress; // 改变当前进度对象的progress\n      }\n    } else {\n      state.progressList.push(progressObj); // 当前进度列表为空，没有下载任务，直接将该进度对象添加到进度数组内\n    }\n  },\n  delProgress: function delProgress(state, props) {\n    state.progressList.splice(state.progressList.findIndex(function (item) {\n      return item.path == props;\n    }), 1); // 删除进度列表中的进度对象\n  }\n};", {"version": 3, "names": ["_", "defaultState", "userInfo", "department", "unreadCount", "menuList", "permissions", "permissionList", "systemConfig", "isAdmin", "isTenantMode", "fullScreen", "emediaModalVisibles", "loadingShow", "sysVersion", "progressList", "processConditions", "formItemList", "hasCondition", "state", "formId", "needIndex", "arguments", "length", "undefined", "index", "findIndex", "d", "cloneDeep", "mutations", "initPConditions", "data", "addPCondition", "unshift", "delPCondition", "cons", "splice", "clearPCondition", "updateFormItemList", "list", "setUserInfo", "setDepartment", "setMenuList", "setPermissions", "setPermissionList", "setVersion", "version", "setSystemConfig", "config", "setEmediaModalVisibles", "setIsAdmin", "roleList", "Array", "isArray", "some", "e", "code", "setFullScreen", "setProgressList", "clearStore", "exceptList", "Object", "keys", "every", "indexOf", "setProgress", "progressObj", "find", "item", "path", "progress", "push", "delProgress", "props"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/store/common/mutations.js"], "sourcesContent": ["import _ from 'lodash';\r\nconst defaultState = {\r\n  userInfo: {},\r\n  department: {},\r\n  unreadCount: 0,\r\n  menuList: [],\r\n  permissions: [],\r\n  permissionList: [],\r\n  systemConfig: {},\r\n  isAdmin: false,\r\n  isTenantMode: true,\r\n  fullScreen: true,\r\n  emediaModalVisibles: false,\r\n  loadingShow: false,\r\n  sysVersion: '',\r\n  progressList: [],\r\n  processConditions: [], // processConditions 用于传递流程图需要的条件\r\n  formItemList: [], // 流程节点表单权限控制——组件列表,\r\n};\r\nlet hasCondition = (state, formId, needIndex = false) => {\r\n  let index = state.processConditions.findIndex(d => d.formId === formId);\r\n  return needIndex ? index : index > -1;\r\n};\r\nexport const state = _.cloneDeep(defaultState);\r\nexport const mutations = {\r\n  // 所有mutations中的方法的第一个参数一定是state变量，用来进行对state中的状态的操作\r\n  // 第二个参数是可选参数，用于调用该 mutations 方法的时候传参\r\n  initPConditions(state, data) {\r\n    state.processConditions = data;\r\n  },\r\n  addPCondition(state, data) {\r\n    if (data.formId === null || data.formId === undefined) {\r\n      return;\r\n    }\r\n    if (!hasCondition(state, data.formId)) {\r\n      state.processConditions.unshift(data);\r\n    }\r\n  },\r\n  delPCondition(state, formId) {\r\n    if (formId === null || formId === undefined) {\r\n      return;\r\n    }\r\n    let index = hasCondition(state, formId, true);\r\n    let cons = state.processConditions;\r\n    index > -1 && cons.splice(index, 1);\r\n  },\r\n  //  * 清除所有的条件\r\n  clearPCondition(state) {\r\n    state.processConditions = [];\r\n  },\r\n  updateFormItemList(state, list) {\r\n    state.formItemList = list;\r\n  },\r\n  setUserInfo(state, userInfo) {\r\n    state.userInfo = userInfo;\r\n  },\r\n  setDepartment(state, department) {\r\n    state.department = department;\r\n  },\r\n  setMenuList(state, menuList) {\r\n    state.menuList = menuList;\r\n  },\r\n  setPermissions(state, permissions) {\r\n    state.permissions = permissions;\r\n  },\r\n  setPermissionList(state, permissionList) {\r\n    state.permissionList = permissionList;\r\n  },\r\n  setVersion(state, version) {\r\n    state.sysVersion = version;\r\n  },\r\n  setSystemConfig(state, config) {\r\n    state.systemConfig = config;\r\n  },\r\n  setEmediaModalVisibles(state, emediaModalVisibles) {\r\n    state.emediaModalVisibles = emediaModalVisibles;\r\n  },\r\n  setIsAdmin(state, roleList) {\r\n    if (Array.isArray(roleList)) {\r\n      state.isAdmin = roleList.some(e => {\r\n        return e.code === 'ROLE_ADMIN';\r\n      });\r\n    }\r\n  },\r\n  setFullScreen(state, fullScreen) {\r\n    state.fullScreen = fullScreen;\r\n  },\r\n  setProgressList(state, progressList) {\r\n    state.progressList = progressList;\r\n  },\r\n  clearStore(state, exceptList) {\r\n    Object.keys(state).every(e => {\r\n      if (Array.isArray(exceptList) && exceptList.indexOf(e) !== -1) {\r\n        return true;\r\n      }\r\n      if (typeof defaultState[e] !== 'undefined') {\r\n        state[e] = defaultState[e];\r\n      } else {\r\n        state[e] = null;\r\n      }\r\n      return true;\r\n    });\r\n  },\r\n\r\n  setProgress(state, progressObj) { // 修改进度列表\r\n    if (state.progressList.length) { // 如果进度列表存在\r\n      if (state.progressList.find(item => item.path == progressObj.path)) { // 前面说的path时间戳是唯一存在的，所以如果在进度列表中找到当前的进度对象\r\n        state.progressList.find(item => item.path == progressObj.path).progress = progressObj.progress; // 改变当前进度对象的progress\r\n      }\r\n    } else {\r\n      state.progressList.push(progressObj); // 当前进度列表为空，没有下载任务，直接将该进度对象添加到进度数组内\r\n    }\r\n  },\r\n  delProgress(state, props) {\r\n    state.progressList.splice(state.progressList.findIndex(item => item.path == props), 1); // 删除进度列表中的进度对象\r\n  },\r\n};\r\n"], "mappings": ";;;;AAAA,OAAOA,CAAC,MAAM,QAAQ;AACtB,IAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,CAAC,CAAC;EACZC,UAAU,EAAE,CAAC,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,EAAE;EACZC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,EAAE;EAClBC,YAAY,EAAE,CAAC,CAAC;EAChBC,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,mBAAmB,EAAE,KAAK;EAC1BC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,EAAE;EAChBC,iBAAiB,EAAE,EAAE;EAAE;EACvBC,YAAY,EAAE,EAAE,CAAE;AACpB,CAAC;AACD,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEC,MAAM,EAAwB;EAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAClD,IAAIG,KAAK,GAAGN,KAAK,CAACH,iBAAiB,CAACU,SAAS,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACP,MAAM,KAAKA,MAAM;EAAA,EAAC;EACvE,OAAOC,SAAS,GAAGI,KAAK,GAAGA,KAAK,GAAG,CAAC,CAAC;AACvC,CAAC;AACD,OAAO,IAAMN,KAAK,GAAGnB,CAAC,CAAC4B,SAAS,CAAC3B,YAAY,CAAC;AAC9C,OAAO,IAAM4B,SAAS,GAAG;EACvB;EACA;EACAC,eAAe,WAAfA,eAAeA,CAACX,KAAK,EAAEY,IAAI,EAAE;IAC3BZ,KAAK,CAACH,iBAAiB,GAAGe,IAAI;EAChC,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAACb,KAAK,EAAEY,IAAI,EAAE;IACzB,IAAIA,IAAI,CAACX,MAAM,KAAK,IAAI,IAAIW,IAAI,CAACX,MAAM,KAAKI,SAAS,EAAE;MACrD;IACF;IACA,IAAI,CAACN,YAAY,CAACC,KAAK,EAAEY,IAAI,CAACX,MAAM,CAAC,EAAE;MACrCD,KAAK,CAACH,iBAAiB,CAACiB,OAAO,CAACF,IAAI,CAAC;IACvC;EACF,CAAC;EACDG,aAAa,WAAbA,aAAaA,CAACf,KAAK,EAAEC,MAAM,EAAE;IAC3B,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKI,SAAS,EAAE;MAC3C;IACF;IACA,IAAIC,KAAK,GAAGP,YAAY,CAACC,KAAK,EAAEC,MAAM,EAAE,IAAI,CAAC;IAC7C,IAAIe,IAAI,GAAGhB,KAAK,CAACH,iBAAiB;IAClCS,KAAK,GAAG,CAAC,CAAC,IAAIU,IAAI,CAACC,MAAM,CAACX,KAAK,EAAE,CAAC,CAAC;EACrC,CAAC;EACD;EACAY,eAAe,WAAfA,eAAeA,CAAClB,KAAK,EAAE;IACrBA,KAAK,CAACH,iBAAiB,GAAG,EAAE;EAC9B,CAAC;EACDsB,kBAAkB,WAAlBA,kBAAkBA,CAACnB,KAAK,EAAEoB,IAAI,EAAE;IAC9BpB,KAAK,CAACF,YAAY,GAAGsB,IAAI;EAC3B,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAACrB,KAAK,EAAEjB,QAAQ,EAAE;IAC3BiB,KAAK,CAACjB,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EACDuC,aAAa,WAAbA,aAAaA,CAACtB,KAAK,EAAEhB,UAAU,EAAE;IAC/BgB,KAAK,CAAChB,UAAU,GAAGA,UAAU;EAC/B,CAAC;EACDuC,WAAW,WAAXA,WAAWA,CAACvB,KAAK,EAAEd,QAAQ,EAAE;IAC3Bc,KAAK,CAACd,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EACDsC,cAAc,WAAdA,cAAcA,CAACxB,KAAK,EAAEb,WAAW,EAAE;IACjCa,KAAK,CAACb,WAAW,GAAGA,WAAW;EACjC,CAAC;EACDsC,iBAAiB,WAAjBA,iBAAiBA,CAACzB,KAAK,EAAEZ,cAAc,EAAE;IACvCY,KAAK,CAACZ,cAAc,GAAGA,cAAc;EACvC,CAAC;EACDsC,UAAU,WAAVA,UAAUA,CAAC1B,KAAK,EAAE2B,OAAO,EAAE;IACzB3B,KAAK,CAACL,UAAU,GAAGgC,OAAO;EAC5B,CAAC;EACDC,eAAe,WAAfA,eAAeA,CAAC5B,KAAK,EAAE6B,MAAM,EAAE;IAC7B7B,KAAK,CAACX,YAAY,GAAGwC,MAAM;EAC7B,CAAC;EACDC,sBAAsB,WAAtBA,sBAAsBA,CAAC9B,KAAK,EAAEP,mBAAmB,EAAE;IACjDO,KAAK,CAACP,mBAAmB,GAAGA,mBAAmB;EACjD,CAAC;EACDsC,UAAU,WAAVA,UAAUA,CAAC/B,KAAK,EAAEgC,QAAQ,EAAE;IAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MAC3BhC,KAAK,CAACV,OAAO,GAAG0C,QAAQ,CAACG,IAAI,CAAC,UAAAC,CAAC,EAAI;QACjC,OAAOA,CAAC,CAACC,IAAI,KAAK,YAAY;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAACtC,KAAK,EAAER,UAAU,EAAE;IAC/BQ,KAAK,CAACR,UAAU,GAAGA,UAAU;EAC/B,CAAC;EACD+C,eAAe,WAAfA,eAAeA,CAACvC,KAAK,EAAEJ,YAAY,EAAE;IACnCI,KAAK,CAACJ,YAAY,GAAGA,YAAY;EACnC,CAAC;EACD4C,UAAU,WAAVA,UAAUA,CAACxC,KAAK,EAAEyC,UAAU,EAAE;IAC5BC,MAAM,CAACC,IAAI,CAAC3C,KAAK,CAAC,CAAC4C,KAAK,CAAC,UAAAR,CAAC,EAAI;MAC5B,IAAIH,KAAK,CAACC,OAAO,CAACO,UAAU,CAAC,IAAIA,UAAU,CAACI,OAAO,CAACT,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7D,OAAO,IAAI;MACb;MACA,IAAI,OAAOtD,YAAY,CAACsD,CAAC,CAAC,KAAK,WAAW,EAAE;QAC1CpC,KAAK,CAACoC,CAAC,CAAC,GAAGtD,YAAY,CAACsD,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLpC,KAAK,CAACoC,CAAC,CAAC,GAAG,IAAI;MACjB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EAEDU,WAAW,WAAXA,WAAWA,CAAC9C,KAAK,EAAE+C,WAAW,EAAE;IAAE;IAChC,IAAI/C,KAAK,CAACJ,YAAY,CAACQ,MAAM,EAAE;MAAE;MAC/B,IAAIJ,KAAK,CAACJ,YAAY,CAACoD,IAAI,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,IAAI,IAAIH,WAAW,CAACG,IAAI;MAAA,EAAC,EAAE;QAAE;QACpElD,KAAK,CAACJ,YAAY,CAACoD,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACC,IAAI,IAAIH,WAAW,CAACG,IAAI;QAAA,EAAC,CAACC,QAAQ,GAAGJ,WAAW,CAACI,QAAQ,CAAC,CAAC;MAClG;IACF,CAAC,MAAM;MACLnD,KAAK,CAACJ,YAAY,CAACwD,IAAI,CAACL,WAAW,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;EACDM,WAAW,WAAXA,WAAWA,CAACrD,KAAK,EAAEsD,KAAK,EAAE;IACxBtD,KAAK,CAACJ,YAAY,CAACqB,MAAM,CAACjB,KAAK,CAACJ,YAAY,CAACW,SAAS,CAAC,UAAA0C,IAAI;MAAA,OAAIA,IAAI,CAACC,IAAI,IAAII,KAAK;IAAA,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1F;AACF,CAAC", "ignoreList": []}]}
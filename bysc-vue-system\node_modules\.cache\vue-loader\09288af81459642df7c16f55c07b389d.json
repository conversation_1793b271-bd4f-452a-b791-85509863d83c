{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\nofound.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\nofound.vue", "mtime": 1745205562851}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./nofound.vue?vue&type=template&id=1d3090b7&scoped=true\"\nimport script from \"./nofound.vue?vue&type=script&lang=js\"\nexport * from \"./nofound.vue?vue&type=script&lang=js\"\nimport style0 from \"./nofound.vue?vue&type=style&index=0&id=1d3090b7&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d3090b7\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1d3090b7')) {\n      api.createRecord('1d3090b7', component.options)\n    } else {\n      api.reload('1d3090b7', component.options)\n    }\n    module.hot.accept(\"./nofound.vue?vue&type=template&id=1d3090b7&scoped=true\", function () {\n      api.rerender('1d3090b7', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/nofound.vue\"\nexport default component.exports"]}
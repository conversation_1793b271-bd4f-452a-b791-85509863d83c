<template>
  <div class="scoket"></div>
</template>

<script>
export default {
  name: 'WebsocketConnectTest',
  data() {
    return {
      wsUrl: 'ws://localhost',
      websock: null, // ws实例
      startPort: 6146, // 链接webscoket的开始端口
      tryTime: 0, // 当前重连次数
      len: 10, // 设置重连次数
      connectEnd: false,
      errorMsg: '监测失败！请稍后重试',
      msg: {
        type: 1, // 1请求成功，2服务端返回的消息，3断开连接
        data: {}
      }
    };
  },
  mounted() {
    // this.initWebSocket();
  },
  beforeDestroy() {
    // 离开路由之后断开websocket连接
    this.connectEnd = true;
    this.websock && this.websock.close();
    this.len = 0;
  },
  methods: {
    // 初始化weosocket
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        this.$Message.error('您的浏览器不支持WebSocket，无法获取数据');
        return false;
      }
      let port = '';
      if (this.len == 1) {
        port = this.startPort;
      } else {
        // eslint-disable-next-line radix
        port = parseInt(this.startPort) + parseInt(this.tryTime);
      }
      this.websock = new WebSocket(this.wsUrl + ':' + port);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen(e) {
      // 链接成功
      this.tryTime = 0;// 成功后重试次数重置
      this.msg.data = e;
      this.msg.type = 1;
      this.$emit('wsMsg', this.msg);
    },
    websocketonerror() {
      // 连接建立失败重连
      // this.initWebSocket();
    },
    websocketonmessage(e) {
      // 数据接收
      const redata = JSON.parse(e.data);
      this.msg.type = 2;
      this.msg.data = redata;
      this.$emit('wsMsg', this.msg);
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data);
    },
    websocketclose(e) {
      if (this.connectEnd) {
        return false;
      }
      // 关闭
      this.msg.data = e;
      this.msg.type = 3;
      this.$emit('wsMsg', this.msg);
      let that = this;
      if (e && e.code !== 1000) {
        if (this.tryTime < this.len) {
          setTimeout(function () {
            that.websock = null;
            that.tryTime += 1;
            that.initWebSocket();
            console.log(`第${that.tryTime}次重连`);
          }, 100);
        } else {
          this.msg.data = e;
          this.msg.type = 4;
          this.$emit('wsMsg', this.msg);
          this.errorMsg && this.$Message.error(this.errorMsg);
          this.tryTime = 0;
        }
        // Message.error('ws连接异常，请稍候重试')
        // errorCallback()
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>

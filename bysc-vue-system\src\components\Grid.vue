<template>
  <div>
    <div v-show="showSearchForm" style="width: 100%;margin-bottom: 20px;">
      <slot name="search"></slot>
    </div>
    <el-row>
      <el-col :span="24">
        <div style="float: left">
          <slot name="action"></slot>
        </div>
        <el-drawer :wrapperClosable="false"
          size="200px"
          title="展示/隐藏列"
          :visible.sync="drawer"
          :direction="direction"
          :append-to-body="true"
        >
          <div style="padding: 0 20px; z-index: 999">
            <div @click.stop="showSearchForm = showSearchForm">
              <el-checkbox-group
                @change="checkAllGroupChange($event, index)"
                v-for="(item, index) of checkAllGroup"
                :key="index + 'CheckboxGroup'"
                v-model="checkAllGroup1"
              >
                <el-checkbox
                  style="float: left; clear: both"
                  :label="item.title"
                ></el-checkbox>
              </el-checkbox-group>
              <div
                style="
                  text-align: center;
                  width: 100%;
                  clear: both;
                  font-size: 14px;
                  color: cornflowerblue;
                  position: relative;
                  top: 10px;
                "
                v-show="checkAllGroup.length > checkAllGroup1.length"
              >
                <a @click="showAll">全部展示</a>
              </div>
            </div>
          </div>
        </el-drawer>
        <el-tooltip
          v-if="showColumn"
          style="float: right; margin-bottom: 10px"
          content="操作列"
          placement="top"
          :transfer="true"
        >
          <div>
            <el-button
              size="small"
              circle
              @click.native="drawer = true"
              icon="el-icon-s-grid"
            ></el-button>
          </div>
        </el-tooltip>
        <el-tooltip
          v-if="showReset"
          style="float: right; margin-right: 10px; margin-bottom: 10px"
          content="刷新"
          placement="top"
          :transfer="true"
        >
          <el-button
            size="small"
            circle
            @click.native="query"
            icon="el-icon-refresh"
          ></el-button>
        </el-tooltip>
        <el-tooltip
          v-if="showSearch"
          style="float: right; margin-right: 10px; margin-bottom: 10px"
          :content="showSearchForm ? '隐藏搜索' : '展示搜索'"
          placement="top"
          :transfer="true"
        >
          <el-button
            size="small"
            @click="showSearchForm = !showSearchForm"
            circle
            icon="el-icon-search"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <slot name="other"></slot>
    <slot name="table" :data="rows" :loading="loading"></slot>
    <el-row
      type="flex"
      justify="end"
      style="margin-top: 10px; padding-bottom: 5px; margin-bottom: 10px"
    >
      <el-pagination
        v-show="showPage"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
        :current-page.sync="localCurrentPage"
        :page-sizes="pageSizeOpts"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    showPage: {
      type: Boolean,
      default() {
        return true;
      },
    },
    eventBus: Object,
    newcolumn: {
      type: Array,
      default() {
        return [];
      },
    },
    searchParams: {
      type: Object,
      default() {
        return {
        };
      },
    },
    autoLoad: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showColumn: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showReset: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showSearch: {
      type: Boolean,
      default() {
        return true;
      },
    },
    api: {
      type: String,
      default() {
        return '';
      },
    },
    pageSizeOpts: {
      type: Array,
      default() {
        return [10, 20, 50];
      },
      validator(val) {
        return (
          Array.isArray(val)
          && val.length > 0
          && val.reduce((result, item) => {
            return typeof item === 'number' && item > 0 && result;
          }, true)
        );
      },
    },
    currentPage: {
      type: Number,
      default: 1,
    },
  },
  inject: ['showLoading', 'hideLoading'],
  watch: {
    currentPage: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          this.localCurrentPage = newVal;
        }
      },
    },
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      single: false,
      showSearchForm: true,
      checkAllGroup1: [],
      checkAllGroup: [],
      columnArr: [],
      localCurrentPage: 1,
      loading: false,
      rows: [],
      total: 0,
      pageSize: this.pageSizeOpts[0],
    };
  },
  created() {
    if (this.autoLoad) {
      this.query();
    }
    this.eventBus.$on('search', e => {
      if (e !== 'update') {
        this.localCurrentPage = 1;
      }
      this.query();
    });
  },
  mounted() {
    setTimeout(() => {
      this.columnArr = [...this.newcolumn];
      this.columnArr.forEach(e => {
        if (e.title) {
          this.checkAllGroup.push(e);
          this.checkAllGroup1.push(e.title);
        }
      });
    }, 100);
  },
  methods: {
    showAll() {
      let arr = [];
      this.checkAllGroup.forEach(e => {
        arr.push(e.title);
      });
      this.checkAllGroup1 = arr;
      this.checkAllGroupChange(arr);
    },
    checkAllGroupChange(e) {
      let arr = [];
      if (this.columnArr[0] && this.columnArr[0].type == 'selection') {
        arr.push({
          type: 'selection',
          width: 60,
          align: 'center',
          fixed: 'left',
        });
      }
      this.columnArr.forEach(item => {
        let isSome = e.some(row => {
          return row == item.title;
        });
        if (isSome) {
          arr.push(item);
        }
      });
      this.$emit('columnChange', arr);
    },
    query(isTimer) {
      if (isTimer) {
        this.loading = true;
        this.$api[this.api]({
          current: this.localCurrentPage,
          limit: this.pageSize,
          param: this.searchParams
        }
        ).then(res => {
          if (res) {
            this.total = res.total;
            if (Array.isArray(res.list) && res.list.length > 0) {
              this.rows = res.list;
            } else {
              this.rows = [];
            }
            this.$emit('datas', res.list);
            this.$emit('total', res.total);
          }
        }).finally(() => {
          this.loading = false;
        });
      } else {
        this.localCurrentPage = 1;
        this.loading = true;
        this.$api[this.api]({
          current: 1,
          limit: this.pageSize,
          param: this.searchParams
        }).then(res => {
          if (res) {
            this.total = res.total;
            if (Array.isArray(res.list) && res.list.length > 0) {
              this.rows = res.list;
            } else {
              this.rows = [];
            }
            this.$emit('datas', res.list);
            this.$emit('total', res.total);
          }
        }).finally(() => {
          this.loading = false;
        });
      }
    },
    queryData() {
      this.localCurrentPage = 1;
      this.loading = true;
      this.$api[this.api](
        {
          current: this.localCurrentPage,
          limit: this.pageSize,
          param: this.searchParams
        }
      )
        .then(res => {
          if (res) {
            this.total = res.total;
            if (Array.isArray(res.list) && res.list.length > 0) {
              this.rows = res.list;
            } else {
              this.rows = [];
            }
            this.$emit('datas', res.list);
            this.$emit('total', res.total);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handlePageChange(current) {
      this.localCurrentPage = current;
      this.query(true);
      this.$emit('pageChange');
    },
    handlePageSizeChange(pageSize) {
      this.localCurrentPage = 1;
      this.pageSize = pageSize;
      this.query();
    },
    // fanqz add 补充可以获取table当前数据
    getTableData() {
      return this.rows;
    },
    setTableData(rows) {
      this.rows = rows;
    },
  },
};
</script>

import axios from './axios';
import api from './api';
import consts from './const';
import dayjs from 'dayjs';
import store from './store';
// import global from '../global/global'
const global = require('@/global/global');
// 全局ajax
global.ajax = axios;

export default {
  install: (Vue, options) => {
    // 需要挂载的都放在这里
    Vue.prototype.$ajax = axios;
    Vue.prototype.$api = api;
    Vue.prototype.$const = consts;
    Vue.prototype.$dayjs = dayjs;
    Vue.prototype.$havePermission = function (routeName) {
      return store.state.common.permissionList.some(permission => {
        return permission.code.toUpperCase() === routeName.toUpperCase();
      });
    };
    Object.keys(global).map(key => {
      Vue.prototype[key] = global[key];
    });
  }
};

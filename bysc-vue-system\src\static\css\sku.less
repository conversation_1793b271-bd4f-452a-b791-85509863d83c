/** {
  list-style: none;
  padding: 0;
  border: 0;
}*/
#appDikaer {
  padding: 50px;
  .title {
    padding: 0 12px;
    line-height: 1;
    font-size: 18px;
    border-left: 4px solid #50bfff;
    color: #666;
    margin: 0 0 16px 0;
    font-weight: 400;
  }
  .example {
    margin-top: 50px;
    .code-area {
      width: 740px;
      height: 300px;
      box-sizing: border-box;
      padding: 20px;
      border: 2px dashed #c00;
      font-size: 14px;
      line-height: 1.6;
    }
  }
  .specification {
    display: inline-block;
    vertical-align: top;
    .spec-list {
      width: 740px;
      background-color: #fff;
      border: 1px solid #d8d8d8;
      padding: 10px;
      .item {
        margin-top: 5px;
        &:first-child {
          margin-top: 0;
        }
        .name {
          background: #f3f6fb;
          padding: 2px 8px;
          text-align: right;
          overflow: hidden;
          .el-input {
            float: left;
            width: 150px;
          }
          .icon {
            display: none;
            color: #929292;
            cursor: pointer;
            &:hover {
              color: #880000;
            }
          }
          &:hover {
            .icon {
              display: inline-block;
            }
          }
        }
        .values {
          .el-tag {
            margin: 8px 0 0 8px;
          }
          .add-attr {
            display: inline-block;
            vertical-align: top;
            margin-top: 4px;
            .el-input {
              width: 200px;
              margin: 2px 0 0 4px;
            }
          }
        }
      }
      .add-spec {
        font-size: 13px;
      }
    }
  }
  .stock-table {
    width: 740px;
    padding: 0;
    border-collapse: separate;
    border-color: #dfe6ec;
    border-style: solid;
    border-width: 1px 0 0 1px;
    background-color: #fff;
    td,
    th {
      padding: 4px 10px;
      border-bottom: 1px solid #dfe6ec;
      border-right: 1px solid #dfe6ec;
    }
    th {
      line-height: 30px;
      background-color: #eef1f6;
    }
    .link {
      cursor: pointer;
      color: #0088ee;
      font-size: 13px;
      margin-left: 6px;
    }
    .wh-foot {
      line-height: 30px;
      .label {
        display: inline-block;
        vertical-align: top;
      }
      .set-list {
        display: inline-block;
        vertical-align: top;
        font-size: 0;
        .set-item {
          display: inline-block;
          vertical-align: top;
          margin-left: 15px;
          font-size: 13px;
          cursor: pointer;
          color: #0088ee;
        }
      }
      .set-form {
        display: inline-block;
        margin-left: 10px;
        .el-input {
          display: inline-block;
          width: 120px;
        }
        .set-btn {
          display: inline-block;
          padding: 0 2px;
          font-size: 15px;
          cursor: pointer;
          &.blue {
            color: #0088ee;
          }
          &.red {
            color: #cc0000;
          }
        }
      }
      .right {
        float: right;
      }
      .text {
        font-size: 13px;
      }
    }
  }
}

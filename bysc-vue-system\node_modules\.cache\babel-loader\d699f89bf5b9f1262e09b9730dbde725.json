{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\draggable.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\draggable.js", "mtime": 1745205562783}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["// 设置需要拖拽的元素为相对定位，其父元素为绝对定位。\n// 鼠标按下(onmousedown)时记录目标元素当前的 left 和 top 值。\n// 鼠标移动(onmousemove)时计算每次移动的横向距离和纵向距离的变化值，并改变元素的 left 和 top 值\n// 鼠标松开(onmouseup)时完成一次拖拽\n// <div class=\"el-dialog\" v-draggable></div>\nvar draggable = {\n  inserted: function inserted(el) {\n    el.style.cursor = 'move';\n    el.onmousedown = function (e) {\n      var disx = e.pageX - el.offsetLeft;\n      var disy = e.pageY - el.offsetTop;\n      document.onmousemove = function (e) {\n        var x = e.pageX - disx;\n        var y = e.pageY - disy;\n        // eslint-disable-next-line radix\n        var maxX = document.body.clientWidth - parseInt(window.getComputedStyle(el).width);\n        // eslint-disable-next-line radix\n        var maxY = document.body.clientHeight - parseInt(window.getComputedStyle(el).height);\n        if (x < 0) {\n          x = 0;\n        } else if (x > maxX) {\n          x = maxX;\n        }\n        if (y < 0) {\n          y = 0;\n        } else if (y > maxY) {\n          y = maxY;\n        }\n        el.style.left = x + 'px';\n        el.style.top = y + 'px';\n      };\n      document.onmouseup = function () {\n        document.onmousemove = document.onmouseup = null;\n      };\n    };\n  }\n};\nexport default draggable;", {"version": 3, "names": ["draggable", "inserted", "el", "style", "cursor", "onmousedown", "e", "disx", "pageX", "offsetLeft", "disy", "pageY", "offsetTop", "document", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "maxX", "body", "clientWidth", "parseInt", "window", "getComputedStyle", "width", "maxY", "clientHeight", "height", "left", "top", "onmouseup"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/draggable.js"], "sourcesContent": ["// 设置需要拖拽的元素为相对定位，其父元素为绝对定位。\r\n// 鼠标按下(onmousedown)时记录目标元素当前的 left 和 top 值。\r\n// 鼠标移动(onmousemove)时计算每次移动的横向距离和纵向距离的变化值，并改变元素的 left 和 top 值\r\n// 鼠标松开(onmouseup)时完成一次拖拽\r\n// <div class=\"el-dialog\" v-draggable></div>\r\nconst draggable = {\r\n  inserted: function (el) {\r\n    el.style.cursor = 'move';\r\n    el.onmousedown = function (e) {\r\n      let disx = e.pageX - el.offsetLeft;\r\n      let disy = e.pageY - el.offsetTop;\r\n      document.onmousemove = function (e) {\r\n        let x = e.pageX - disx;\r\n        let y = e.pageY - disy;\r\n        // eslint-disable-next-line radix\r\n        let maxX = document.body.clientWidth - parseInt(window.getComputedStyle(el).width);\r\n        // eslint-disable-next-line radix\r\n        let maxY = document.body.clientHeight - parseInt(window.getComputedStyle(el).height);\r\n        if (x < 0) {\r\n          x = 0;\r\n        } else if (x > maxX) {\r\n          x = maxX;\r\n        }\r\n\r\n        if (y < 0) {\r\n          y = 0;\r\n        } else if (y > maxY) {\r\n          y = maxY;\r\n        }\r\n\r\n        el.style.left = x + 'px';\r\n        el.style.top = y + 'px';\r\n      };\r\n      document.onmouseup = function () {\r\n        document.onmousemove = document.onmouseup = null;\r\n      };\r\n    };\r\n  },\r\n};\r\nexport default draggable;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAMA,SAAS,GAAG;EAChBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,EAAE,EAAE;IACtBA,EAAE,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IACxBF,EAAE,CAACG,WAAW,GAAG,UAAUC,CAAC,EAAE;MAC5B,IAAIC,IAAI,GAAGD,CAAC,CAACE,KAAK,GAAGN,EAAE,CAACO,UAAU;MAClC,IAAIC,IAAI,GAAGJ,CAAC,CAACK,KAAK,GAAGT,EAAE,CAACU,SAAS;MACjCC,QAAQ,CAACC,WAAW,GAAG,UAAUR,CAAC,EAAE;QAClC,IAAIS,CAAC,GAAGT,CAAC,CAACE,KAAK,GAAGD,IAAI;QACtB,IAAIS,CAAC,GAAGV,CAAC,CAACK,KAAK,GAAGD,IAAI;QACtB;QACA,IAAIO,IAAI,GAAGJ,QAAQ,CAACK,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAACC,MAAM,CAACC,gBAAgB,CAACpB,EAAE,CAAC,CAACqB,KAAK,CAAC;QAClF;QACA,IAAIC,IAAI,GAAGX,QAAQ,CAACK,IAAI,CAACO,YAAY,GAAGL,QAAQ,CAACC,MAAM,CAACC,gBAAgB,CAACpB,EAAE,CAAC,CAACwB,MAAM,CAAC;QACpF,IAAIX,CAAC,GAAG,CAAC,EAAE;UACTA,CAAC,GAAG,CAAC;QACP,CAAC,MAAM,IAAIA,CAAC,GAAGE,IAAI,EAAE;UACnBF,CAAC,GAAGE,IAAI;QACV;QAEA,IAAID,CAAC,GAAG,CAAC,EAAE;UACTA,CAAC,GAAG,CAAC;QACP,CAAC,MAAM,IAAIA,CAAC,GAAGQ,IAAI,EAAE;UACnBR,CAAC,GAAGQ,IAAI;QACV;QAEAtB,EAAE,CAACC,KAAK,CAACwB,IAAI,GAAGZ,CAAC,GAAG,IAAI;QACxBb,EAAE,CAACC,KAAK,CAACyB,GAAG,GAAGZ,CAAC,GAAG,IAAI;MACzB,CAAC;MACDH,QAAQ,CAACgB,SAAS,GAAG,YAAY;QAC/BhB,QAAQ,CAACC,WAAW,GAAGD,QAAQ,CAACgB,SAAS,GAAG,IAAI;MAClD,CAAC;IACH,CAAC;EACH;AACF,CAAC;AACD,eAAe7B,SAAS", "ignoreList": []}]}
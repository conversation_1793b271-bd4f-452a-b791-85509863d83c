{"name": "emc_vue", "version": "0.1.0", "private": true, "scripts": {"start": "vue-cli-service serve", "build": "vue-cli-service build", "build-test": "vue-cli-service build --mode development", "lint": "vue-cli-service lint", "eslint-fix": "eslint src/**/*.*  --fix"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue/component-compiler-utils": "^3.3.0", "axios": "^0.18.1", "axios-auth-refresh": "^3.1.0", "browserslist": "^4.20.3", "caniuse-lite": "^1.0.30001342", "copy-webpack-plugin": "4.6.0", "core-js": "^2.6.12", "countup": "^1.8.2", "crypto-js": "^4.0.0", "dayjs": "^1.11.2", "e-vue-contextmenu": "^0.1.3", "echarts": "^4.9.0", "echarts-liquidfill": "^2.0.6", "element-ui": "^2.15.10", "js-file-download": "^0.4.12", "js-md5": "^0.7.3", "lodash": "^4.17.21", "moment": "^2.29.3", "node-sass": "^6.0.1", "nprogress": "^0.2.0", "sass-loader": "^10.2.0", "swiper": "^6.8.0", "view-design": "^4.7.0", "vue": "^2.6.14", "vue-awesome-swiper": "^3.1.3", "vue-baidu-map": "^0.21.22", "vue-cookies": "^1.8.3", "vue-grid-layout": "^2.3.12", "vue-i18n": "^8.27.1", "vue-jsonp": "^2.0.0", "vue-loading-overlay": "^3.4.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.4", "vuedraggable": "^2.23.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-plugin-unit-jest": "^5.0.4", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.3", "less": "^3.13.1", "less-loader": "^4.1.0", "lodash-webpack-plugin": "^0.11.6", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue-template-compiler": "^2.6.14", "webpack-bundle-analyzer": "^3.9.0", "webpack-manifest-plugin": "^2.2.0"}}
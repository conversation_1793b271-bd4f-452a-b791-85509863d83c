<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-12-09 09:26:18
 * @FilePath: \bycloud-vue\src\bysc_system\views\role\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="systems/rolePage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-input
              style="width: 200px; margin: 0 10px 0 0"
              v-model.trim="searchForm.name"
              size="small"
              placeholder="请输入名称"
            ></el-input>
            <el-input
              style="width: 200px; margin: 0 10px 0 0"
              v-model.trim="searchForm.code"
              size="small"
              placeholder="请输入标识"
            ></el-input>
            <el-button
              size="small"
              type="primary"
              style="margin: 0 0 0 10px"
              @click="searchTable"
              >搜索</el-button
            >
            <el-button size="small" @click="resetTable">重置</el-button>
          </div>
          <div slot="action">
            <el-button size="small" type="primary" @click="handleAdd"
              >添加</el-button
            >
          </div>
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="tableData"
            stripe
            style="width: 100%"
          >
            <el-table-column
              fixed="left"
              :align="'center'"
              type="selection"
              width="55"
            >
            </el-table-column>
            <el-table-column
              fixed="left"
              :align="'center'"
              label="序号"
              type="index"
              width="50"
            >
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot === 'roleStatus'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{ scope.row[item.slot] ? "启用" : "禁用" }}
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot === 'roleType'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{ scope.row[item.slot] === 1 ? '超管' :
                     scope.row[item.slot] === 2 ? '管理员' :
                     scope.row[item.slot] === 3 ? '普通用户' : '' }}
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button @click="getRoleUser(scope.row)" type="text"
                  >角色用户</el-button
                >
                <el-dropdown style="margin-left: 6px">
                  <span class="el-dropdown-link" style="font-size: 14px">
                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="handleRdit(scope.row)">
                      <el-button type="text">编辑</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="setMenu(scope.row)">
                      <el-button type="text">资源配置</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="setDatas(scope.row)">
                      <el-button type="text">数据配置</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="setApps(scope.row)">
                      <el-button type="text">应用配置</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="scope.row.canDelete"
                      @click.native="handleDelete(scope.row.id)"
                    >
                      <el-button type="text">删除</el-button>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      title="资源配置"
      :visible.sync="menuDrawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px; margin-bottom: 100px">
        <checkTree
          ref="ctree"
          :checkStrictly="checkStrictly"
          :defaultexpandall="true"
          :tree-props="treeProps"
          :tree-data="treedata"
          @treeNode="getSelectKeys"
        ></checkTree>
        <div class="demo-drawer-footer">
          <el-button
            size="small"
            type="primary"
            @click="saveMenu()"
            :loading="loading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="名称" prop="roleName">
            <el-input
              size="small"
              maxlength="15"
              v-model.trim="ruleForm.roleName"
            ></el-input>
          </el-form-item>
          <el-form-item label="标识" prop="roleKey">
            <el-input
              size="small"
              maxlength="32"
              v-model.trim="ruleForm.roleKey"
            ></el-input>
          </el-form-item>

          <el-form-item label="角色类型" prop="roleType">
            <el-select
              v-model="ruleForm.roleType"
              size="small"
              placeholder="请选择角色类型"
            >
              <el-option label="超管" :value="1"></el-option>
              <el-option label="管理员" :value="2"></el-option>
              <el-option label="普通用户" :value="3"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="角色描述" prop="comments">
            <el-input
              type="textarea"
              size="small"
              maxlength="200"
              v-model.trim="ruleForm.comments"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否启用" prop="roleStatus">
            <el-switch v-model.trim="roleStatus"></el-switch>
          </el-form-item>
          <div class="demo-drawer-footer">
            <el-button size="small" @click="closeDrawer">关闭</el-button>
            <el-button
              size="small"
              type="primary"
              @click="submitForm('ruleForm')"
              >保存</el-button
            >
          </div>
        </el-form>
      </div>
    </el-drawer>
    <el-drawer
      size="700px"
      stitle="角色用户"
      :visible.sync="roleDrawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px">
        <roleUser ref="roleUser"></roleUser>
      </div>
    </el-drawer>
    <el-drawer
      size="700px"
      title="数据配置"
      :modal="false"
      :visible.sync="dataDrawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px; position: relative">
        <!-- <span style="font-weight:bold">数据配置：</span> -->
        <el-radio-group v-model="dataForm.permissionType">
          <el-radio style="width: 100%; margin-top: 5px" :label="'ME'"
            >本人</el-radio
          >
          <!-- <el-radio style="width: 100%; margin-top: 5px" :label="'SUBORDINATE'"
            >本人及下属</el-radio
          > -->
          <!-- <el-radio
            style="width: 100%; margin-top: 5px"
            :label="'MY_DEPARTMENT'"
            >本部门</el-radio
          > -->
          <!-- <el-radio
            style="width: 100%; margin-top: 5px"
            :label="'MY_DEPARTMENT_AND_SUB'"
            >本部门及下属</el-radio
          > -->
          <el-radio style="width: 100%; margin-top: 5px" :label="'ALL'"
            >全部</el-radio
          >
          <el-radio style="width: 100%; margin-top: 5px" :label="8"
            >指定范围</el-radio
          >
        </el-radio-group>
        <div style="margin-top: 10px" v-show="dataForm.permissionType === 8">
          <span>指定范围：</span>
          <el-radio-group v-model="dataForm.permissionTypeCode1">
            <el-radio :label="'SPECIFIC_USER'">指定用户</el-radio>
            <el-radio :label="'SPECIFIC_DEPT'">指定部门/分子公司</el-radio>
            <el-radio :label="'SPECIFIC_ROLE'">指定组织角色</el-radio>
          </el-radio-group>
        </div>
        <div
          v-show="dataForm.permissionType === 8"
          style="width: 100%; height: 1px; background: #ccc; margin-top: 15px"
        ></div>
        <div v-show="dataForm.permissionType === 8" style="margin-top: 15px">
          <div
            style="margin-top: 5px"
            v-show="dataForm.permissionTypeCode1 == 'SPECIFIC_USER'"
          >
            <div style="font-weight: bold; margin-bottom: 8px">指定用户</div>
            <el-button
              style="margin-right: 5px"
              @click="addUsers"
              type="primary"
              size="small"
              icon="el-icon-circle-plus-outline"
              >添加</el-button
            >
            <el-tag
              style="margin: 5px"
              v-for="tag in selectedUserLists"
              :key="tag.id"
              closable
              @close="handleUserClose(tag)"
            >
              {{ tag.label }}
            </el-tag>
          </div>
          <div
            style="margin-top: 5px"
            v-show="dataForm.permissionTypeCode1 == 'SPECIFIC_DEPT'"
          >
            <div style="font-weight: bold; margin-bottom: 8px">指定部门</div>
            <el-button
              style="margin-right: 5px"
              @click="addDepts"
              type="primary"
              size="small"
              icon="el-icon-circle-plus-outline"
              >添加</el-button
            >
            <el-tag
              style="margin: 5px"
              v-for="tag in selectedDeptLists"
              :key="tag.id"
              closable
              @close="handleDeptClose(tag)"
            >
              {{ tag.organizationName }}
            </el-tag>
          </div>
          <div
            style="margin-top: 5px"
            v-show="dataForm.permissionTypeCode1 == 'SPECIFIC_ROLE'"
          >
            <div style="font-weight: bold; margin-bottom: 8px">
              指定组织角色
            </div>
            <el-button
              style="margin-right: 5px"
              @click="addRoles"
              type="primary"
              size="small"
              icon="el-icon-circle-plus-outline"
              >添加</el-button
            >
            <el-tag
              style="margin: 5px"
              v-for="tag in selectedRoleLists"
              :key="tag.id"
              closable
              @close="handleClose(tag)"
            >
              {{ tag.roleName }}
            </el-tag>
          </div>
          <el-dialog
            title="指定用户"
            :modal="false"
            :visible.sync="dialogUserVisible"
            width="50%"
          >
            <div style="width: 100%; height: 500px; overflow: auto">
              <!-- <div style="width:100%">
                <el-input
                  placeholder="输入关键字进行过滤"
                  size="small"
                  style="margin-bottom:10px;width:48%"
                  v-model="filterText">
                </el-input>
              </div> -->
              <div
                style="
                  height: 100%;
                  width: 50%;
                  float: left;
                  padding: 10px;
                  overflow-y: scroll;
                "
              >
                <Lazytrees ref="ltree" @treeNode="getUserData"></Lazytrees>
                <!-- <el-tree
                  :data="userLists"
                  show-checkbox
                  :check-strictly="true"
                  @check="getUserTrees"
                  node-key="primaryCode"
                  ref="tree"
                  :default-expand-all="true"
                  :filter-node-method="filterNode"
                  :props="defaultUserProps">
                </el-tree> -->
              </div>
              <div
                style="
                  height: 100%;
                  width: 50%;
                  float: left;
                  padding: 10px;
                  border: 1px solid #f2f2f2;
                "
              >
                <div
                  style="
                    text-align: center;
                    height: 30px;
                    width: 100%;
                    line-height: 30px;
                  "
                >
                  已选择的用户
                </div>
                <div>
                  <el-tag
                    style="margin: 5px"
                    v-for="tag in selectedUserLists"
                    :key="tag.primaryCode"
                    closable
                    @close="handleUserClose(tag)"
                  >
                    {{ tag.label }}
                  </el-tag>
                </div>
              </div>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button
                size="small"
                type="primary"
                @click="dialogUserVisible = false"
                >确 定</el-button
              >
            </span>
          </el-dialog>
          <el-dialog
            title="选择部门"
            :modal="false"
            :visible.sync="dialogDeptVisible"
            width="50%"
          >
            <div style="width: 100%; height: 500px; overflow: auto">
              <div style="height: 100%; width: 50%; float: left; padding: 10px">
                <el-input
                  placeholder="输入关键字进行过滤"
                  size="small"
                  style="margin-bottom: 10px; width: 90%"
                  v-model="filterText"
                >
                </el-input>
                <el-tree
                  :data="DeptLists"
                  show-checkbox
                  :check-strictly="false"
                  @check="getDeptTrees"
                  node-key="id"
                  ref="tree"
                  :filter-node-method="filterDeptNode"
                  :props="defaultDeptProps"
                >
                </el-tree>
              </div>
              <div
                style="
                  height: 100%;
                  width: 50%;
                  float: left;
                  padding: 10px;
                  border: 1px solid #f2f2f2;
                "
              >
                <div
                  style="
                    text-align: center;
                    height: 30px;
                    width: 100%;
                    line-height: 30px;
                  "
                >
                  已选择的部门
                </div>
                <div>
                  <el-tag
                    style="margin: 5px"
                    v-for="tag in selectedDeptLists"
                    :key="tag.id"
                    closable
                    @close="handleDeptClose(tag)"
                  >
                    {{ tag.organizationName }}
                  </el-tag>
                </div>
              </div>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button
                size="small"
                type="primary"
                @click="
                  dialogDeptVisible = false;
                  updateDeptRefIds();
                "
                >确 定</el-button
              >
            </span>
          </el-dialog>
          <el-dialog
            title="组织选择角色"
            :modal="false"
            :visible.sync="dialogRoleVisible"
            width="50%"
          >
            <div style="width: 100%; height: 500px; overflow: auto">
              <div style="height: 100%; width: 50%; float: left; padding: 10px">
                <el-input
                  placeholder="输入关键字进行过滤"
                  size="small"
                  style="margin-bottom: 10px; width: 90%"
                  v-model="filterText"
                >
                </el-input>
                <el-tree
                  :data="roleLists"
                  show-checkbox
                  @check="getRoleTrees"
                  node-key="id"
                  ref="tree"
                  :filter-node-method="filterNode"
                  :props="defaultProps"
                >
                </el-tree>
              </div>
              <div
                style="
                  height: 100%;
                  width: 50%;
                  float: left;
                  padding: 10px;
                  border: 1px solid #f2f2f2;
                "
              >
                <div
                  style="
                    text-align: center;
                    height: 30px;
                    width: 100%;
                    line-height: 30px;
                  "
                >
                  已选择的组织角色
                </div>
                <div>
                  <el-tag
                    style="margin: 5px"
                    v-for="tag in selectedRoleLists"
                    :key="tag.id"
                    closable
                    @close="handleClose(tag)"
                  >
                    {{ tag.roleName }}
                  </el-tag>
                </div>
              </div>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button
                size="small"
                type="primary"
                @click="dialogRoleVisible = false"
                >确 定</el-button
              >
            </span>
          </el-dialog>
        </div>
        <div style="padding-top: 20px">
          <el-divider content-position="left">生效模块</el-divider>
          <el-checkbox-group v-model="dataForm.moduleNames" size="small">
            <div
              style="margin-top: 10px"
              v-for="item in moduleNameLists"
              :key="item.id"
            >
              <el-checkbox :label="item.dictCode" border>{{
                item.dictName
              }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </div>

        <div style="width: 100%; position: absolute; right: 20px; top: 85vh">
          <el-button
            style="float: right"
            size="small"
            type="primary"
            @click="submitData"
            :loading="loading"
            >{{ loading ? "提交中 ..." : "确 定" }}</el-button
          >
        </div>
      </div>
    </el-drawer>
    <!-- 应用配置 -->
    <el-drawer
      size="700px"
      title="应用配置"
      :visible.sync="appDrawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-checkbox-group v-model="appForm.appIds">
          <el-row :gutter="20">
            <el-col
              :span="6"
              v-for="(item, index) in appLists"
              :key="index + 'app'"
            >
              <div class="appCard">
                <el-checkbox
                  style="position: absolute; left: 3px"
                  :key="item.appId"
                  :label="item.appId"
                >
                  <div class="cardTitle">{{ item.appName }}</div>
                </el-checkbox>
              </div>
            </el-col>
          </el-row>
        </el-checkbox-group>
        <div>
          <el-button
            style="float: right; margin-top: 60px"
            @click="saveAppLists()"
            type="primary"
            size="small"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from "vue";
import Grid from "@/components/Grid";
import _ from "lodash";
import commonTree from "@/components/treeComp/commonTree";
import checkTree from "@/components/treeComp/checkTree";
import iconChoose from "@/components/choose/icon-choose";
import roleUser from "@/bysc_system/views/role/roleUser";
import Lazytrees from "@/components/treeComp/ltrees.vue";
const defaultSearchForm = {
  name: "",
  code: "",
};
const defaultForm = {
  comments: "",
  roleKey: "",
  roleName: "",
  roleType: "",
  roleStatus: 1,
};
export default {
  components: {commonTree, Grid, iconChoose, checkTree, roleUser, Lazytrees},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      selectModuleName: [],
      moduleNameLists: [],
      loading: false,
      selectedAppList: [],
      appList: [
        {
          name: "工作流",
          id: 1,
        },
        {
          name: "系统",
          id: 2,
        },
        {
          name: "消息模块",
          id: 3,
        },
        {
          name: "库存",
          id: 4,
        },
        {
          name: "采购",
          id: 5,
        },
        {
          name: "质量",
          id: 6,
        },
        {
          name: "运行",
          id: 7,
        },
      ],
      bgurls: [
        {url: require("./images/bg1.png")},
        {url: require("./images/bg2.png")},
        {url: require("./images/bg3.png")},
      ],
      defaultUserProps: {
        children: "children",
        label: "label",
        roles: [],
      },
      selectedUserLists: [],
      userLists: [],
      dialogUserVisible: false,
      filterText: "",
      defaultProps: {
        children: "children",
        label: "roleName",
        roles: [],
      },
      selectedRoleLists: [],
      roleLists: [],
      dialogRoleVisible: false,
      defaultDeptProps: {
        children: "children",
        label: "organizationName",
        roles: [],
      },
      selectedDeptLists: [],
      DeptLists: [],
      dialogDeptVisible: false,
      dataForm: {
        moduleNames: [],
        permissionType: "",
        permissionTypeCode: "",
        permissionTypeCode1: "",
        refIds: [],
        roleId: null,
        allUserDeptIds: [],
      },
      appForm: {
        appIds: [],
        roleId: null,
      },
      dataDrawer: false,
      appDrawer: false,
      dialogVisible: false,
      roleStatus: true,
      checkStrictly: true,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        roleName: [
          {required: true, message: "请输入名称", trigger: "blur"},
          {
            min: 1,
            max: 15,
            message: "长度在 1 到 15 个字符",
            trigger: "blur",
          },
        ],
        roleKey: [{required: true, message: "请输入标识", trigger: "blur"}],
        roleType: [
          {required: true, message: "请选择角色类型", trigger: "change"},
        ],
      },
      drawerName: "添加",
      drawer: false,
      menuDrawer: false,
      direction: "rtl",
      searchForm: _.cloneDeep(defaultSearchForm),
      treeProps: {
        children: "children",
        label: "resourceName",
      },
      resourceName: "",
      columns: [
        {
          title: "名称",
          key: "roleName",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "标识",
          key: "roleKey",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: "角色状态",
          slot: "roleStatus",
          tooltip: true,
          minWidth: 170,
        },

        {
          title: "角色类型",
          slot: "roleType",
          tooltip: true,
          minWidth: 170,
        },
        {
          title: "角色描述",
          key: "comments",
          tooltip: true,
          minWidth: 170,
        },
      ],
      permissionsList: [],
      roleName: "",
      tableData: [],
      selectedResouce: {},
      treedata: [],
      resourceIds: [],
      roleForm: {
        resourceIds: [],
        roleId: "",
      },
      roleId: "",
      roleDrawer: false,
      appLists: [],
    };
  },
  watch: {
    roleStatus(val) {
      this.ruleForm.roleStatus = val ? 1 : 0;
    },
    // 'dataForm.permissionTypeCode1'(val) {
    //   if (val === 'SPECIFIC_USER') {
    //     this.selectedUserLists = [];
    //     this.selectedRoleLists = [];
    //   } else if (val === 'SPECIFIC_DEPT') {
    //     this.selectedRoleLists = [];
    //     this.selectedUserLists = [];
    //   } else if (val === 'SPECIFIC_ROLE') {
    //     this.selectedDeptLists = [];
    //     this.selectedUserLists = [];
    //   }
    // },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    this.getRoleLists();
    this.getDeptTreeLists();
    this.getUserLists();
    this.getParamDatas();
  },

  methods: {
    getParamDatas() {
      this.$api["sysDict/getParam"]({
        code: "DATA_PERMISSION_MODULE_NAMES",
      }).then(data => {
        this.moduleNameLists = data;
      });
    },
    updateDeptRefIds() {
      // 清空之前的 refIds
      this.dataForm.refIds = [];

      // 从当前选中的部门列表中重新构建 refIds
      this.selectedDeptLists.forEach(item => {
        this.dataForm.refIds.push(item.id);
      });

      // 记录修改后的 refIds
    },
    getUserData(e) {
      let refIds = [];
      e.forEach(r => {
        refIds.push(r.primaryCode);
      });
      // this.dataForm.refIds = [];
      this.dataForm.allUserDeptIds = refIds;
      this.selectedUserLists = e;
    },
    submitData() {
      this.loading = true;
      if (this.dataForm.permissionType == 8) {
        this.dataForm.permissionTypeCode = this.dataForm.permissionTypeCode1;

        // 根据不同的权限类型，确保 refIds 是最新的
        if (this.dataForm.permissionTypeCode1 === "SPECIFIC_DEPT") {
          // 如果是指定部门，调用 updateDeptRefIds 确保 refIds 是最新的
          this.updateDeptRefIds();
        } else if (this.dataForm.permissionTypeCode1 === "SPECIFIC_ROLE") {
          // 如果是指定角色，确保 refIds 是最新的
          this.dataForm.refIds = [];
          this.selectedRoleLists.forEach(item => {
            this.dataForm.refIds.push(item.id);
          });
        } else if (this.dataForm.permissionTypeCode1 === "SPECIFIC_USER") {
          // 如果是指定用户，确保 allUserDeptIds 是最新的
          this.dataForm.refIds = [];
          this.selectedUserLists.forEach(item => {
            // 用户ID可能带有 "user" 前缀，需要去掉
            const userId = item.primaryCode.startsWith("user")
              ? item.primaryCode.substring(4)
              : item.primaryCode;
            this.dataForm.refIds.push(userId);
          });
        }
      } else {
        this.dataForm.permissionTypeCode = this.dataForm.permissionType;
      }

      // 打印最终要提交的数据
      this.$api["systems/datapermission"](this.dataForm)
        .then(data => {
          this.$message.success("保存成功");
          this.dataDrawer = false;
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
          console.error("提交数据出错:", err);
        });
    },
    getAppLists(id) {
      this.$api["systems/getAppLists"]().then(data => {
        this.appLists = data;
        this.$api["systems/roleGrantedApps"]({roleId: id}).then(data => {
          this.appForm.appIds = data;
        });
      });
    },
    saveAppLists(id) {
      this.$api["systems/saveRoleApp"](this.appForm).then(data => {
        this.$message.success("应用配置成功");
        this.appDrawer = false;
      });
    },
    getUserLists() {
      this.$api["systems/org-user-tree"]().then(data => {
        this.userLists = data;
      });
    },
    handleClose(tag) {
      let index = this.selectedRoleLists.findIndex(e => {
        return e.id === tag.id;
      });
      this.selectedRoleLists.splice(index, 1);
      let ids = [];
      this.selectedRoleLists.forEach(element => {
        ids.push(element.id);
      });

      // 添加安全检查，确保 this.$refs.tree 存在
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys(ids);
      } else {
        console.warn("this.$refs.tree is undefined in handleClose");
      }

      // 重新设置 dataForm.refIds，确保只包含当前选中的角色 ID
      this.dataForm.refIds = [...ids];
    },
    handleDeptClose(tag) {
      let index = this.selectedDeptLists.findIndex(e => {
        return e.id === tag.id;
      });
      this.selectedDeptLists.splice(index, 1);
      let ids = [];
      this.selectedDeptLists.forEach(element => {
        ids.push(element.id);
      });

      // 添加安全检查，确保 this.$refs.tree 存在
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys(ids);
      } else {
        console.warn("this.$refs.tree is undefined in handleDeptClose");
      }

      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds
      this.updateDeptRefIds();
    },
    handleUserClose(tag) {
      let index = this.selectedUserLists.findIndex(e => {
        return e.primaryCode === tag.primaryCode;
      });
      this.selectedUserLists.splice(index, 1);
      let ids = [];
      this.selectedUserLists.forEach(element => {
        ids.push(element.primaryCode);
      });

      // 添加安全检查，确保 this.$refs.ltree 存在
      if (this.$refs.ltree) {
        this.$refs.ltree.setCheckedKeys(ids);
      } else {
        console.warn("this.$refs.ltree is undefined in handleUserClose");
      }

      // 重新设置 dataForm.allUserDeptIds，确保只包含当前选中的用户 ID
      this.dataForm.allUserDeptIds = [...ids];
    },
    getDeptTreeLists() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.DeptLists = data;
      });
    },
    getUserTreeLists() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.DeptLists = data;
      });
    },
    getUserTrees(node, datas) {
      this.dataForm.allUserDeptIds = datas.checkedKeys;
      this.selectedUserLists = datas.checkedNodes;
    },
    getRoleTrees(node, datas) {
      this.dataForm.refIds = datas.checkedKeys;
      this.selectedRoleLists = datas.checkedNodes;
    },
    getDeptTrees(node, datas) {
      // 先清空 selectedDeptLists
      this.selectedDeptLists = datas.checkedNodes;
      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds
      this.updateDeptRefIds();
    },
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data.roleName.indexOf(value) !== -1;
    },
    filterDeptNode(value, data) {
      if (!value) {
        return true;
      }
      return data.organizationName.indexOf(value) !== -1;
    },
    getRoleLists() {
      this.$api["systems/roleList"]().then(data => {
        this.roleLists = data;
      });
    },
    addRoles() {
      this.dialogRoleVisible = true;
      setTimeout(() => {
        var ids = [];
        this.selectedRoleLists.forEach(e => {
          ids.push(e.id);
        });
        this.$refs.tree.setCheckedKeys(ids);
      }, 100);
    },
    addUsers() {
      this.dialogUserVisible = true;
      setTimeout(() => {
        this.$refs.ltree.setTimer = new Date().getTime();
        var ids = [];
        this.selectedUserLists.forEach(e => {
          ids.push(e.primaryCode);
        });
        this.$refs.ltree.setCheckedKeys(ids);
        this.$refs.ltree.selectedNodes = this.selectedUserLists;
        // console.log(this.$refs.ltree.selectedNode, "设置");
      }, 100);
    },
    addDepts() {
      this.dialogDeptVisible = true;

      setTimeout(() => {
        var ids = [];
        this.selectedDeptLists.forEach(e => {
          ids.push(e.id);
        });
        this.$refs.tree.setCheckedKeys(ids);
      }, 100);
    },
    getRoleUser(e) {
      this.roleDrawer = true;
      setTimeout(() => {
        this.$refs.roleUser.searchForm.roleId = e.id;
        this.$refs.roleUser.searchTableData();
      }, 50);
    },
    getSelectKeys(e) {
      this.roleForm.resourceIds = [];
      e
        && e.forEach(element => {
          this.roleForm.resourceIds.push(element.id + "");
        });
    },
    saveMenu() {
      // roleresourcesave
      if (!this.roleForm.resourceIds.length) {
        this.$message.info("请选择对应资源在保存");
      } else {
        this.$api["systems/roleresourcesave"](this.roleForm).then(data => {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this.menuDrawer = false;
        });
      }
    },
    getBindResourceIds() {
      this.$api["systems/getResourceIds"]({id: this.roleForm.roleId}).then(
        data => {
          this.resourceIds = [];
          this.roleForm.resourceIds = [];
          data.forEach(e => {
            this.resourceIds.push(Number(e));
            this.roleForm.resourceIds.push(Number(e));
          });
          this.checkStrictly = true;
          this.$nextTick(() => {
            this.$refs.ctree.setTree(this.resourceIds);
            setTimeout(() => {
              this.checkStrictly = false;
            }, 500);
          });
        }
      );
    },
    setDatas(e) {
      this.$api["systems/permissionquery"]({roleId: e.id}).then(data => {
        this.selectModuleName = [];
        this.dataDrawer = true;
        if (data) {
          if (
            data.permissionTypeCode == "SPECIFIC_DEPT"
            || data.permissionTypeCode == "SPECIFIC_USER"
            || data.permissionTypeCode == "SPECIFIC_ROLE"
          ) {
            this.dataForm.permissionType = 8;
            this.dataForm.permissionTypeCode1 = data.permissionTypeCode;
            this.dataForm.permissionTypeCode = data.permissionTypeCode;
            this.dataForm.refIds = [];
            this.selectedDeptLists = [];
            this.selectedRoleLists = [];
            this.selectedUserLists = [];
            data.nameIdList.forEach(e => {
              if (data.permissionTypeCode === "SPECIFIC_USER") {
                this.selectedUserLists.push({
                  primaryCode: "user" + e.id,
                  label: e.name,
                });
              } else if (data.permissionTypeCode === "SPECIFIC_DEPT") {
                this.selectedDeptLists.push({
                  organizationName: e.name,
                  id: e.id,
                });
              } else if (data.permissionTypeCode === "SPECIFIC_ROLE") {
                this.selectedRoleLists.push({id: e.id, roleName: e.name});
              }
              this.dataForm.refIds.push(e.id);
            });
          } else {
            this.dataForm.permissionType = data.permissionTypeCode;
            this.dataForm.permissionTypeCode = data.permissionTypeCode;
            this.dataForm.refIds = [];
          }
          this.dataForm.moduleNames = data.moduleNames ? data.moduleNames : [];
        } else {
          this.selectedDeptLists = [];
          this.selectedRoleLists = [];
          this.selectedUserLists = [];
          this.dataForm = {
            moduleNames: [],
            permissionType: "",
            permissionTypeCode: "",
            permissionTypeCode1: "",
            refIds: [],
            roleId: null,
          };
        }
        this.dataForm.roleId = e.id;
      });
    },
    setApps(e) {
      this.appDrawer = true;
      this.appForm.roleId = e.id;
      this.getAppLists(e.id);
    },
    setMenu(e) {
      this.menuDrawer = true;
      this.roleForm.roleId = e.id;
      this.getTrees();
      this.getBindResourceIds();
    },
    handleAdd() {
      this.drawer = true;
      this.drawerName = "添加";
      this.ruleForm = _.cloneDeep(defaultForm);
      this.roleStatus = true;
    },
    handleRdit(data) {
      this.ruleForm = JSON.parse(JSON.stringify(data));
      this.roleStatus = !!data.roleStatus;
      this.drawer = true;
      this.drawerName = "编辑";
    },
    getParentName(arr, param) {
      let name = "";
      arr.forEach(e => {
        if (e.children) {
          if (e.id == param) {
            name = e.roleName;
          }
          this.getParentName(e.children, param);
        } else {
          if (e.id == param) {
            name = e.roleName;
          }
        }
      });
      return name;
    },
    handleDelete(e) {
      this.$confirm("您确定要删除该角色吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api["systems/roleDelete"]({id: e}).then(data => {
            this.$refs.grid.query();
            this.$message({
              message: "删除成功",
              type: "success",
            });
          });
        })
        .catch(() => {});
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true;
          this.$api["systems/addRole"](this.ruleForm).then(data => {
            this.drawer = false;
            this.loading = false;
            this.$message({
              type: "success",
              message: "保存成功",
            });
            this.$refs.grid.query();
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.ruleForm = _.cloneDeep(defaultForm);
      this.$refs.ruleForm.resetFields();
      this.drawer = false;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    gerTreeData() {
      if (this.resourceName) {
        this.$api["systems/getTreeList"]({
          resourceName: this.resourceName,
        }).then(data => {
          this.treedata = data;
        });
      } else {
        this.searchForm.parentId = null;
        this.getTrees();
      }
    },
    getTrees() {
      this.$api["systems/getPremissTree"]({parentId: 0}).then(data => {
        this.treedata = data;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.appCard {
  width: 100%;
  overflow: hidden;
  height: 60px;
  margin-bottom: 10px;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.cardTitle {
  position: absolute;
  z-index: 999;
  width: 150px;
  text-align: center;
  font-weight: bold;
  height: 60px;
  line-height: 60px;
  top: 0px;
  left: 0;
}
</style>

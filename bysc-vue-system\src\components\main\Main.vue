<template>
  <div>
    <div class="headerBarSortTheme" style="line-height: 67px; padding-right: 10px; height: 67px; min-width: 1280px; width: 100%; overflow-y: hidden">
      <div class="platformTitleGeneral">
          <!-- <img :src="systemConfig.logoUrl?systemConfig.logoUrl:logourl" key="min-logo" style="height: 40px;" />&nbsp; -->
          <img src="../../../src/assets/xtgl.png" key="min-logo" style="height: 40px;" />&nbsp;
          系统管理
      </div>
      <user ref="mainuser" style="float: right;width:380px" :message-unread-count="unreadCount" :user-avatar="userAvatar" :userName="userName" />

    </div>
    <el-container style="height: 100%; min-width: 1280px; width: 100%" class="main">
      <el-aside
        hide-trigger
        collapsible
        :width="collapsed?'60px':'226px'"
        :collapsed-width="64"
        class="left-sider"
      >
      <div style="box-shadow: inset 0px 0px 12px 15px #001529;width:15px;position: absolute;height: 100%;z-index: 1;right: 0;"></div>
        <side-menu
          :key="timer"
          :accordion="true"
          :theme="'dark'"
          ref="sideMenu"
          :active-name="$route.name"
          :collapsed="collapsed"
          @on-select="turnToPage"
          :menu-list="menuLists"
        ></side-menu>
      </el-aside>
      <el-container class="maincontainer">
        <el-header style="padding: 0px !important;">
          <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange"></header-bar>
        </el-header>
        <el-main style="padding: 0px !important;">
          <el-container class="main-layout-con">
            <el-main>
              <router-view />
            </el-main>
          </el-container>
        </el-main>
      </el-container>
    </el-container>
    <wsnotice ref="wsnotices" @wsMsg="getwsMsg"></wsnotice>
  </div>
</template>
<script>
import SideMenu from './components/side-menu/side-menu.vue';
import HeaderBar from './components/header-bar';
import User from './components/user';
import logourl from './logo.png';
import wsnotice from "../webscoket/wsnotice.vue";
import './main.less';
export default {
  name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    User,
    wsnotice
  },
  data() {
    return {
      logourl: logourl,
      openedNames: [],
      timre: '',
      menuLists: [],
      selectedColor: true,
      selectedIndex: 0,
      menu: [],
      minHeight: window.innerHeight + 'px',
      collapsed: false,
      userName: '',
      playFlag: false,
      chatCounts: 0,
      timer: '',
      intervalId: 0,
      msgWs: null,
      msgData: {},
      showMsg: false,
      unreadCount: 0,
      msgDetails: {},
      cname: ''
    };
  },
  computed: {
    systemConfig() {
      return this.$store.state.common.systemConfig;
    },
    userAvatar() {
      return this.$store.state.common.userInfo.avatar;
    },
    menuList() {
      return this.$store.getters.menuList;
    },
    fullScreen() {
      return this.$store.getters.fullScreen;
    },
    loadingShow() {
      return this.$store.state.common.loadingShow;
    }
  },
  watch: {
    loadingShow: {
      handler(val, oldval) {
        console.log(val);
      },
      // 深度观察监听
      deep: true
    },
    $route: {
      handler(val, oldval) {
        this.$localCache.setLocal('nowSelectRoute', val.path);
        this.$notify.closeAll();
      },
      // 深度观察监听
      deep: true
    }
  },
  provide() {
    return {
      turnToPage: this.turnToPage,
      getUnReadCount: this.getUnReadCount
    };
  },
  created() {
    let index = this.menuList.findIndex(e => {
      return e.resourcePageName === 'system/cfg';
    });
    if (index == -1) {
      alert('您无该模块权限');
      window.close();
    }
    this.menuLists = this.menuList[index].children;
    this.collapsed = this.$localCache.getLocal('collapsed') == 'true';
    this.$api['systems/system-about']({id: 'common'}).then(async data => {
      localStorage.setItem('loginPageUrl', data.loginPageUrl);
    });
  },
  methods: {
    getUnReadCount() {
      this.$refs.mainuser.getMsgCount();
    },
    getwsMsg(e) {
      // 获取消息通知
      console.log(e);
      if (e.type === 2) {
        this.$notify({
          title: e.data.title,
          duration: 6500,
          dangerouslyUseHTMLString: true,
          message: '<div>' + e.data.content + '</div>' + '<div><i class="el-icon-alarm-clock"></i>' + e.data.noticeTime + '</div>'
        });
        this.$refs.mainuser.getMsgCount();
        // this.$notify({
        //   title: e.data.title,
        //   message: h('i', {style: 'color: #333'}, e.data.content)
        // });
      }
      // else if (e.type === 1) {
      //   this.$refs.wsnotices.websocketsend(JSON.stringify({title: 'dkoakda', content: 'dlpakdpa', noticeTime: 'dlpadpakdp'}));
      // }
    },
    initWsnotice() {
      let wsprotocol = location.protocol === "http:" ? "ws" : "wss";
      let wsurl = wsprotocol + `://${location.host}/websocket/ws/notice`;
      let token = this.$cookies.get('userToken');
      this.$refs.wsnotices.initWebSocket(wsurl, token);
    },
    selectMenu(index, name, isSelect = true) {
      this.selectedIndex = index;
      this.$localCache.setLocal('selectedIndex', index);
      this.$localCache.setLocal('selectedName', name);
      // if (index) {
      this.menuList.forEach((e, index) => {
        if (e.name == name) {
          this.menuLists = [this.menuList[index]][0].children;
        }
      });
      let nowSelectRoute = this.$localCache.getLocal('nowSelectRoute');
      if (nowSelectRoute && !isSelect) {
        this.$router.push({path: nowSelectRoute});
      } else {
        this.$router.push({path: this.menuLists[0].meta.path});
      }
    },
    localStorageDate() {
      this.$localCache.setLocal('app', document.body.className);
    },
    turnToPage(route) {
      let {name, params, query} = {};
      if (typeof route === 'string') {
        name = route;
      } else {
        name = route.name;
        params = route.params;
        query = route.query;
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1]);
        return;
      }
      this.$router.push({
        name,
        params,
        query
      });
    },
    handleCollapsedChange(state) {
      this.collapsed = state;
      this.$localCache.setLocal('collapsed', state);
    }
  },
  mounted() {
    this.initWsnotice();
  }
};
</script>
<style scoped lang="less">

/deep/.ivu-notice-title{
  color: #17233d!important;
}

.main{
  margin-top:65px
}
.platformTitleGeneral {
  width: 600px;
  float: left;
  height: 65px;
  display: flex;
  justify-content: left;
  align-items: center;
  color: #fff;
  font-size: 24px;
  padding-left: 10px;
}
.headerBarSortTheme {
  background: #016CEC;
  position: fixed;
  z-index: 999;
  top: 0;
  overflow: hidden;
}
.headerBarSortTheme1 {
  background-image: linear-gradient(to right, #270a55, #c354df);
}
.maincontainer{
  overflow-y: auto;
  height: calc(100vh - 65px)
}
.left-sider {
  background-color: #001529;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 65px)
}
.menus {
  width: 110px;
  height: 67px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  line-height: 67px;
  float: left;
}
.cmenus {
  width: 110px;
  height: 67px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  line-height: 67px;
  float: left;
  border-bottom: 4px solid #fff;
  text-shadow: 1px 1px 6px #fff;
}
@-moz-keyframes shine {
  0% {
    background-position: 0;
  }
  60% {
    background-position: 280px;
  }
  100% {
    background-position: 280px;
  }
}
@-webkit-keyframes shine {
  0% {
    background-position: 0;
  }
  60% {
    background-position: 280px;
  }
  100% {
    background-position: 280px;
  }
}
@-o-keyframes shine {
  0% {
    background-position: 0;
  }
  60% {
    background-position: 280px;
  }
  100% {
    background-position: 280px;
  }
}
@keyframes shine {
  0% {
    background-position: 0;
  }
  60% {
    background-position: 280px;
  }
  100% {
    background-position: 280px;
  }
}
</style>

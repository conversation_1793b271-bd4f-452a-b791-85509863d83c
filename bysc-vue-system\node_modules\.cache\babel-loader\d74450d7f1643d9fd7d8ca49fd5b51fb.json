{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\interceptors\\axios.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\config\\interceptors\\axios.js", "mtime": 1745205562780}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import { CONSOLE_REQUEST_ENABLE, CONSOLE_RESPONSE_ENABLE } from \"../index\";\nimport { get } from 'lodash';\nimport router from '@/plugins/router';\nexport function requestSuccessFunc(requestObj) {\n  CONSOLE_REQUEST_ENABLE && console.info('requestInterceptorFunc', \"url: \".concat(requestObj.url), requestObj);\n  // 处理权限，请求发送监控\n\n  return requestObj;\n}\nexport function requestFailFunc(requestError) {\n  // 发送请求失败处理\n\n  return Promise.reject(requestError);\n}\nexport function responseSuccessFunc(responseObj) {\n  CONSOLE_RESPONSE_ENABLE && console.log('requestInterceptorFunc', 'data: ', responseObj.data);\n  return responseObj.data.data;\n}\nexport function responseFailFunc(responseError) {\n  if (responseError.response) {\n    switch (responseError.response.status) {\n      // case 403:\n      //   location.reload()\n      //   break\n      case 400:\n        // responseError.message = '未授权，请重新登录'\n        break;\n      case 401:\n        // responseError.message = get(responseError, 'response.data.message', '鉴权失败')\n        router.push('loginPage'); // Unauthorized 鉴权失败，跳转到登录页面\n        break;\n      case 403:\n        responseError.message = get(responseError, 'response.data.message', '您没有访问权限');\n        router.push('loginPage'); // Forbidden没有权限，跳转到登录页面\n        break;\n      case 404:\n        responseError.message = get(responseError, 'response.data.message', '接口错误');\n        break;\n      case 500:\n        responseError.message = get(responseError, 'response.data.message', errorMsg);\n        break;\n      default:\n        responseError.message = get(responseError, 'response.data.message', '出错啦');\n    }\n  }\n  responseError.config && !responseError.config.noShowDefaultError && global.vbus.$emit('global.$Message.show', responseError.message);\n  return Promise.reject(responseError);\n}", {"version": 3, "names": ["CONSOLE_REQUEST_ENABLE", "CONSOLE_RESPONSE_ENABLE", "get", "router", "requestSuccessFunc", "requestObj", "console", "info", "concat", "url", "requestFailFunc", "requestError", "Promise", "reject", "responseSuccessFunc", "responseObj", "log", "data", "responseFailFunc", "responseError", "response", "status", "push", "message", "errorMsg", "config", "noShowDefaultError", "global", "vbus", "$emit"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/config/interceptors/axios.js"], "sourcesContent": ["import {CONSOLE_REQUEST_ENABLE, CONSOLE_RESPONSE_ENABLE} from '../index';\r\nimport {get} from 'lodash';\r\nimport router from '@/plugins/router';\r\n\r\nexport function requestSuccessFunc(requestObj) {\r\n  CONSOLE_REQUEST_ENABLE && console.info('requestInterceptorFunc', `url: ${requestObj.url}`, requestObj);\r\n  // 处理权限，请求发送监控\r\n\r\n  return requestObj;\r\n}\r\n\r\nexport function requestFailFunc(requestError) {\r\n  // 发送请求失败处理\r\n\r\n  return Promise.reject(requestError);\r\n}\r\n\r\nexport function responseSuccessFunc(responseObj) {\r\n  CONSOLE_RESPONSE_ENABLE && console.log('requestInterceptorFunc', 'data: ', responseObj.data);\r\n  return responseObj.data.data;\r\n}\r\n\r\nexport function responseFailFunc(responseError) {\r\n  if (responseError.response) {\r\n    switch (responseError.response.status) {\r\n      // case 403:\r\n      //   location.reload()\r\n      //   break\r\n      case 400:\r\n        // responseError.message = '未授权，请重新登录'\r\n        break;\r\n      case 401:\r\n        // responseError.message = get(responseError, 'response.data.message', '鉴权失败')\r\n        router.push('loginPage'); // Unauthorized 鉴权失败，跳转到登录页面\r\n        break;\r\n      case 403:\r\n        responseError.message = get(responseError, 'response.data.message', '您没有访问权限');\r\n        router.push('loginPage'); // Forbidden没有权限，跳转到登录页面\r\n        break;\r\n      case 404:\r\n        responseError.message = get(responseError, 'response.data.message', '接口错误');\r\n        break;\r\n      case 500:\r\n        responseError.message = get(responseError, 'response.data.message', errorMsg);\r\n        break;\r\n      default:\r\n        responseError.message = get(responseError, 'response.data.message', '出错啦');\r\n    }\r\n  }\r\n  responseError.config && !responseError.config.noShowDefaultError && global.vbus.$emit('global.$Message.show', responseError.message);\r\n\r\n  return Promise.reject(responseError);\r\n}\r\n"], "mappings": "AAAA,SAAQA,sBAAsB,EAAEC,uBAAuB;AACvD,SAAQC,GAAG,QAAO,QAAQ;AAC1B,OAAOC,MAAM,MAAM,kBAAkB;AAErC,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC7CL,sBAAsB,IAAIM,OAAO,CAACC,IAAI,CAAC,wBAAwB,UAAAC,MAAA,CAAUH,UAAU,CAACI,GAAG,GAAIJ,UAAU,CAAC;EACtG;;EAEA,OAAOA,UAAU;AACnB;AAEA,OAAO,SAASK,eAAeA,CAACC,YAAY,EAAE;EAC5C;;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,YAAY,CAAC;AACrC;AAEA,OAAO,SAASG,mBAAmBA,CAACC,WAAW,EAAE;EAC/Cd,uBAAuB,IAAIK,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAE,QAAQ,EAAED,WAAW,CAACE,IAAI,CAAC;EAC5F,OAAOF,WAAW,CAACE,IAAI,CAACA,IAAI;AAC9B;AAEA,OAAO,SAASC,gBAAgBA,CAACC,aAAa,EAAE;EAC9C,IAAIA,aAAa,CAACC,QAAQ,EAAE;IAC1B,QAAQD,aAAa,CAACC,QAAQ,CAACC,MAAM;MACnC;MACA;MACA;MACA,KAAK,GAAG;QACN;QACA;MACF,KAAK,GAAG;QACN;QACAlB,MAAM,CAACmB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1B;MACF,KAAK,GAAG;QACNH,aAAa,CAACI,OAAO,GAAGrB,GAAG,CAACiB,aAAa,EAAE,uBAAuB,EAAE,SAAS,CAAC;QAC9EhB,MAAM,CAACmB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1B;MACF,KAAK,GAAG;QACNH,aAAa,CAACI,OAAO,GAAGrB,GAAG,CAACiB,aAAa,EAAE,uBAAuB,EAAE,MAAM,CAAC;QAC3E;MACF,KAAK,GAAG;QACNA,aAAa,CAACI,OAAO,GAAGrB,GAAG,CAACiB,aAAa,EAAE,uBAAuB,EAAEK,QAAQ,CAAC;QAC7E;MACF;QACEL,aAAa,CAACI,OAAO,GAAGrB,GAAG,CAACiB,aAAa,EAAE,uBAAuB,EAAE,KAAK,CAAC;IAC9E;EACF;EACAA,aAAa,CAACM,MAAM,IAAI,CAACN,aAAa,CAACM,MAAM,CAACC,kBAAkB,IAAIC,MAAM,CAACC,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAEV,aAAa,CAACI,OAAO,CAAC;EAEpI,OAAOX,OAAO,CAACC,MAAM,CAACM,aAAa,CAAC;AACtC", "ignoreList": []}]}
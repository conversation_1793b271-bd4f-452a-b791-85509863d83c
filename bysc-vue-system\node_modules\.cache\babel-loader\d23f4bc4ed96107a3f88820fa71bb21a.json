{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\sider-trigger\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\sider-trigger\\index.js", "mtime": 1745205562763}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import siderTrigger from \"./sider-trigger.vue\";\nexport default siderTrigger;", {"version": 3, "names": ["sider<PERSON>rigger"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/components/main/components/header-bar/sider-trigger/index.js"], "sourcesContent": ["import siderTrigger from './sider-trigger.vue';\r\nexport default siderTrigger;\r\n"], "mappings": "AAAA,OAAOA,YAAY;AACnB,eAAeA,YAAY", "ignoreList": []}]}
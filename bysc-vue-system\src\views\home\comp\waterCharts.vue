<!--
 * @Author: czw
 * @Date: 2022-04-22 14:04:29
 * @LastEditTime: 2022-06-02 09:44:38
 * @LastEditors: czw
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \kdsp_vue\src\views\home\comp\waterCharts.vue
-->
<template>
  <div>
    <div ref="water" :style="{width: width,height:height}"></div>
  </div>
</template>
<script>
import 'echarts-liquidfill';
import {on, off} from '@/utils/tools';
export default {
  name: 'waterCharts',
  props: {
    shadowColor: String,
    color: Array,
    title: String,
    id: String,
    width: {
      type: String,
      default: '100%'
    },
    borderColor: String,
    height: {
      type: String,
      default: '100%'
    },
    values: Number
  },
  data() {
    return {
      timer: '',
      myChart: null
    };
  },
  beforeDestroy() {
    off(window, 'resize', this.resize);
  },
  methods: {
    resize() {
      this.myChart.resize();
    },
    drawChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.water);
      let value = this.values / 100;
      // 指定图表的配置项和数据
      let option =  {
        title: {
          text: this.title,
          left: 'center',
          top: '65%',
          textStyle: {
            color: '#fff',
            fontWeight: 'normal',
            fontSize: 14,
          },
        },
        series: [
          {
            type: 'liquidFill',
            radius: '75%',
            center: ['51%', '50%'],
            data: [value + 0.005, value], // data个数代表波浪数
            color: this.color,
            outline: {
              // 轮廓设置
              show: true,
              borderDistance: 10, // 边框与球中间间距
              itemStyle: {
                borderWidth: 0,
                borderColor: this.borderColor,
              },
            },
            itemStyle: {
              normal: {
                shadowColor: this.borderColor,
                shadowBlur: 10,
              },
            },
            backgroundStyle: {
              borderWidth: 5,
              borderColor: this.borderColor,
              shadowColor: this.shadowColor,
              color: this.shadowColor,
            },
            label: {
              position: ['50%', '65%'],
              formatter: (value * 100).toFixed(1) + '%',
              textStyle: {
                color: '#fff',
                fontSize: 14,
              },
            },
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
      on(window, 'resize', this.resize);
    }
  },
  mounted() {
    // 加延迟防止图表溢出盒子
    this.drawChart();
  }
};
</script>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\router.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\plugins\\router.js", "mtime": 1745205562799}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport ROUTES from '@/routes';\nimport { ROUTER_DEFAULT_CONFIG } from '@/config';\nimport { routerBeforeEachFunc, routerAfterEachFunc } from '@/config/interceptors';\nvar originalPush = VueRouter.prototype.push;\nVueRouter.prototype.push = function push(location) {\n  return originalPush.call(this, location).catch(function (err) {\n    return err;\n  });\n};\nVue.use(VueRouter);\n// const originalPush = ROUTES.prototype.push;\n// ROUTES.prototype.push = function push(location) {\n//   return originalPush.call(this, location).catch(err => err);\n// };\nexport function createNewRoute() {\n  return new VueRouter(_objectSpread(_objectSpread({}, ROUTER_DEFAULT_CONFIG), {}, {\n    routes: ROUTES\n  }));\n}\n\n// 注入默认配置和路由表\n// let routerInstance = new VueRouter({\n//   ...ROUTER_DEFAULT_CONFIG,\n//   routes: ROUTES\n// })\nexport var routerInstance = createNewRoute();\n// 注入拦截器\nrouterInstance.beforeEach(routerBeforeEachFunc);\nrouterInstance.afterEach(routerAfterEachFunc);\n/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */\n// routerInstance.onError(routerErrorEachFunc);\nexport default routerInstance;", {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ROUTES", "ROUTER_DEFAULT_CONFIG", "routerBeforeEachFunc", "routerAfterEachFunc", "originalPush", "prototype", "push", "location", "call", "catch", "err", "use", "createNewRoute", "_objectSpread", "routes", "routerInstance", "beforeEach", "after<PERSON>ach"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/plugins/router.js"], "sourcesContent": ["import Vue from 'vue';\r\nimport VueRouter from 'vue-router';\r\nimport ROUTES from '@/routes';\r\nimport {ROUTER_DEFAULT_CONFIG} from '@/config';\r\nimport {routerBeforeEachFunc, routerAfterEachFunc} from '@/config/interceptors';\r\nconst originalPush = VueRouter.prototype.push;\r\n\r\nVueRouter.prototype.push = function push(location) {\r\n  return originalPush.call(this, location).catch(err => err);\r\n};\r\nVue.use(VueRouter);\r\n// const originalPush = ROUTES.prototype.push;\r\n// ROUTES.prototype.push = function push(location) {\r\n//   return originalPush.call(this, location).catch(err => err);\r\n// };\r\nexport function createNewRoute() {\r\n  return new VueRouter({\r\n    ...ROUTER_DEFAULT_CONFIG,\r\n    routes: ROUTES\r\n  });\r\n}\r\n\r\n// 注入默认配置和路由表\r\n// let routerInstance = new VueRouter({\r\n//   ...ROUTER_DEFAULT_CONFIG,\r\n//   routes: ROUTES\r\n// })\r\nexport let routerInstance = createNewRoute();\r\n// 注入拦截器\r\nrouterInstance.beforeEach(routerBeforeEachFunc);\r\n\r\nrouterInstance.afterEach(routerAfterEachFunc);\r\n/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */\r\n// routerInstance.onError(routerErrorEachFunc);\r\nexport default routerInstance;\r\n"], "mappings": ";;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAAQC,qBAAqB,QAAO,UAAU;AAC9C,SAAQC,oBAAoB,EAAEC,mBAAmB,QAAO,uBAAuB;AAC/E,IAAMC,YAAY,GAAGL,SAAS,CAACM,SAAS,CAACC,IAAI;AAE7CP,SAAS,CAACM,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACC,QAAQ,EAAE;EACjD,OAAOH,YAAY,CAACI,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC5D,CAAC;AACDZ,GAAG,CAACa,GAAG,CAACZ,SAAS,CAAC;AAClB;AACA;AACA;AACA;AACA,OAAO,SAASa,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAIb,SAAS,CAAAc,aAAA,CAAAA,aAAA,KACfZ,qBAAqB;IACxBa,MAAM,EAAEd;EAAM,EACf,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIe,cAAc,GAAGH,cAAc,CAAC,CAAC;AAC5C;AACAG,cAAc,CAACC,UAAU,CAACd,oBAAoB,CAAC;AAE/Ca,cAAc,CAACE,SAAS,CAACd,mBAAmB,CAAC;AAC7C;AACA;AACA,eAAeY,cAAc", "ignoreList": []}]}
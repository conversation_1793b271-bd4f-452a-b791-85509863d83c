/*css主要部分的样式*/
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
.el-table__fixed::before{
  background-color: transparent;
}
.el-table__fixed-right::before{
  /* opacity: 0; */
  background-color: transparent;
}
::-webkit-scrollbar {
  width: 6px; /*对垂直流动条有效*/
  height: 16px; /*对水平流动条有效*/
}

/*定义滚动条的轨道颜色、内阴影及圆角*/
::-webkit-scrollbar-track{
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); */
  background-color: #fff;
}

/*定义滑块颜色、内阴影及圆角*/
::-webkit-scrollbar-thumb{
  border-radius: 8px;
  background-color: #DDDEE0;
  /* -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); */
}

/*定义滑块悬停变化颜色、内阴影及圆角*/
::-webkit-scrollbar-thumb:hover{
  background-color: #f2f2f2;
}

/*定义两端按钮的样式*/
::-webkit-scrollbar-button {
  /* background-color: cyan; */
}

/*定义右下角汇合处的样式*/
::-webkit-scrollbar-corner {
  /* background: khaki; */
}

/* 隐藏滚动条 */
/* ::-webkit-scrollbar {
  display: none;
} */

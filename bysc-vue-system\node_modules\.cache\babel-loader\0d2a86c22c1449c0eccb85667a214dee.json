{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\routes\\system.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\routes\\system.js", "mtime": 1753782546819}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import Main from '@/components/main/Main';\n// 支付模块的路由\n\nvar systemRoutes = [{\n  name: 'system/cfg',\n  path: '/system/cfg',\n  component: Main,\n  redirect: '/permission',\n  meta: {\n    showInMenu: true,\n    showAlways: true,\n    // 表示一定要展示子菜单\n    title: '系统管理'\n  },\n  children: [{\n    name: 'systemHome',\n    path: '/system/home',\n    component: function component() {\n      return import('@/bysc_system/views/home');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/home',\n      title: '首页',\n      icon: 'logo-apple'\n    }\n  }, {\n    name: 'permission',\n    path: '/permission',\n    redirect: '/system/user/table',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/permission',\n      title: '权限管理',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'userTable',\n      path: '/system/user/table',\n      component: function component() {\n        return import('@/bysc_system/views/user');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/user/table',\n        title: '用户管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'roleTable',\n      path: '/system/role/table',\n      component: function component() {\n        return import('@/bysc_system/views/role');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/role/table',\n        title: '角色管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'resourceTable',\n      path: '/system/resource/table',\n      component: function component() {\n        return import('@/bysc_system/views/menus');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/resource/table',\n        title: '资源管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'organizationTable',\n      path: '/system/organization/table',\n      component: function component() {\n        return import('@/bysc_system/views/organization');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/organization/table',\n        title: '组织管理',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'systemBase',\n    path: '/system/base',\n    redirect: '/system/dict/table',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/base',\n      title: '基础配置',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'dictTable',\n      path: '/system/dict/table',\n      component: function component() {\n        return import('@/bysc_system/views/sysDict');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/dict/table',\n        title: '系统字典',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'tenantTable',\n      path: '/base/tenant/table',\n      component: function component() {\n        return import('@/bysc_system/views/tenant');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/base/tenant/table',\n        title: '租户管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'config',\n      path: '/system/base/config',\n      component: function component() {\n        return import('@/bysc_system/views/config');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/base/config',\n        title: '系统设置',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'systemCode',\n    path: '/system/code',\n    redirect: '/system/code/generation',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/code',\n      title: '代码生成',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'codeGeneration',\n      path: '/system/code/generation',\n      component: function component() {\n        return import('@/bysc_system/views/codeGeneration');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/code/generation',\n        title: '代码生成',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'thirdLogin',\n    path: '/system/thirdLogin',\n    redirect: '/system/thirdLogin/thirdList',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/thirdLogin',\n      title: '第三方登录',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'thirdList',\n      path: '/system/thirdLogin/thirdList',\n      component: function component() {\n        return import('@/bysc_system/views/thirdLogin');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/thirdLogin/thirdList',\n        title: '第三方列表',\n        icon: 'logo-apple'\n      }\n    }]\n  }]\n}];\nexport default systemRoutes;", {"version": 3, "names": ["Main", "systemRoutes", "name", "path", "component", "redirect", "meta", "showInMenu", "showAlways", "title", "children", "icon"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/routes/system.js"], "sourcesContent": ["import Main from '@/components/main/Main';\r\n// 支付模块的路由\r\n\r\nconst systemRoutes = [\r\n  {\r\n    name: 'system/cfg',\r\n    path: '/system/cfg',\r\n    component: Main,\r\n    redirect: '/permission',\r\n    meta: {\r\n      showInMenu: true,\r\n      showAlways: true, // 表示一定要展示子菜单\r\n      title: '系统管理',\r\n    },\r\n    children: [\r\n      {\r\n        name: 'systemHome',\r\n        path: '/system/home',\r\n        component: () => import('@/bysc_system/views/home'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/home',\r\n          title: '首页',\r\n          icon: 'logo-apple'\r\n        }\r\n      },\r\n      {\r\n        name: 'permission',\r\n        path: '/permission',\r\n        redirect: '/system/user/table',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/permission',\r\n          title: '权限管理',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'userTable',\r\n            path: '/system/user/table',\r\n            component: () => import('@/bysc_system/views/user'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/user/table',\r\n              title: '用户管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'roleTable',\r\n            path: '/system/role/table',\r\n            component: () => import('@/bysc_system/views/role'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/role/table',\r\n              title: '角色管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'resourceTable',\r\n            path: '/system/resource/table',\r\n            component: () => import('@/bysc_system/views/menus'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/resource/table',\r\n              title: '资源管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'organizationTable',\r\n            path: '/system/organization/table',\r\n            component: () => import('@/bysc_system/views/organization'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/organization/table',\r\n              title: '组织管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'systemBase',\r\n        path: '/system/base',\r\n        redirect: '/system/dict/table',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/base',\r\n          title: '基础配置',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'dictTable',\r\n            path: '/system/dict/table',\r\n            component: () => import('@/bysc_system/views/sysDict'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/dict/table',\r\n              title: '系统字典',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n\r\n          {\r\n            name: 'tenantTable',\r\n            path: '/base/tenant/table',\r\n            component: () => import('@/bysc_system/views/tenant'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/base/tenant/table',\r\n              title: '租户管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'config',\r\n            path: '/system/base/config',\r\n            component: () => import('@/bysc_system/views/config'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/base/config',\r\n              title: '系统设置',\r\n              icon: 'logo-apple'\r\n            }\r\n          }\r\n   \r\n        ]\r\n      },\r\n      {\r\n        name: 'systemCode',\r\n        path: '/system/code',\r\n        redirect: '/system/code/generation',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/code',\r\n          title: '代码生成',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'codeGeneration',\r\n            path: '/system/code/generation',\r\n            component: () => import('@/bysc_system/views/codeGeneration'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/code/generation',\r\n              title: '代码生成',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'thirdLogin',\r\n        path: '/system/thirdLogin',\r\n        redirect: '/system/thirdLogin/thirdList',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/thirdLogin',\r\n          title: '第三方登录',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'thirdList',\r\n            path: '/system/thirdLogin/thirdList',\r\n            component: () => import('@/bysc_system/views/thirdLogin'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/thirdLogin/thirdList',\r\n              title: '第三方列表',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n  \r\n   \r\n\r\n    \r\n    \r\n    ]\r\n  }\r\n];\r\n\r\nexport default systemRoutes;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,wBAAwB;AACzC;;AAEA,IAAMC,YAAY,GAAG,CACnB;EACEC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ,IAAI;EACfK,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE;IACJC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAAE;IAClBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAQ,MAAM,CAAC,0BAA0B,CAAC;IAAA;IACnDE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,IAAI;MACXE,IAAI,EAAE;IACR;EACF,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,aAAa;IACnBE,QAAQ,EAAE,oBAAoB;IAC9BD,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAQ,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,aAAa;MACnBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,0BAA0B,CAAC;MAAA;MACnDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,0BAA0B,CAAC;MAAA;MACnDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,2BAA2B,CAAC;MAAA;MACpDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,wBAAwB;QAC9BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,kCAAkC,CAAC;MAAA;MAC3DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,4BAA4B;QAClCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBE,QAAQ,EAAE,oBAAoB;IAC9BD,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAQ,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,6BAA6B,CAAC;MAAA;MACtDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EAED;MACET,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,4BAA4B,CAAC;MAAA;MACrDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,qBAAqB;MAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,4BAA4B,CAAC;MAAA;MACrDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,qBAAqB;QAC3BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAGL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBE,QAAQ,EAAE,yBAAyB;IACnCD,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAQ,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,oCAAoC,CAAC;MAAA;MAC7DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,yBAAyB;QAC/BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,oBAAoB;IAC1BE,QAAQ,EAAE,8BAA8B;IACxCD,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAQ,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,oBAAoB;MAC1BM,KAAK,EAAE,OAAO;MACdE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,8BAA8B;MACpCC,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,MAAM,CAAC,gCAAgC,CAAC;MAAA;MACzDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,OAAO;QACdE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC;AAOL,CAAC,CACF;AAED,eAAeV,YAAY", "ignoreList": []}]}
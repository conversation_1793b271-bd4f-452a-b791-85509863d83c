{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\loading-fix.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\loading-fix.js", "mtime": 1753782634847}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\n/**\n * Loading 状态修复工具\n * 用于解决页面出现蒙层无法消除的问题\n */\n\nexport default {\n  /**\n   * 清除所有可能的遮罩层\n   */\n  clearAllMasks: function clearAllMasks() {\n    try {\n      // 清除 Element UI 的 loading 遮罩\n      var loadingMasks = document.querySelectorAll('.el-loading-mask');\n      loadingMasks.forEach(function (mask) {\n        if (mask.parentNode) {\n          mask.parentNode.removeChild(mask);\n        }\n      });\n\n      // 清除其他可能的遮罩层\n      var overlayMasks = document.querySelectorAll('.el-overlay, [class*=\"mask\"], [class*=\"loading\"]');\n      overlayMasks.forEach(function (mask) {\n        var style = window.getComputedStyle(mask);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.zIndex > 100 && mask.offsetWidth > window.innerWidth * 0.5) {\n            if (mask.parentNode) {\n              mask.parentNode.removeChild(mask);\n            }\n          }\n        }\n      });\n      console.log('已清除所有遮罩层');\n    } catch (error) {\n      console.error('清除遮罩层时出错:', error);\n    }\n  },\n  /**\n   * 重置所有 loading 状态\n   */\n  resetAllLoadingStates: function resetAllLoadingStates() {\n    try {\n      // 重置 body 上的 loading 类\n      document.body.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');\n\n      // 重置所有带有 loading 属性的元素\n      var loadingElements = document.querySelectorAll('[aria-label*=\"loading\"], [class*=\"loading\"]');\n      loadingElements.forEach(function (el) {\n        el.removeAttribute('aria-label');\n        el.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');\n      });\n      console.log('已重置所有 loading 状态');\n    } catch (error) {\n      console.error('重置 loading 状态时出错:', error);\n    }\n  },\n  /**\n   * 强制清理页面遮罩层\n   */\n  forceClearMasks: function forceClearMasks() {\n    var _this = this;\n    this.clearAllMasks();\n    this.resetAllLoadingStates();\n\n    // 延迟再次清理，确保动态生成的遮罩也被清除\n    setTimeout(function () {\n      _this.clearAllMasks();\n    }, 100);\n  },\n  /**\n   * 监听并自动清理异常的遮罩层\n   */\n  startAutoCleanup: function startAutoCleanup() {\n    var observer = new MutationObserver(function (mutations) {\n      mutations.forEach(function (mutation) {\n        if (mutation.type === 'childList') {\n          mutation.addedNodes.forEach(function (node) {\n            if (node.nodeType === 1 && node.classList) {\n              // 检查是否是 loading 遮罩\n              if (node.classList.contains('el-loading-mask')) {\n                // 5秒后自动清除长时间存在的遮罩\n                setTimeout(function () {\n                  if (node.parentNode) {\n                    console.log('自动清除长时间存在的遮罩层');\n                    node.parentNode.removeChild(node);\n                  }\n                }, 5000);\n              }\n            }\n          });\n        }\n      });\n    });\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n    return observer;\n  },\n  /**\n   * 检查页面是否存在异常遮罩\n   */\n  checkForAbnormalMasks: function checkForAbnormalMasks() {\n    var masks = document.querySelectorAll('.el-loading-mask');\n    var abnormalMasks = [];\n    masks.forEach(function (mask) {\n      var style = window.getComputedStyle(mask);\n      if (style.display !== 'none' && mask.offsetParent !== null) {\n        abnormalMasks.push({\n          element: mask,\n          zIndex: style.zIndex,\n          position: style.position,\n          parent: mask.parentNode\n        });\n      }\n    });\n    if (abnormalMasks.length > 0) {\n      console.warn('发现异常遮罩层:', abnormalMasks);\n      return abnormalMasks;\n    }\n    return null;\n  },\n  /**\n   * 安装到 Vue 原型上\n   */\n  install: function install(Vue) {\n    Vue.prototype.$loadingFix = this;\n\n    // 全局混入，在每个组件挂载时检查遮罩层\n    Vue.mixin({\n      mounted: function mounted() {\n        var _this2 = this;\n        this.$nextTick(function () {\n          var abnormalMasks = _this2.$loadingFix.checkForAbnormalMasks();\n          if (abnormalMasks) {\n            _this2.$loadingFix.forceClearMasks();\n          }\n        });\n      }\n    });\n  }\n};", {"version": 3, "names": ["clearAllMasks", "loadingMasks", "document", "querySelectorAll", "for<PERSON>ach", "mask", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "overlayMasks", "style", "window", "getComputedStyle", "position", "zIndex", "offsetWidth", "innerWidth", "console", "log", "error", "resetAllLoadingStates", "body", "classList", "remove", "loadingElements", "el", "removeAttribute", "forceClearMasks", "_this", "setTimeout", "startAutoCleanup", "observer", "MutationObserver", "mutations", "mutation", "type", "addedNodes", "node", "nodeType", "contains", "observe", "childList", "subtree", "checkForAbnormalMasks", "masks", "abnormalMasks", "display", "offsetParent", "push", "element", "parent", "length", "warn", "install", "<PERSON><PERSON>", "prototype", "$loadingFix", "mixin", "mounted", "_this2", "$nextTick"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/utils/loading-fix.js"], "sourcesContent": ["/**\n * Loading 状态修复工具\n * 用于解决页面出现蒙层无法消除的问题\n */\n\nexport default {\n  /**\n   * 清除所有可能的遮罩层\n   */\n  clearAllMasks() {\n    try {\n      // 清除 Element UI 的 loading 遮罩\n      const loadingMasks = document.querySelectorAll('.el-loading-mask');\n      loadingMasks.forEach(mask => {\n        if (mask.parentNode) {\n          mask.parentNode.removeChild(mask);\n        }\n      });\n\n      // 清除其他可能的遮罩层\n      const overlayMasks = document.querySelectorAll('.el-overlay, [class*=\"mask\"], [class*=\"loading\"]');\n      overlayMasks.forEach(mask => {\n        const style = window.getComputedStyle(mask);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.zIndex > 100 && mask.offsetWidth > window.innerWidth * 0.5) {\n            if (mask.parentNode) {\n              mask.parentNode.removeChild(mask);\n            }\n          }\n        }\n      });\n\n      console.log('已清除所有遮罩层');\n    } catch (error) {\n      console.error('清除遮罩层时出错:', error);\n    }\n  },\n\n  /**\n   * 重置所有 loading 状态\n   */\n  resetAllLoadingStates() {\n    try {\n      // 重置 body 上的 loading 类\n      document.body.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');\n      \n      // 重置所有带有 loading 属性的元素\n      const loadingElements = document.querySelectorAll('[aria-label*=\"loading\"], [class*=\"loading\"]');\n      loadingElements.forEach(el => {\n        el.removeAttribute('aria-label');\n        el.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');\n      });\n\n      console.log('已重置所有 loading 状态');\n    } catch (error) {\n      console.error('重置 loading 状态时出错:', error);\n    }\n  },\n\n  /**\n   * 强制清理页面遮罩层\n   */\n  forceClearMasks() {\n    this.clearAllMasks();\n    this.resetAllLoadingStates();\n    \n    // 延迟再次清理，确保动态生成的遮罩也被清除\n    setTimeout(() => {\n      this.clearAllMasks();\n    }, 100);\n  },\n\n  /**\n   * 监听并自动清理异常的遮罩层\n   */\n  startAutoCleanup() {\n    const observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.type === 'childList') {\n          mutation.addedNodes.forEach((node) => {\n            if (node.nodeType === 1 && node.classList) {\n              // 检查是否是 loading 遮罩\n              if (node.classList.contains('el-loading-mask')) {\n                // 5秒后自动清除长时间存在的遮罩\n                setTimeout(() => {\n                  if (node.parentNode) {\n                    console.log('自动清除长时间存在的遮罩层');\n                    node.parentNode.removeChild(node);\n                  }\n                }, 5000);\n              }\n            }\n          });\n        }\n      });\n    });\n\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n\n    return observer;\n  },\n\n  /**\n   * 检查页面是否存在异常遮罩\n   */\n  checkForAbnormalMasks() {\n    const masks = document.querySelectorAll('.el-loading-mask');\n    const abnormalMasks = [];\n\n    masks.forEach(mask => {\n      const style = window.getComputedStyle(mask);\n      if (style.display !== 'none' && mask.offsetParent !== null) {\n        abnormalMasks.push({\n          element: mask,\n          zIndex: style.zIndex,\n          position: style.position,\n          parent: mask.parentNode\n        });\n      }\n    });\n\n    if (abnormalMasks.length > 0) {\n      console.warn('发现异常遮罩层:', abnormalMasks);\n      return abnormalMasks;\n    }\n\n    return null;\n  },\n\n  /**\n   * 安装到 Vue 原型上\n   */\n  install(Vue) {\n    Vue.prototype.$loadingFix = this;\n    \n    // 全局混入，在每个组件挂载时检查遮罩层\n    Vue.mixin({\n      mounted() {\n        this.$nextTick(() => {\n          const abnormalMasks = this.$loadingFix.checkForAbnormalMasks();\n          if (abnormalMasks) {\n            this.$loadingFix.forceClearMasks();\n          }\n        });\n      }\n    });\n  }\n};\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,eAAe;EACb;AACF;AACA;EACEA,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI;MACF;MACA,IAAMC,YAAY,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,CAAC;MAClEF,YAAY,CAACG,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,UAAU,EAAE;UACnBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;;MAEF;MACA,IAAMG,YAAY,GAAGN,QAAQ,CAACC,gBAAgB,CAAC,kDAAkD,CAAC;MAClGK,YAAY,CAACJ,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAMI,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACN,IAAI,CAAC;QAC3C,IAAII,KAAK,CAACG,QAAQ,KAAK,OAAO,IAAIH,KAAK,CAACG,QAAQ,KAAK,UAAU,EAAE;UAC/D,IAAIH,KAAK,CAACI,MAAM,GAAG,GAAG,IAAIR,IAAI,CAACS,WAAW,GAAGJ,MAAM,CAACK,UAAU,GAAG,GAAG,EAAE;YACpE,IAAIV,IAAI,CAACC,UAAU,EAAE;cACnBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;YACnC;UACF;QACF;MACF,CAAC,CAAC;MAEFW,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED;AACF;AACA;EACEC,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB,IAAI;MACF;MACAjB,QAAQ,CAACkB,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,6BAA6B,EAAE,2BAA2B,CAAC;;MAE1F;MACA,IAAMC,eAAe,GAAGrB,QAAQ,CAACC,gBAAgB,CAAC,6CAA6C,CAAC;MAChGoB,eAAe,CAACnB,OAAO,CAAC,UAAAoB,EAAE,EAAI;QAC5BA,EAAE,CAACC,eAAe,CAAC,YAAY,CAAC;QAChCD,EAAE,CAACH,SAAS,CAACC,MAAM,CAAC,6BAA6B,EAAE,2BAA2B,CAAC;MACjF,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED;AACF;AACA;EACEQ,eAAe,WAAfA,eAAeA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAChB,IAAI,CAAC3B,aAAa,CAAC,CAAC;IACpB,IAAI,CAACmB,qBAAqB,CAAC,CAAC;;IAE5B;IACAS,UAAU,CAAC,YAAM;MACfD,KAAI,CAAC3B,aAAa,CAAC,CAAC;IACtB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED;AACF;AACA;EACE6B,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;IACjB,IAAMC,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,UAACC,SAAS,EAAK;MACnDA,SAAS,CAAC5B,OAAO,CAAC,UAAC6B,QAAQ,EAAK;QAC9B,IAAIA,QAAQ,CAACC,IAAI,KAAK,WAAW,EAAE;UACjCD,QAAQ,CAACE,UAAU,CAAC/B,OAAO,CAAC,UAACgC,IAAI,EAAK;YACpC,IAAIA,IAAI,CAACC,QAAQ,KAAK,CAAC,IAAID,IAAI,CAACf,SAAS,EAAE;cACzC;cACA,IAAIe,IAAI,CAACf,SAAS,CAACiB,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBAC9C;gBACAV,UAAU,CAAC,YAAM;kBACf,IAAIQ,IAAI,CAAC9B,UAAU,EAAE;oBACnBU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;oBAC5BmB,IAAI,CAAC9B,UAAU,CAACC,WAAW,CAAC6B,IAAI,CAAC;kBACnC;gBACF,CAAC,EAAE,IAAI,CAAC;cACV;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFN,QAAQ,CAACS,OAAO,CAACrC,QAAQ,CAACkB,IAAI,EAAE;MAC9BoB,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,OAAOX,QAAQ;EACjB,CAAC;EAED;AACF;AACA;EACEY,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB,IAAMC,KAAK,GAAGzC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,CAAC;IAC3D,IAAMyC,aAAa,GAAG,EAAE;IAExBD,KAAK,CAACvC,OAAO,CAAC,UAAAC,IAAI,EAAI;MACpB,IAAMI,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACN,IAAI,CAAC;MAC3C,IAAII,KAAK,CAACoC,OAAO,KAAK,MAAM,IAAIxC,IAAI,CAACyC,YAAY,KAAK,IAAI,EAAE;QAC1DF,aAAa,CAACG,IAAI,CAAC;UACjBC,OAAO,EAAE3C,IAAI;UACbQ,MAAM,EAAEJ,KAAK,CAACI,MAAM;UACpBD,QAAQ,EAAEH,KAAK,CAACG,QAAQ;UACxBqC,MAAM,EAAE5C,IAAI,CAACC;QACf,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAIsC,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;MAC5BlC,OAAO,CAACmC,IAAI,CAAC,UAAU,EAAEP,aAAa,CAAC;MACvC,OAAOA,aAAa;IACtB;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;EACEQ,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;IACXA,GAAG,CAACC,SAAS,CAACC,WAAW,GAAG,IAAI;;IAEhC;IACAF,GAAG,CAACG,KAAK,CAAC;MACRC,OAAO,WAAPA,OAAOA,CAAA,EAAG;QAAA,IAAAC,MAAA;QACR,IAAI,CAACC,SAAS,CAAC,YAAM;UACnB,IAAMf,aAAa,GAAGc,MAAI,CAACH,WAAW,CAACb,qBAAqB,CAAC,CAAC;UAC9D,IAAIE,aAAa,EAAE;YACjBc,MAAI,CAACH,WAAW,CAAC7B,eAAe,CAAC,CAAC;UACpC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\routes\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\routes\\index.js", "mtime": 1745214368717}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["var AdminLogin = function AdminLogin() {\n  return import('@/views/login/index');\n};\n// const datav = () => import('@/components/datav/datav.vue')\n\n// const Register = () => import('@/views/Register.vue')\n\nexport default [{\n  path: '/adminLogin',\n  // 为了转发nginx，避免重名，故而加一个index地址TODO 改进\n  name: 'adminLogin',\n  component: AdminLogin,\n  meta: {\n    showInMenu: false\n  }\n}, {\n  name: 'errorPage',\n  path: '/errorPage',\n  // TODO 这里变更首页\n  component: function component() {\n    return import('@/views/errorPage');\n  },\n  meta: {\n    showInMenu: false\n  }\n}, {\n  name: 'nofound',\n  path: '/nofound',\n  // TODO 这里变更首页\n  component: function component() {\n    return import('@/views/nofound');\n  },\n  meta: {\n    showInMenu: false\n  }\n}, {\n  name: 'social',\n  path: '/social',\n  // TODO 这里变更首页\n  component: function component() {\n    return import('@/views/social');\n  },\n  meta: {\n    showInMenu: false\n  }\n}\n// {\n//   path: '/register',\n//   name: 'register',\n//   component: Register\n// },\n// {\n//   path: '/about',\n//   name: 'about',\n//   // route level code-splitting\n//   // this generates a separate chunk (about.[hash].js) for this route\n//   // which is lazy-loaded when the route is visited.\n//   component: () => import(/* webpackChunkName: \"about\" */ '@/views/About.vue')\n// }\n];", {"version": 3, "names": ["AdminLogin", "path", "name", "component", "meta", "showInMenu"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/routes/index.js"], "sourcesContent": ["const AdminLogin = () => import('@/views/login/index');\r\n// const datav = () => import('@/components/datav/datav.vue')\r\n\r\n// const Register = () => import('@/views/Register.vue')\r\n\r\nexport default [\r\n  {\r\n    path: '/adminLogin', // 为了转发nginx，避免重名，故而加一个index地址TODO 改进\r\n    name: 'adminLogin',\r\n    component: AdminLogin,\r\n    meta: {\r\n      showInMenu: false\r\n    }\r\n  },\r\n  {\r\n    name: 'errorPage',\r\n    path: '/errorPage',\r\n    // TODO 这里变更首页\r\n    component: () => import('@/views/errorPage'),\r\n    meta: {\r\n      showInMenu: false\r\n    }\r\n  },\r\n  {\r\n    name: 'nofound',\r\n    path: '/nofound',\r\n    // TODO 这里变更首页\r\n    component: () => import('@/views/nofound'),\r\n    meta: {\r\n      showInMenu: false\r\n    }\r\n  },\r\n  {\r\n    name: 'social',\r\n    path: '/social',\r\n    // TODO 这里变更首页\r\n    component: () => import('@/views/social'),\r\n    meta: {\r\n      showInMenu: false\r\n    }\r\n  }\r\n  // {\r\n  //   path: '/register',\r\n  //   name: 'register',\r\n  //   component: Register\r\n  // },\r\n  // {\r\n  //   path: '/about',\r\n  //   name: 'about',\r\n  //   // route level code-splitting\r\n  //   // this generates a separate chunk (about.[hash].js) for this route\r\n  //   // which is lazy-loaded when the route is visited.\r\n  //   component: () => import(/* webpackChunkName: \"about\" */ '@/views/About.vue')\r\n  // }\r\n];\r\n"], "mappings": "AAAA,IAAMA,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,qBAAqB,CAAC;AAAA;AACtD;;AAEA;;AAEA,eAAe,CACb;EACEC,IAAI,EAAE,aAAa;EAAE;EACrBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEH,UAAU;EACrBI,IAAI,EAAE;IACJC,UAAU,EAAE;EACd;AACF,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBD,IAAI,EAAE,YAAY;EAClB;EACAE,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,mBAAmB,CAAC;EAAA;EAC5CC,IAAI,EAAE;IACJC,UAAU,EAAE;EACd;AACF,CAAC,EACD;EACEH,IAAI,EAAE,SAAS;EACfD,IAAI,EAAE,UAAU;EAChB;EACAE,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,iBAAiB,CAAC;EAAA;EAC1CC,IAAI,EAAE;IACJC,UAAU,EAAE;EACd;AACF,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdD,IAAI,EAAE,SAAS;EACf;EACAE,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,gBAAgB,CAAC;EAAA;EACzCC,IAAI,EAAE;IACJC,UAAU,EAAE;EACd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD", "ignoreList": []}]}
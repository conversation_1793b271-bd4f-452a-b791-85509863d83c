{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\sysDict.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\sysDict.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'dict-page',\n  // 系统字典分页\n  method: 'POST',\n  path: '/base/dict/page'\n}, {\n  name: 'dict-save',\n  // 保存系统字典\n  method: 'POST',\n  path: '/base/dict/save'\n}, {\n  name: 'dict-delete',\n  // 删除系统字典\n  method: 'OTHER',\n  path: '/base/dict/delete'\n}, {\n  name: 'dict-status',\n  // 修改系统字典状态\n  method: 'OTHER',\n  path: '/base/dict/status'\n}, {\n  name: 'getParamList',\n  method: 'GET',\n  path: '/base/dict/list'\n}, {\n  name: 'getParam',\n  // 获取字典列表\n  method: 'OTHER',\n  path: '/base/dict/query'\n}, {\n  name: 'dict-item-page',\n  // 系统字典值分页\n  method: 'POST',\n  path: '/base/dict/item/page'\n}, {\n  name: 'dict-item-save',\n  // 保存系统字典值\n  method: 'POST',\n  path: '/base/dict/item/save'\n}, {\n  name: 'dict-item-delete',\n  // 删除系统字典值\n  method: 'POST',\n  path: '/base/dict/item/delete'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/sysDict.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'dict-page', // 系统字典分页\r\n    method: 'POST',\r\n    path: '/base/dict/page'\r\n  },\r\n  {\r\n    name: 'dict-save', // 保存系统字典\r\n    method: 'POST',\r\n    path: '/base/dict/save'\r\n  },\r\n  {\r\n    name: 'dict-delete', // 删除系统字典\r\n    method: 'OTHER',\r\n    path: '/base/dict/delete'\r\n  },\r\n  {\r\n    name: 'dict-status', // 修改系统字典状态\r\n    method: 'OTHER',\r\n    path: '/base/dict/status'\r\n  },\r\n  {\r\n    name: 'getParamList',\r\n    method: 'GET',\r\n    path: '/base/dict/list'\r\n  },\r\n  {\r\n    name: 'getParam', // 获取字典列表\r\n    method: 'OTHER',\r\n    path: '/base/dict/query'\r\n  },\r\n  {\r\n    name: 'dict-item-page', // 系统字典值分页\r\n    method: 'POST',\r\n    path: '/base/dict/item/page'\r\n  },\r\n  {\r\n    name: 'dict-item-save', // 保存系统字典值\r\n    method: 'POST',\r\n    path: '/base/dict/item/save'\r\n  },\r\n  {\r\n    name: 'dict-item-delete', // 删除系统字典值\r\n    method: 'POST',\r\n    path: '/base/dict/item/delete'\r\n  },\r\n];\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,WAAW;EAAE;EACnBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EAAE;EACnBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\getters.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\getters.js", "mtime": 1745205562807}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import { cloneDeep } from 'lodash';\nexport default {\n  userInfo: function userInfo(state) {\n    return cloneDeep(state.userInfo);\n  },\n  department: function department(state) {\n    return cloneDeep(state.department);\n  },\n  menuList: function menuList(state) {\n    return cloneDeep(state.menuList);\n  },\n  isAdmin: function isAdmin(state) {\n    return cloneDeep(state.isAdmin);\n  },\n  fullScreen: function fullScreen(state) {\n    return cloneDeep(state.fullScreen);\n  },\n  systemConfig: function systemConfig(state) {\n    return cloneDeep(state.systemConfig);\n  },\n  havePermission: function havePermission(state) {\n    return function (routeName) {\n      return state.permissionList.some(function (permission) {\n        return permission.code.toUpperCase() === routeName.toUpperCase();\n      });\n    };\n  }\n};", {"version": 3, "names": ["cloneDeep", "userInfo", "state", "department", "menuList", "isAdmin", "fullScreen", "systemConfig", "havePermission", "routeName", "permissionList", "some", "permission", "code", "toUpperCase"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/store/common/getters.js"], "sourcesContent": ["import {cloneDeep} from 'lodash';\r\n\r\nexport default {\r\n  userInfo: state => cloneDeep(state.userInfo),\r\n  department: state => cloneDeep(state.department),\r\n  menuList: state => cloneDeep(state.menuList),\r\n  isAdmin: state => cloneDeep(state.isAdmin),\r\n  fullScreen: state => cloneDeep(state.fullScreen),\r\n  systemConfig: state => cloneDeep(state.systemConfig),\r\n  havePermission: state => routeName => {\r\n    return state.permissionList.some(permission => {\r\n      return permission.code.toUpperCase() === routeName.toUpperCase();\r\n    });\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,QAAQ;AAEhC,eAAe;EACbC,QAAQ,EAAE,SAAVA,QAAQA,CAAEC,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACD,QAAQ,CAAC;EAAA;EAC5CE,UAAU,EAAE,SAAZA,UAAUA,CAAED,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACC,UAAU,CAAC;EAAA;EAChDC,QAAQ,EAAE,SAAVA,QAAQA,CAAEF,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACE,QAAQ,CAAC;EAAA;EAC5CC,OAAO,EAAE,SAATA,OAAOA,CAAEH,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACG,OAAO,CAAC;EAAA;EAC1CC,UAAU,EAAE,SAAZA,UAAUA,CAAEJ,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACI,UAAU,CAAC;EAAA;EAChDC,YAAY,EAAE,SAAdA,YAAYA,CAAEL,KAAK;IAAA,OAAIF,SAAS,CAACE,KAAK,CAACK,YAAY,CAAC;EAAA;EACpDC,cAAc,EAAE,SAAhBA,cAAcA,CAAEN,KAAK;IAAA,OAAI,UAAAO,SAAS,EAAI;MACpC,OAAOP,KAAK,CAACQ,cAAc,CAACC,IAAI,CAAC,UAAAC,UAAU,EAAI;QAC7C,OAAOA,UAAU,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAKL,SAAS,CAACK,WAAW,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC;EAAA;AACH,CAAC", "ignoreList": []}]}
/**
 * Loading 状态修复工具
 * 用于解决页面出现蒙层无法消除的问题
 */

export default {
  /**
   * 清除所有可能的遮罩层
   */
  clearAllMasks() {
    try {
      // 清除 Element UI 的 loading 遮罩
      const loadingMasks = document.querySelectorAll('.el-loading-mask');
      loadingMasks.forEach(mask => {
        if (mask.parentNode) {
          mask.parentNode.removeChild(mask);
        }
      });

      // 清除其他可能的遮罩层
      const overlayMasks = document.querySelectorAll('.el-overlay, [class*="mask"], [class*="loading"]');
      overlayMasks.forEach(mask => {
        const style = window.getComputedStyle(mask);
        if (style.position === 'fixed' || style.position === 'absolute') {
          if (style.zIndex > 100 && mask.offsetWidth > window.innerWidth * 0.5) {
            if (mask.parentNode) {
              mask.parentNode.removeChild(mask);
            }
          }
        }
      });

      console.log('已清除所有遮罩层');
    } catch (error) {
      console.error('清除遮罩层时出错:', error);
    }
  },

  /**
   * 重置所有 loading 状态
   */
  resetAllLoadingStates() {
    try {
      // 重置 body 上的 loading 类
      document.body.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');
      
      // 重置所有带有 loading 属性的元素
      const loadingElements = document.querySelectorAll('[aria-label*="loading"], [class*="loading"]');
      loadingElements.forEach(el => {
        el.removeAttribute('aria-label');
        el.classList.remove('el-loading-parent--relative', 'el-loading-parent--hidden');
      });

      console.log('已重置所有 loading 状态');
    } catch (error) {
      console.error('重置 loading 状态时出错:', error);
    }
  },

  /**
   * 强制清理页面遮罩层
   */
  forceClearMasks() {
    this.clearAllMasks();
    this.resetAllLoadingStates();
    
    // 延迟再次清理，确保动态生成的遮罩也被清除
    setTimeout(() => {
      this.clearAllMasks();
    }, 100);
  },

  /**
   * 监听并自动清理异常的遮罩层
   */
  startAutoCleanup() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1 && node.classList) {
              // 检查是否是 loading 遮罩
              if (node.classList.contains('el-loading-mask')) {
                // 5秒后自动清除长时间存在的遮罩
                setTimeout(() => {
                  if (node.parentNode) {
                    console.log('自动清除长时间存在的遮罩层');
                    node.parentNode.removeChild(node);
                  }
                }, 5000);
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return observer;
  },

  /**
   * 检查页面是否存在异常遮罩
   */
  checkForAbnormalMasks() {
    const masks = document.querySelectorAll('.el-loading-mask');
    const abnormalMasks = [];

    masks.forEach(mask => {
      const style = window.getComputedStyle(mask);
      if (style.display !== 'none' && mask.offsetParent !== null) {
        abnormalMasks.push({
          element: mask,
          zIndex: style.zIndex,
          position: style.position,
          parent: mask.parentNode
        });
      }
    });

    if (abnormalMasks.length > 0) {
      console.warn('发现异常遮罩层:', abnormalMasks);
      return abnormalMasks;
    }

    return null;
  },

  /**
   * 安装到 Vue 原型上
   */
  install(Vue) {
    Vue.prototype.$loadingFix = this;
    
    // 全局混入，在每个组件挂载时检查遮罩层
    Vue.mixin({
      mounted() {
        this.$nextTick(() => {
          const abnormalMasks = this.$loadingFix.checkForAbnormalMasks();
          if (abnormalMasks) {
            this.$loadingFix.forceClearMasks();
          }
        });
      }
    });
  }
};

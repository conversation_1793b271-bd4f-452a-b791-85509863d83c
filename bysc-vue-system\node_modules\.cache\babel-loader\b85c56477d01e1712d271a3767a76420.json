{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\const\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\const\\index.js", "mtime": 1745205562806}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import app from \"./app\";\nexport default {\n  app: app\n};", {"version": 3, "names": ["app"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/const/index.js"], "sourcesContent": ["import app from './app';\r\n\r\nexport default {\r\n  app\r\n};\r\n"], "mappings": "AAAA,OAAOA,GAAG;AAEV,eAAe;EACbA,GAAG,EAAHA;AACF,CAAC", "ignoreList": []}]}
import store from '@/plugins/store';
import router from '@/plugins/router';
import global from '@/global/global';
import {Message} from 'element-ui';
import NProgress from 'nprogress';
import localCache from '@/utils/storage';
export function routerBeforeEachFunc(to, from, next) {
  routerBeforeEachFuncNormal(to, from, next);
}
export function routerBeforeEachFuncNormal(to, from, next) {
  // 页面拦截、权限处理
  // iView.LoadingBar.start();
  NProgress.start();
  // 拦截每一个方法、页面/例外登录页面
  let permissions = store.state.common.permissions;
  let issome = permissions.some(e => {
    return e.resourcePath == to.path;
  });
  // if (!issome && permissions.length && to.name != 'home') {
  //   Message.error('您暂无该页面权限');
  //   return;
  // }
  let username = localCache.getLocal('nowUserName') || ''; // 用户登录信息
  let loginPage = global.LOGIN_PAGE_NAME;
  if (!username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound') { // 1.登录信息失效(且非登录页面)，统一跳转用户登录界面
    var reg = RegExp(/social/);
    var reg1 = RegExp(/callback/);
    console.log(reg.test(to.path) && reg1.test(to.path));
    if (reg.test(to.path) && reg1.test(to.path)) {
      var stype = to.path.match(/social(\S*)callback/)[1];
      stype.replace(/\//g, '');
      let {
        name,
        params,
        query
      } = {};
      name = 'social';
      query = to.query,
      query.stype = stype;
      router.push({
        name,
        params,
        query
      });
    } else {
      let {
        name,
        params,
        query
      } = {};
      name = loginPage;
      router.push({
        name,
        params,
        query
      });
    }
  } else if ((username && to.name !== loginPage && to.name !== 'social' && to.name !== 'errorPage' && to.name !== 'nofound' && !store.getters.menuList) || to.name === null) { // 2.登录信息存在，如果是登录状态,并且进入非登录页面
    // 先获取登录信息，后跳转业务界面
    if (to.path) {
      store.dispatch('login').then(data => {
        router.push({
          path: to.path
        });
      });
    } else {
      store.dispatch('login').then(data => {
        router.push({
          path: '/home/<USER>'
        });
      });
    }

  } else {
    next();
  }
}

export function routerAfterEachFunc(to) {
  NProgress.done();
  // iView.LoadingBar.finish();
}

<template>
  <div>
    <div id="login">
      <!--    账户登录-->
      <div class="titleStyle">{{ systemConfig.systemName }}</div>
      <div class="form-wrap">
        <div class="logoStyle">欢迎登录</div>
        <el-form ref="forms" :model="form" :rules="rules">
          <el-form-item prop="username">
            <el-input
              v-model.trim="form.username"
              prefix="md-person"
              size="large"
              placeholder="请输入账户名"
              @keydown.enter.native.prevent="submitLogin"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model.trim="form.password"
              prefix="ios-lock"
              type="password"
              size="large"
              placeholder="请输入密码"
              @keydown.enter.native.prevent="submitLogin"
            />
          </el-form-item>
          <el-form-item class="form-margin">
            <el-button
              style="width: 100%;"
              round
              @click="submitLogin"
              :loading="loading"
              type="primary"
              >{{ loading ? "登录中..." : "登录" }}</el-button>
            <div style="width: 100%; margin-top: 15px">
              <div style="float: left; color: #999">
                密码过期?<a @click="fingerprintLogin = 'edit'"> 去修改</a>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import Code from './code.vue';
import webscoket from '@/components/webscoket/webscoket.vue';
export default {
  name: 'Login',
  components: {Code, webscoket},
  data() {
    const validateMenuClassifyId = (rule, value, callback) => {
      if (value === '' || value.length === 0) {
        // 写逻辑从而达到验证值
        callback(new Error('请输入账户名！')); // callback（）暂时理解为回调，验证后回调给界面的信息
      } else {
        callback(); // 默认回调，没有异常值
      }
    };
    return {
      browserName: '',
      showSpin: true,
      sysconfig: {},
      checkDevice: false,
      hasDevice: false,
      code: '',
      vcode: '',
      fingerprintLogin: 'username',
      login: true,
      register: false,
      findpwd: false,
      disabled: false,
      pdisabled: false,
      time: 60,
      btntxt: '发送验证码',
      pbtntxt: '发送验证码',
      userRegister: {
        displayName: '',
        username: '',
        password: '',
        repassword: '',
        sex: '1',
        smsCode: '',
        email: '',
        mobile: '',
      },
      findPwdInfo: {
        mobile: '',
        smsCode: '',
        password: '',
        repassword: '',
      },
      loading: false,
      remember: true,
      editform: {
        username: '',
        password: '',
        oldPwd: '',
        surePassword: '',
      },
      editrules: {
        username: [
          {
            required: true,
            validator: validateMenuClassifyId,
            trigger: 'blur',
          },
        ],
        surePassword: {
          required: true,
          message: '请输入确认密码',
          trigger: 'blur',
        },
        oldPwd: {
          required: true,
          message: '请输入旧密码',
          trigger: 'blur',
        },
        password: {
          required: true,
          message: '请输入新密码',
          trigger: 'blur',
        },
      },
      fingerCodeList: [],
      validateId: '',
      validateNum: 0,
      form: {
        username: '',
        password: '',
        img: '',
      },
      isFingerSuccess: '0',
      rules: {
        username: [
          {
            required: true,
            validator: validateMenuClassifyId,
            trigger: 'blur',
          },
        ],
        password: {
          required: true,
          message: '密码不能为空',
          trigger: 'blur',
        },
      },
      findrules: {
        password: [
          {
            required: true,
            message: '密码不能为空',
            trigger: 'blur',
          },
          // {
          //   pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
          //   message: '密码必须包含字母和数字且在6-18位之间',
          //   trigger: 'blur'
          // }
        ],
        repassword: [
          {
            required: true,
            message: '确认密码不能为空',
            trigger: 'blur',
          },
          // {
          //   pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
          //   message: '密码必须包含字母和数字且在6-18位之间',
          //   trigger: 'blur'
          // }
        ],
        smsCode: [
          {
            required: true,
            message: '验证码不能为空',
            trigger: 'blur',
          },
        ],
        mobile: [
          {
            required: true,
            message: '手机号不能为空',
            trigger: 'blur',
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
      },
      regrules: {
        username: [
          {required: true, message: '账户名不能为空', trigger: 'change'},
          {
            pattern: /^[a-zA-Z0-9_]{4,16}$/,
            message: '支持英文、数字、“_”的组合，4-16个字符',
            trigger: 'blur',
          },
          // {
          //   required: true,
          //   message: '账户名不能为空,且不能超过16位',
          //   trigger: 'blur'
          // },
          // {
          //   pattern: /[^\u4e00-\u9fa5]/,
          //   message: '账户名不能为中文',
          //   trigger: 'blur'
          // }
        ],
        password: [
          {
            required: true,
            message: '密码不能为空',
            trigger: 'blur',
          },
          // {
          //   pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)/,
          //   message: '密码必须包含字母和数字',
          //   trigger: 'blur'
          // },
          // {
          //   pattern: /^[0-9A-Za-z]{6,18}$/,
          //   message: '密码必须在6-18位之间',
          //   trigger: 'blur'
          // }
        ],
        repassword: [
          {
            required: true,
            message: '确认密码不能为空',
            trigger: 'blur',
          },
          // {
          //   pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)/,
          //   message: '密码必须包含字母和数字',
          //   trigger: 'blur'
          // },
          // {
          //   pattern: /^[0-9A-Za-z]{6,18}$/,
          //   message: '密码必须在6-18位之间',
          //   trigger: 'blur'
          // }
        ],
        smsCode: [
          {
            required: true,
            message: '验证码不能为空',
            trigger: 'blur',
          },
        ],
        mobile: [
          {
            required: true,
            message: '手机号不能为空',
            trigger: 'blur',
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        displayName: [
          {
            required: true,
            message: '真实姓名不能为空',
            trigger: 'blur',
          },
          {
            pattern: /^[\u2E80-\u9FFF]+$/,
            message: '请输入您的真实姓名',
            trigger: 'blur',
          },
        ],
        email: [
          {type: 'email', message: '请输入正确的邮箱名', trigger: 'change'},
          // {
          //   required: false,
          //   pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
          //   message: '请输入正确的邮箱',
          //   trigger: 'blur'
          // }
        ],
      },
    };
  },
  computed: {
    systemConfig() {
      return this.$store.state.common.systemConfig;
    },
  },
  beforeCreate() {},
  beforeDestroy() {

  },
  created() {
    this.browserName = this.judgeBrowser();
  },
  mounted() {
  },
  methods: {
    judgeBrowser() {
      var userAgent = navigator.userAgent;
      // 判断是否Opera浏览器
      if (userAgent.indexOf('Opera') > -1) {
        return 'Opera';
      }
      // 判断是否Firefox浏览器
      if (userAgent.indexOf('Firefox') > -1) {
        return 'FF';
      }
      // 判断是否chorme浏览器
      if (userAgent.indexOf('Chrome') > -1) {
        return 'Chrome';
      }
      // 判断是否Safari浏览器
      if (userAgent.indexOf('Safari') > -1) {
        return 'Safari';
      }
      // 判断是否IE浏览器
      if (
        userAgent.indexOf('compatible') > -1
        && userAgent.indexOf('MSIE') > -1
        && !isOpera
      ) {
        return 'IE';
      }
      // 判断是否Edge浏览器
      if (userAgent.indexOf('Trident') > -1) {
        return 'Edge';
      }
    },
    // 登录web系统
    submitLogin() {
      setTimeout(() => {
        this.$refs.forms.validate(valid => {
          if (valid) {
            this.loading = true;
            this.$store
              .dispatch('login', {
                username: this.form.username,
                password: this.form.password,
                grant_type: 'password'
              })
              .then(res => {
                let permissions = [];
                // eslint-disable-next-line max-nested-callbacks
                res.permissions.forEach(e => {
                  if (e.resourceType == 0 && e.url) {
                    permissions.push(e.url);
                  }
                });
                let roles = res.roles.length;
                if (
                  res.account.status == 1
                    && res.account.type != 2
                    && roles
                    && permissions.length !== 0
                ) {
                  let _this = this;
                  // 延迟遍历菜单权限，以免循环速度太快跳到后面的页面
                  for (var i = 0; i < permissions.length; i++) {
                    // eslint-disable-next-line max-nested-callbacks
                    (function (t, data) {
                      // 注意这里是形参
                      // eslint-disable-next-line max-nested-callbacks
                      setTimeout(function () {
                        if (window.location.pathname !== '/adminLogin') {
                          return false;
                        }
                        _this.$router.push({
                          path: data,
                          params: {},
                        });
                      }, 150 * t); // 还是每秒执行一次，不是累加的
                    })(i, permissions[i]); // 注意这里是实参，这里把要用的参数传进去
                  }
                  // this.pwdlocal();
                }

              })
              .finally(datas => {
                this.loading = false;
              })
              .catch(err => {
                this.vcode = '';
              });
          }
        });
      }, 200);
    },
  },
};
</script>
<style lang="less" scoped>
.icons {
  float: left;
  margin-top: 10px;
  position: absolute;
  left: 10px;
}
.titleStyle {
  width: 100%;
  text-align: center;
  font-size: 30px;
  color: #ffffff;
  margin-top: 15vh;
}
.logoStyle {
  width: 100%;
  margin: 20px 0;
  text-align: center;
  font-size: 32px;
  color: #0c78d4;
}
#login {
  width: 100%;
  height: 100%;
  min-height: 660px;
  position: fixed;
  z-index: 1;
  background: url("./image/loginbg.png");
  background-size: 100% 100%;
  .login-mask {
    height: 100%;
  }
  .form-sub {
    .icon {
      vertical-align: middle;
      margin-left: 16px;
      font-size: 30px;
      color: rgba(0, 0, 0, 0.2);
      transition: all 0.5s;
      cursor: pointer;
    }
    i.icon-weibo-copy {
      .icon;
      &:hover {
        color: rgba(230, 22, 45, 0.7);
      }
    }
  }
  .form-wrap {
    background-color: #ffffff;
    border-radius: 30px;
    h1 {
      text-align: center;
      padding-bottom: 15px;
      font-size: 33px;
    }
    p {
      text-align: center;
      padding: 15px 0;
      span {
        padding: 0 10px;
        padding-bottom: 10px;
        font-size: 18px;
        display: inline-block;
      }
    }
    /*border: 1px solid aqua;*/
    width: 380px;
    padding: 10px 30px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 1px 1px 1px #ffffff;
  }
  .form-margin {
    margin-bottom: 10px;
  }
  .form-code {
    padding-right: 105px;
    position: relative;
  }
  .checkcode {
    margin-top: 2px;
    position: absolute;
    right: 0;
    top: 0;
    height: 36px;
  }
  @media screen and(max-width:678px) {
    .form-wrap {
      width: 90%;
      margin-left: 0;
    }
  }
}
</style>

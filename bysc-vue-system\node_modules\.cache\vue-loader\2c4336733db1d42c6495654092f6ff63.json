{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\login\\code.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\views\\login\\code.vue", "mtime": 1745205562837}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./code.vue?vue&type=template&id=593d0ced\"\nimport script from \"./code.vue?vue&type=script&lang=js\"\nexport * from \"./code.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('593d0ced')) {\n      api.createRecord('593d0ced', component.options)\n    } else {\n      api.reload('593d0ced', component.options)\n    }\n    module.hot.accept(\"./code.vue?vue&type=template&id=593d0ced\", function () {\n      api.rerender('593d0ced', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/login/code.vue\"\nexport default component.exports"]}
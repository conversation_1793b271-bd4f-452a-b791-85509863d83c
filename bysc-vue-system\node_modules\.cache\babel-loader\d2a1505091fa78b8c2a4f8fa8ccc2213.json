{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue", "mtime": 1753782531199}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport _toConsumableArray from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es6.number.constructor\";\nexport default {\n  props: {\n    showPage: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    eventBus: Object,\n    newcolumn: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    searchParams: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    autoLoad: {\n      type: <PERSON>olean,\n      default: function _default() {\n        return true;\n      }\n    },\n    showColumn: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    showReset: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    showSearch: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    api: {\n      type: String,\n      default: function _default() {\n        return '';\n      }\n    },\n    pageSizeOpts: {\n      type: Array,\n      default: function _default() {\n        return [10, 20, 50];\n      },\n      validator: function validator(val) {\n        return Array.isArray(val) && val.length > 0 && val.reduce(function (result, item) {\n          return typeof item === 'number' && item > 0 && result;\n        }, true);\n      }\n    },\n    currentPage: {\n      type: Number,\n      default: 1\n    }\n  },\n  inject: ['showLoading', 'hideLoading'],\n  watch: {\n    currentPage: {\n      deep: true,\n      immediate: true,\n      handler: function handler(newVal) {\n        if (newVal) {\n          this.localCurrentPage = newVal;\n        }\n      }\n    }\n  },\n  data: function data() {\n    return {\n      drawer: false,\n      direction: 'rtl',\n      single: false,\n      showSearchForm: true,\n      checkAllGroup1: [],\n      checkAllGroup: [],\n      columnArr: [],\n      localCurrentPage: 1,\n      loading: false,\n      rows: [],\n      total: 0,\n      pageSize: this.pageSizeOpts[0]\n    };\n  },\n  created: function created() {\n    var _this = this;\n    if (this.autoLoad) {\n      this.query();\n    }\n    this.eventBus.$on('search', function (e) {\n      if (e !== 'update') {\n        _this.localCurrentPage = 1;\n      }\n      _this.query();\n    });\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n    setTimeout(function () {\n      _this2.columnArr = _toConsumableArray(_this2.newcolumn);\n      _this2.columnArr.forEach(function (e) {\n        if (e.title) {\n          _this2.checkAllGroup.push(e);\n          _this2.checkAllGroup1.push(e.title);\n        }\n      });\n    }, 100);\n  },\n  methods: {\n    showAll: function showAll() {\n      var arr = [];\n      this.checkAllGroup.forEach(function (e) {\n        arr.push(e.title);\n      });\n      this.checkAllGroup1 = arr;\n      this.checkAllGroupChange(arr);\n    },\n    checkAllGroupChange: function checkAllGroupChange(e) {\n      var arr = [];\n      if (this.columnArr[0] && this.columnArr[0].type == 'selection') {\n        arr.push({\n          type: 'selection',\n          width: 60,\n          align: 'center',\n          fixed: 'left'\n        });\n      }\n      this.columnArr.forEach(function (item) {\n        var isSome = e.some(function (row) {\n          return row == item.title;\n        });\n        if (isSome) {\n          arr.push(item);\n        }\n      });\n      this.$emit('columnChange', arr);\n    },\n    query: function query(isTimer) {\n      var _this3 = this;\n      if (isTimer) {\n        this.loading = true;\n        this.$api[this.api]({\n          current: this.localCurrentPage,\n          limit: this.pageSize,\n          param: this.searchParams\n        }).then(function (res) {\n          if (res) {\n            _this3.total = res.total;\n            if (Array.isArray(res.list) && res.list.length > 0) {\n              _this3.rows = res.list;\n            } else {\n              _this3.rows = [];\n            }\n            _this3.$emit('datas', res.list);\n            _this3.$emit('total', res.total);\n          }\n        }).finally(function () {\n          _this3.loading = false;\n        });\n      } else {\n        this.localCurrentPage = 1;\n        this.loading = true;\n        this.$api[this.api]({\n          current: 1,\n          limit: this.pageSize,\n          param: this.searchParams\n        }).then(function (res) {\n          if (res) {\n            _this3.total = res.total;\n            if (Array.isArray(res.list) && res.list.length > 0) {\n              _this3.rows = res.list;\n            } else {\n              _this3.rows = [];\n            }\n            _this3.$emit('datas', res.list);\n            _this3.$emit('total', res.total);\n          }\n        }).finally(function () {\n          _this3.loading = false;\n        });\n      }\n    },\n    queryData: function queryData() {\n      var _this4 = this;\n      this.localCurrentPage = 1;\n      this.loading = true;\n      this.$api[this.api]({\n        current: this.localCurrentPage,\n        limit: this.pageSize,\n        param: this.searchParams\n      }).then(function (res) {\n        if (res) {\n          _this4.total = res.total;\n          if (Array.isArray(res.list) && res.list.length > 0) {\n            _this4.rows = res.list;\n          } else {\n            _this4.rows = [];\n          }\n          _this4.$emit('datas', res.list);\n          _this4.$emit('total', res.total);\n        }\n      }).finally(function () {\n        _this4.loading = false;\n      });\n    },\n    handlePageChange: function handlePageChange(current) {\n      this.localCurrentPage = current;\n      this.query(true);\n      this.$emit('pageChange');\n    },\n    handlePageSizeChange: function handlePageSizeChange(pageSize) {\n      this.localCurrentPage = 1;\n      this.pageSize = pageSize;\n      this.query();\n    },\n    // fanqz add 补充可以获取table当前数据\n    getTableData: function getTableData() {\n      return this.rows;\n    },\n    setTableData: function setTableData(rows) {\n      this.rows = rows;\n    }\n  }\n};", {"version": 3, "names": ["props", "showPage", "type", "Boolean", "default", "eventBus", "Object", "newcolumn", "Array", "searchParams", "autoLoad", "showColumn", "showReset", "showSearch", "api", "String", "pageSizeOpts", "validator", "val", "isArray", "length", "reduce", "result", "item", "currentPage", "Number", "inject", "watch", "deep", "immediate", "handler", "newVal", "localCurrentPage", "data", "drawer", "direction", "single", "showSearchForm", "checkAllGroup1", "checkAllGroup", "columnArr", "loading", "rows", "total", "pageSize", "created", "_this", "query", "$on", "e", "mounted", "_this2", "setTimeout", "_toConsumableArray", "for<PERSON>ach", "title", "push", "methods", "showAll", "arr", "checkAllGroupChange", "width", "align", "fixed", "isSome", "some", "row", "$emit", "isTimer", "_this3", "$api", "current", "limit", "param", "then", "res", "list", "finally", "queryData", "_this4", "handlePageChange", "handlePageSizeChange", "getTableData", "setTableData"], "sources": ["src/components/Grid.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-show=\"showSearchForm\" style=\"width: 100%;margin-bottom: 20px;\">\r\n      <slot name=\"search\"></slot>\r\n    </div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <div style=\"float: left\">\r\n          <slot name=\"action\"></slot>\r\n        </div>\r\n        <el-drawer :wrapperClosable=\"false\"\r\n          size=\"200px\"\r\n          title=\"展示/隐藏列\"\r\n          :visible.sync=\"drawer\"\r\n          :direction=\"direction\"\r\n          :append-to-body=\"true\"\r\n        >\r\n          <div style=\"padding: 0 20px; z-index: 999\">\r\n            <div @click.stop=\"showSearchForm = showSearchForm\">\r\n              <el-checkbox-group\r\n                @change=\"checkAllGroupChange($event, index)\"\r\n                v-for=\"(item, index) of checkAllGroup\"\r\n                :key=\"index + 'CheckboxGroup'\"\r\n                v-model=\"checkAllGroup1\"\r\n              >\r\n                <el-checkbox\r\n                  style=\"float: left; clear: both\"\r\n                  :label=\"item.title\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n              <div\r\n                style=\"\r\n                  text-align: center;\r\n                  width: 100%;\r\n                  clear: both;\r\n                  font-size: 14px;\r\n                  color: cornflowerblue;\r\n                  position: relative;\r\n                  top: 10px;\r\n                \"\r\n                v-show=\"checkAllGroup.length > checkAllGroup1.length\"\r\n              >\r\n                <a @click=\"showAll\">全部展示</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-drawer>\r\n        <el-tooltip\r\n          v-if=\"showColumn\"\r\n          style=\"float: right; margin-bottom: 10px\"\r\n          content=\"操作列\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <div>\r\n            <el-button\r\n              size=\"small\"\r\n              circle\r\n              @click.native=\"drawer = true\"\r\n              icon=\"el-icon-s-grid\"\r\n            ></el-button>\r\n          </div>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          v-if=\"showReset\"\r\n          style=\"float: right; margin-right: 10px; margin-bottom: 10px\"\r\n          content=\"刷新\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <el-button\r\n            size=\"small\"\r\n            circle\r\n            @click.native=\"query\"\r\n            icon=\"el-icon-refresh\"\r\n          ></el-button>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          v-if=\"showSearch\"\r\n          style=\"float: right; margin-right: 10px; margin-bottom: 10px\"\r\n          :content=\"showSearchForm ? '隐藏搜索' : '展示搜索'\"\r\n          placement=\"top\"\r\n          :transfer=\"true\"\r\n        >\r\n          <el-button\r\n            size=\"small\"\r\n            @click=\"showSearchForm = !showSearchForm\"\r\n            circle\r\n            icon=\"el-icon-search\"\r\n          ></el-button>\r\n        </el-tooltip>\r\n      </el-col>\r\n    </el-row>\r\n    <slot name=\"other\"></slot>\r\n    <slot name=\"table\" :data=\"rows\" :loading=\"loading\"></slot>\r\n    <el-row\r\n      type=\"flex\"\r\n      justify=\"end\"\r\n      style=\"margin-top: 10px; padding-bottom: 5px; margin-bottom: 10px\"\r\n    >\r\n      <el-pagination\r\n        v-show=\"showPage\"\r\n        @size-change=\"handlePageSizeChange\"\r\n        @current-change=\"handlePageChange\"\r\n        :current-page.sync=\"localCurrentPage\"\r\n        :page-sizes=\"pageSizeOpts\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    showPage: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    eventBus: Object,\r\n    newcolumn: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    searchParams: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n        };\r\n      },\r\n    },\r\n    autoLoad: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showColumn: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showReset: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showSearch: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    api: {\r\n      type: String,\r\n      default() {\r\n        return '';\r\n      },\r\n    },\r\n    pageSizeOpts: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 50];\r\n      },\r\n      validator(val) {\r\n        return (\r\n          Array.isArray(val)\r\n          && val.length > 0\r\n          && val.reduce((result, item) => {\r\n            return typeof item === 'number' && item > 0 && result;\r\n          }, true)\r\n        );\r\n      },\r\n    },\r\n    currentPage: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  inject: ['showLoading', 'hideLoading'],\r\n  watch: {\r\n    currentPage: {\r\n      deep: true,\r\n      immediate: true,\r\n      handler: function (newVal) {\r\n        if (newVal) {\r\n          this.localCurrentPage = newVal;\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      direction: 'rtl',\r\n      single: false,\r\n      showSearchForm: true,\r\n      checkAllGroup1: [],\r\n      checkAllGroup: [],\r\n      columnArr: [],\r\n      localCurrentPage: 1,\r\n      loading: false,\r\n      rows: [],\r\n      total: 0,\r\n      pageSize: this.pageSizeOpts[0],\r\n    };\r\n  },\r\n  created() {\r\n    if (this.autoLoad) {\r\n      this.query();\r\n    }\r\n    this.eventBus.$on('search', e => {\r\n      if (e !== 'update') {\r\n        this.localCurrentPage = 1;\r\n      }\r\n      this.query();\r\n    });\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.columnArr = [...this.newcolumn];\r\n      this.columnArr.forEach(e => {\r\n        if (e.title) {\r\n          this.checkAllGroup.push(e);\r\n          this.checkAllGroup1.push(e.title);\r\n        }\r\n      });\r\n    }, 100);\r\n  },\r\n  methods: {\r\n    showAll() {\r\n      let arr = [];\r\n      this.checkAllGroup.forEach(e => {\r\n        arr.push(e.title);\r\n      });\r\n      this.checkAllGroup1 = arr;\r\n      this.checkAllGroupChange(arr);\r\n    },\r\n    checkAllGroupChange(e) {\r\n      let arr = [];\r\n      if (this.columnArr[0] && this.columnArr[0].type == 'selection') {\r\n        arr.push({\r\n          type: 'selection',\r\n          width: 60,\r\n          align: 'center',\r\n          fixed: 'left',\r\n        });\r\n      }\r\n      this.columnArr.forEach(item => {\r\n        let isSome = e.some(row => {\r\n          return row == item.title;\r\n        });\r\n        if (isSome) {\r\n          arr.push(item);\r\n        }\r\n      });\r\n      this.$emit('columnChange', arr);\r\n    },\r\n    query(isTimer) {\r\n      if (isTimer) {\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n        ).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      } else {\r\n        this.localCurrentPage = 1;\r\n        this.loading = true;\r\n        this.$api[this.api]({\r\n          current: 1,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }).then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        }).finally(() => {\r\n          this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    queryData() {\r\n      this.localCurrentPage = 1;\r\n      this.loading = true;\r\n      this.$api[this.api](\r\n        {\r\n          current: this.localCurrentPage,\r\n          limit: this.pageSize,\r\n          param: this.searchParams\r\n        }\r\n      )\r\n        .then(res => {\r\n          if (res) {\r\n            this.total = res.total;\r\n            if (Array.isArray(res.list) && res.list.length > 0) {\r\n              this.rows = res.list;\r\n            } else {\r\n              this.rows = [];\r\n            }\r\n            this.$emit('datas', res.list);\r\n            this.$emit('total', res.total);\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handlePageChange(current) {\r\n      this.localCurrentPage = current;\r\n      this.query(true);\r\n      this.$emit('pageChange');\r\n    },\r\n    handlePageSizeChange(pageSize) {\r\n      this.localCurrentPage = 1;\r\n      this.pageSize = pageSize;\r\n      this.query();\r\n    },\r\n    // fanqz add 补充可以获取table当前数据\r\n    getTableData() {\r\n      return this.rows;\r\n    },\r\n    setTableData(rows) {\r\n      this.rows = rows;\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;AAoHA;EACAA,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,QAAA,EAAAC,MAAA;IACAC,SAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAK,YAAA;MACAP,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QACA,QACA;MACA;IACA;IACAM,QAAA;MACAR,IAAA,EAAAC,OAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAC,OAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAC,OAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAS,UAAA;MACAX,IAAA,EAAAC,OAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAU,GAAA;MACAZ,IAAA,EAAAa,MAAA;MACAX,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QACA;MACA;MACAa,SAAA,WAAAA,UAAAC,GAAA;QACA,OACAV,KAAA,CAAAW,OAAA,CAAAD,GAAA,KACAA,GAAA,CAAAE,MAAA,QACAF,GAAA,CAAAG,MAAA,WAAAC,MAAA,EAAAC,IAAA;UACA,cAAAA,IAAA,iBAAAA,IAAA,QAAAD,MAAA;QACA;MAEA;IACA;IACAE,WAAA;MACAtB,IAAA,EAAAuB,MAAA;MACArB,OAAA;IACA;EACA;EACAsB,MAAA;EACAC,KAAA;IACAH,WAAA;MACAI,IAAA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAC,gBAAA,GAAAD,MAAA;QACA;MACA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,SAAA;MACAC,MAAA;MACAC,cAAA;MACAC,cAAA;MACAC,aAAA;MACAC,SAAA;MACAR,gBAAA;MACAS,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,QAAA,OAAA5B,YAAA;IACA;EACA;EACA6B,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAApC,QAAA;MACA,KAAAqC,KAAA;IACA;IACA,KAAA1C,QAAA,CAAA2C,GAAA,qBAAAC,CAAA;MACA,IAAAA,CAAA;QACAH,KAAA,CAAAd,gBAAA;MACA;MACAc,KAAA,CAAAC,KAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACAC,UAAA;MACAD,MAAA,CAAAX,SAAA,GAAAa,kBAAA,CAAAF,MAAA,CAAA5C,SAAA;MACA4C,MAAA,CAAAX,SAAA,CAAAc,OAAA,WAAAL,CAAA;QACA,IAAAA,CAAA,CAAAM,KAAA;UACAJ,MAAA,CAAAZ,aAAA,CAAAiB,IAAA,CAAAP,CAAA;UACAE,MAAA,CAAAb,cAAA,CAAAkB,IAAA,CAAAP,CAAA,CAAAM,KAAA;QACA;MACA;IACA;EACA;EACAE,OAAA;IACAC,OAAA,WAAAA,QAAA;MACA,IAAAC,GAAA;MACA,KAAApB,aAAA,CAAAe,OAAA,WAAAL,CAAA;QACAU,GAAA,CAAAH,IAAA,CAAAP,CAAA,CAAAM,KAAA;MACA;MACA,KAAAjB,cAAA,GAAAqB,GAAA;MACA,KAAAC,mBAAA,CAAAD,GAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAX,CAAA;MACA,IAAAU,GAAA;MACA,SAAAnB,SAAA,YAAAA,SAAA,IAAAtC,IAAA;QACAyD,GAAA,CAAAH,IAAA;UACAtD,IAAA;UACA2D,KAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACA,KAAAvB,SAAA,CAAAc,OAAA,WAAA/B,IAAA;QACA,IAAAyC,MAAA,GAAAf,CAAA,CAAAgB,IAAA,WAAAC,GAAA;UACA,OAAAA,GAAA,IAAA3C,IAAA,CAAAgC,KAAA;QACA;QACA,IAAAS,MAAA;UACAL,GAAA,CAAAH,IAAA,CAAAjC,IAAA;QACA;MACA;MACA,KAAA4C,KAAA,iBAAAR,GAAA;IACA;IACAZ,KAAA,WAAAA,MAAAqB,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAA3B,OAAA;QACA,KAAA6B,IAAA,MAAAxD,GAAA;UACAyD,OAAA,OAAAvC,gBAAA;UACAwC,KAAA,OAAA5B,QAAA;UACA6B,KAAA,OAAAhE;QACA,CACA,EAAAiE,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAN,MAAA,CAAA1B,KAAA,GAAAgC,GAAA,CAAAhC,KAAA;YACA,IAAAnC,KAAA,CAAAW,OAAA,CAAAwD,GAAA,CAAAC,IAAA,KAAAD,GAAA,CAAAC,IAAA,CAAAxD,MAAA;cACAiD,MAAA,CAAA3B,IAAA,GAAAiC,GAAA,CAAAC,IAAA;YACA;cACAP,MAAA,CAAA3B,IAAA;YACA;YACA2B,MAAA,CAAAF,KAAA,UAAAQ,GAAA,CAAAC,IAAA;YACAP,MAAA,CAAAF,KAAA,UAAAQ,GAAA,CAAAhC,KAAA;UACA;QACA,GAAAkC,OAAA;UACAR,MAAA,CAAA5B,OAAA;QACA;MACA;QACA,KAAAT,gBAAA;QACA,KAAAS,OAAA;QACA,KAAA6B,IAAA,MAAAxD,GAAA;UACAyD,OAAA;UACAC,KAAA,OAAA5B,QAAA;UACA6B,KAAA,OAAAhE;QACA,GAAAiE,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAN,MAAA,CAAA1B,KAAA,GAAAgC,GAAA,CAAAhC,KAAA;YACA,IAAAnC,KAAA,CAAAW,OAAA,CAAAwD,GAAA,CAAAC,IAAA,KAAAD,GAAA,CAAAC,IAAA,CAAAxD,MAAA;cACAiD,MAAA,CAAA3B,IAAA,GAAAiC,GAAA,CAAAC,IAAA;YACA;cACAP,MAAA,CAAA3B,IAAA;YACA;YACA2B,MAAA,CAAAF,KAAA,UAAAQ,GAAA,CAAAC,IAAA;YACAP,MAAA,CAAAF,KAAA,UAAAQ,GAAA,CAAAhC,KAAA;UACA;QACA,GAAAkC,OAAA;UACAR,MAAA,CAAA5B,OAAA;QACA;MACA;IACA;IACAqC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAA/C,gBAAA;MACA,KAAAS,OAAA;MACA,KAAA6B,IAAA,MAAAxD,GAAA,EACA;QACAyD,OAAA,OAAAvC,gBAAA;QACAwC,KAAA,OAAA5B,QAAA;QACA6B,KAAA,OAAAhE;MACA,CACA,EACAiE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAI,MAAA,CAAApC,KAAA,GAAAgC,GAAA,CAAAhC,KAAA;UACA,IAAAnC,KAAA,CAAAW,OAAA,CAAAwD,GAAA,CAAAC,IAAA,KAAAD,GAAA,CAAAC,IAAA,CAAAxD,MAAA;YACA2D,MAAA,CAAArC,IAAA,GAAAiC,GAAA,CAAAC,IAAA;UACA;YACAG,MAAA,CAAArC,IAAA;UACA;UACAqC,MAAA,CAAAZ,KAAA,UAAAQ,GAAA,CAAAC,IAAA;UACAG,MAAA,CAAAZ,KAAA,UAAAQ,GAAA,CAAAhC,KAAA;QACA;MACA,GACAkC,OAAA;QACAE,MAAA,CAAAtC,OAAA;MACA;IACA;IACAuC,gBAAA,WAAAA,iBAAAT,OAAA;MACA,KAAAvC,gBAAA,GAAAuC,OAAA;MACA,KAAAxB,KAAA;MACA,KAAAoB,KAAA;IACA;IACAc,oBAAA,WAAAA,qBAAArC,QAAA;MACA,KAAAZ,gBAAA;MACA,KAAAY,QAAA,GAAAA,QAAA;MACA,KAAAG,KAAA;IACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MACA,YAAAxC,IAAA;IACA;IACAyC,YAAA,WAAAA,aAAAzC,IAAA;MACA,KAAAA,IAAA,GAAAA,IAAA;IACA;EACA;AACA", "ignoreList": []}]}
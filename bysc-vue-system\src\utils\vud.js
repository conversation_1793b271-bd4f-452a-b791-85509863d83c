import CryptoJS from 'crypto-js';
import localCache from '@/utils/storage';

// 解密函数 - 用于解密密钥
function decryptKey(encryptedKey) {
  try {
    return atob(encryptedKey);
  } catch (error) {
    console.error('Key decryption failed:', error);
    return '';
  }
}

// 加密函数
export function encryptVud(username) {
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数
  const str = `${username}+${timestamp}${random}`;

  // 使用正确的加密后的key
  const encryptedKey = "JEJvd2VpPUAyMDI1Li44OA==";
  const cryptoKey = decryptKey(encryptedKey);

  try {
    const key = CryptoJS.enc.Utf8.parse(cryptoKey);
    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);
    const strUtf8 = CryptoJS.enc.Utf8.parse(str);
    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
  } catch (error) {
    console.error('Vud encryption failed:', error);
    return '';
  }
}

// 获取当前用户的 vud
export function getCurrentVud() {
  const username = window.$cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';
  return encryptVud(username);
}
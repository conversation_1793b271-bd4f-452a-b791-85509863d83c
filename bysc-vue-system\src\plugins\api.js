import axios from './axios';
import qs from 'qs';
import CryptoJS from 'crypto-js';
import {
  API_DEFAULT_CONFIG
} from '@/config';
// 引入系统API MAP
import {
  API_CONFIG
} from '@/global/module-config';
import {
  assert
} from '@/utils';
import {
  assign,
  isEmpty
} from 'lodash';
import localCache from '@/utils/storage';

// 解密函数 - 用于解密密钥
function decryptKey(encryptedKey) {
  try {
    return atob(encryptedKey);
  } catch (error) {
    console.error('Key decryption failed:', error);
    return '';
  }
}

// 加密函数
function encryptVud(username) {
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数
  const str = `${username}+${timestamp}${random}`;

  // 使用正确的加密后的key
  const encryptedKey = "JEJvd2VpPUAyMDI1Li44OA==";
  const cryptoKey = decryptKey(encryptedKey);

  try {
    const key = CryptoJS.enc.Utf8.parse(cryptoKey);
    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);
    const strUtf8 = CryptoJS.enc.Utf8.parse(str);
    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
  } catch (error) {
    console.error('Vud encryption failed:', error);
    return '';
  }
}

// 添加 JWT token 解析函数
function parseJwt(token) {
  try {
    if (!token) {
      return null;
    }
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Token parsing failed:', error);
    return null;
  }
}

class MakeApi {
  constructor(options) {
    this.api = {};
    this.apiBuilder(options);
  }

  apiBuilder({
    sep = '|',
    debug = false,
    config = {}
  }) {
    Object.keys(config).map(namespace => {
      this._apiSingleBuilder({
        namespace,
        sep,
        debug,
        config: config[namespace]
      });
    });
  }

  _apiSingleBuilder({
    namespace,
    sep,
    debug,
    config
  }) {
    config.forEach(api => {
      let {
        name,
        params,
        method,
        path,
        noCheck
      } = api;
      const apiName = `${namespace}${sep}${name}`;
      const apiUrl = path;

      debug && assert(name, `${apiUrl}: 接口name属性不能为空`);
      debug && assert(apiUrl.indexOf('/') === 0, `${apiUrl}: 接口路径path，首字符应为/`);
      Object.defineProperty(this.api, apiName, {
        value(outerParams, outerOptions) {
          let _params;
          if (Array.isArray(outerParams)) {
            _params = outerParams;
          } else {
            _params = isEmpty(outerParams) ? params : assign({}, params, outerParams);
          }
          let url = _replaceURLparams('/api' + apiUrl, _params);
          let reg = /<[^>]+>/g; // 违规字符检测正则
          let count = 0; // 参数中含有违规标签个数
          _params && Object.keys(_params).forEach(e => {
            if (reg.test(_params[e])) {
              count += 1; // 如果检测到则数量加1
            }
          });
          // 如果存在不合规的标签则阻止请求,如果noCheck为true
          if (count && !noCheck) {
            // 如果这个接口noCheck为false就代表需要检测，且有参数中存在违规字符，则将接口地址变掉
            url = '/app/ics/check-param-fail';
            method = 'POST';
          }
          return axios(_normoalize(assign({
            url,
            method
          }, outerOptions), _params)).catch(error => { // 全局捕获异常，后续继续then，如果去掉本段代码，则后续不继续then
            switch (error.response ? error.response.status : 500) {
              case 400:
              case 404:
              case 405:
              case 500:
                throw error;
              default:
                break;
            }
          });
        }
      });
    });
  }
}

function _replaceURLparams(url, data) {
  return url.replace(/:([\w\d]+)/ig, (reg, key) => {
    return data[key];
  });
}

function _normoalize(options, data) {
  let token = $cookies.get('userToken');
  // 从 token 中解析出 account
  const tokenData = parseJwt(token);
  const username = tokenData ? tokenData.account : '';
  // 如果没有从token中获取到username，尝试从其他地方获取
  const finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';

  const Vud = encryptVud(finalUsername);
  const method = options.method.toUpperCase();

  // 基础请求头
  const baseHeaders = {
    'Authorization': 'Bearer ' + token
  };

  // 添加 Vud 到请求头，即使是微服务跳转的情况
  baseHeaders.Vud = Vud;

  if (['POST', 'PUT', 'PATCH'].indexOf(method) > -1) {
    options.data = data;
    options.headers = baseHeaders;
  } else if (['POST_FORM'].indexOf(method) > -1) {
    options.data = qs.stringify(data);
    if (options.url !== '/ics/account/login' && options.url !== '/ics/account/update-password-for-expired') {
      options.headers = {
        ...baseHeaders,
        'X-Requested-With': 'XMLHttpRequest',
        'content-type': 'application/x-www-form-urlencoded'
      };
    } else {
      options.headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'content-type': 'application/x-www-form-urlencoded',
        'Vud': Vud
      };
    }
    options.method = 'POST';
  } else if (['POST_TOKEN'].indexOf(method) > -1) {
    options.data = qs.stringify(data);
    options.method = 'post';
    options.headers = {
      'Authorization': `Basic ${process.env.VUE_APP_CLIENT_SECRET}`,
      'content-type': 'application/x-www-form-urlencoded',
      'Vud': Vud
    };
  } else if (['OTHER'].indexOf(method) > -1) {
    options.headers = baseHeaders;
    options.url += `/${Object.values(data).join('/')}`;
    options.method = 'post';
  } else if (['OTHERGET'].indexOf(method) > -1) {
    options.headers = baseHeaders;
    options.url += `/${Object.values(data).join('/')}`;
    options.method = 'get';
  } else if (['DELETE'].indexOf(method) > -1) {
    options.headers = baseHeaders;
    options.url += `/${Object.values(data).join('/')}`;
    options.method = 'post';
  } else {
    options.params = data;
    options.headers = baseHeaders;
  }
  return options;
}

export default new MakeApi({
  config: API_CONFIG,
  ...API_DEFAULT_CONFIG
}).api;
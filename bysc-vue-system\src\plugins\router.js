import Vue from 'vue';
import VueRouter from 'vue-router';
import ROUTES from '@/routes';
import {ROUTER_DEFAULT_CONFIG} from '@/config';
import {routerBeforeEachFunc, routerAfterEachFunc} from '@/config/interceptors';
const originalPush = VueRouter.prototype.push;

VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
Vue.use(VueRouter);
// const originalPush = ROUTES.prototype.push;
// ROUTES.prototype.push = function push(location) {
//   return originalPush.call(this, location).catch(err => err);
// };
export function createNewRoute() {
  return new VueRouter({
    ...ROUTER_DEFAULT_CONFIG,
    routes: ROUTES
  });
}

// 注入默认配置和路由表
// let routerInstance = new VueRouter({
//   ...ROUTER_DEFAULT_CONFIG,
//   routes: ROUTES
// })
export let routerInstance = createNewRoute();
// 注入拦截器
routerInstance.beforeEach(routerBeforeEachFunc);

routerInstance.afterEach(routerAfterEachFunc);
/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */
// routerInstance.onError(routerErrorEachFunc);
export default routerInstance;

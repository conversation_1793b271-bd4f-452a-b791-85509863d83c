import Main from '@/components/main/Main';
import systemRoutes from '@/bysc_system/routes/system';
import localCache from '@/utils/storage';
import _ from 'lodash';

let defaultRoutes = [
  // {
  //   name: 'homeindex',
  //   path: '/home',
  //   component: Main,
  //   redirect: '/home/<USER>',
  //   meta: {
  //     showInMenu: true,
  //     showAlways: true, // 表示一定要展示子菜单
  //     title: '首页',
  //     icon: 'ios-apps'
  //   },
  //   children: [
  //     {
  //       name: 'home',
  //       path: '/home/<USER>',
  //       // TODO 这里变更首页
  //       component: () => import('@/views/home/<USER>'),
  //       meta: {
  //         showInMenu: false,
  //         path: '/home/<USER>',
  //         title: '系统首页',
  //         icon: 'logo-apple'
  //       }
  //     }
  //   ]
  // }
];
export default permissions => {
  let allRoutes = _.cloneDeep(defaultRoutes);
  allRoutes = allRoutes.concat(systemRoutes); // 合并系统模块
  allRoutes = _.cloneDeep(allRoutes);
  // 可以根据菜单权限过滤路由
  if (allRoutes.length) {
    allRoutes.push({
      path: '*',
      redirect: allRoutes[0].path
    });
  } else {
    localCache.removeLocal('userToken');
    localCache.removeLocal('refreshToken');
    localCache.removeSession('username');
  }

  return allRoutes;
};

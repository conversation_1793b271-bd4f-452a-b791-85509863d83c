{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\main\\components\\header-bar\\index.js", "mtime": 1745205562761}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import HeaderBar from \"./header-bar\";\nexport default HeaderBar;", {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/components/main/components/header-bar/index.js"], "sourcesContent": ["import HeaderBar from './header-bar';\r\nexport default HeaderBar;\r\n"], "mappings": "AAAA,OAAOA,SAAS;AAChB,eAAeA,SAAS", "ignoreList": []}]}
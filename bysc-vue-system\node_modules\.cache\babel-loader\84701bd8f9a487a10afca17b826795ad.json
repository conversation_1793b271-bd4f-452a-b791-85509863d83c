{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\global\\module-config.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\global\\module-config.js", "mtime": 1745205562789}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["// TODO 修改项目配置：路由表和接口MAP\nimport ROUTERS_PERMISSION from '@/routes/permission-system';\nimport API_CONFIG from '@/service/api/index-system';\nexport { ROUTERS_PERMISSION, API_CONFIG };", {"version": 3, "names": ["ROUTERS_PERMISSION", "API_CONFIG"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/global/module-config.js"], "sourcesContent": ["// TODO 修改项目配置：路由表和接口MAP\r\nimport ROUTERS_PERMISSION from '@/routes/permission-system';\r\nimport API_CONFIG from '@/service/api/index-system';\r\nexport {\r\n  ROUTERS_PERMISSION,\r\n  API_CONFIG\r\n};\r\n"], "mappings": "AAAA;AACA,OAAOA,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SACED,kBAAkB,EAClBC,UAAU", "ignoreList": []}]}
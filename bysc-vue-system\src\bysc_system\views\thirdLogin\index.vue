<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 10:11:20
 * @FilePath: \kdsp_vue_clear\src\systemView\views\menus\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid api="thirdLogin/justauth-page" :event-bus="searchEventBus" :search-params="searchForm" :newcolumn="columns"
          @datas="getDatas" @columnChange="getcolumn" ref="grid">
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="名称">
                <el-select size="small" clearable v-model="searchForm.sourceName" placeholder="请选择名称">
                  <el-option
                    v-for="item in sourceNameLists"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="登录类型">
                <el-select size="small" clearable v-model="searchForm.sourceType" placeholder="请选择登录类型">
                  <el-option
                    v-for="item in sourceTypeLists"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'thirdLogin_add'" size="small" type="primary" @click="handleAdd">添加</el-button>
            <el-button v-permission="'thirdLogin_batchDel'" size="small" style="margin-left:10px" @click="batchDelete">批量删除</el-button>
          </div>
          <el-table ref="tables" slot="table" slot-scope="{ loading }" v-loading="loading" :data="tableData" stripe
            style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column v-if="item.slot" :show-overflow-tooltip="true" :align="item.align ? item.align : 'center'"
                :key="index" :prop="item.key" :label="item.title" min-width="180">
                <template slot-scope="scope">
                  <div v-if="item.slot=='status'">
                    {{scope.row[item.slot]==0?'禁用':'启用'}}
                  </div>
                  <div v-if="item.slot=='sourceType'">
                    {{scope.row[item.slot]=='default'?'默认':'自定义'}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-else :show-overflow-tooltip="true" :key="item.key" :prop="item.key" :label="item.title"
                :min-width="item.width ? item.width : '150'" :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="150">
              <template slot-scope="scope">
                <el-button v-permission="'thirdLogin_edit'" style="margin-right: 6px" @click="handleRdit(scope.row)" type="text"
                  size="small">编辑</el-button>
                <el-button
                  v-permission="'thirdLogin_status'"
                  @click="changeStatus(scope.row)"
                  type="text"
                  size="small"
                  style="margin-right: 6px"
                  >{{ scope.row.status==0?'启用':'禁用' }}</el-button>
                <template>
                  <el-popconfirm @confirm="handleDelete(scope.row.id)" title="您确定要删除该第三方列表吗？">
                    <el-button v-permission="'thirdLogin_del'" type="text" size="small" slot="reference">删除</el-button>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer size="30%" :title="drawerName" :visible.sync="drawer" :direction="direction" :wrapperClosable="false">
      <div style="width: 100%; padding: 0 10px">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
          <el-form-item label="登录类型" prop="sourceType">
            <el-select clearable style="width:100%" size="small" v-model="ruleForm.sourceType" placeholder="请选择登录类型">
                  <el-option
                    v-for="item in sourceTypeLists"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode">
                  </el-option>
                </el-select>
          </el-form-item>
          <el-form-item label="名称" prop="sourceName">
            <el-select clearable style="width:100%" size="small" v-model="ruleForm.sourceName" placeholder="请选择名称">
                  <el-option
                    v-for="item in sourceNameLists"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode">
                  </el-option>
                </el-select>
          </el-form-item>
          <el-form-item label="客户端id" prop="clientId">
            <el-input size="small" placeholder="请输入客户端id" minlength="2" maxlength="32"
              v-model.trim="ruleForm.clientId"></el-input>
          </el-form-item>
          <el-form-item label="客户端Secret" prop="clientSecret">
            <el-input size="small" placeholder="请输入客户端Secret" minlength="2" maxlength="32"
              v-model.trim="ruleForm.clientSecret"></el-input>
          </el-form-item>
          <el-form-item label="回调地址" prop="redirectUri">
            <el-input size="small" placeholder="请输入回调地址" maxlength="200"
              v-model.trim="ruleForm.redirectUri"></el-input>
          </el-form-item>
          <el-form-item v-if="ruleForm.sourceType == 'custom'" label="自定义Class" prop="requestClass">
            <el-input size="small" placeholder="请输入回调地址" maxlength="200"
              v-model.trim="ruleForm.requestClass"></el-input>
          </el-form-item>
          <el-form-item v-if="ruleForm.sourceType == 'custom'" label="自定义授权scope" prop="scopes">
            <el-input size="small" placeholder="请输入回调地址" maxlength="200"
              v-model.trim="ruleForm.scopes"></el-input>
          </el-form-item>
          <el-form-item label="设备ID" prop="deviceId">
            <el-input size="small" placeholder="请输入设备ID" maxlength="200"
              v-model.trim="ruleForm.deviceId"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="ruleForm.status">
              <el-radio :label="0">启用</el-radio>
              <el-radio :label="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input size="small" placeholder="请输入备注" maxlength="200" type="textarea"
              v-model.trim="ruleForm.remark"></el-input>
          </el-form-item>
          <el-form-item label="开启PKCE模式" prop="pkce">
            <el-switch
              v-model="ruleForm.pkce">
            </el-switch>
          </el-form-item>
          <el-form-item label="忽略校验codestate" prop="ignoreCheckState">
            <el-switch
              v-model="ruleForm.ignoreCheckState">
            </el-switch>
          </el-form-item>
          <el-form-item label="忽略校验RedirectUri" prop="ignoreCheckRedirectUri">
            <el-switch
              v-model="ruleForm.ignoreCheckRedirectUri">
            </el-switch>
          </el-form-item>
          <el-form-item label="Http代理类型" prop="proxyType">
            <el-select clearable size="small" style="width:100%" v-model="ruleForm.proxyType" placeholder="请选择Http代理类型">
              <el-option
                v-for="item in proxyTypeLists"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Http代理Host" prop="proxyHostName">
            <el-input size="small" placeholder="请输入Http代理Host" maxlength="200"
              v-model.trim="ruleForm.proxyHostName"></el-input>
          </el-form-item>
          <el-form-item label="Http代理Port" prop="proxyPort">
            <el-input size="small" placeholder="请输入Http代理Port" maxlength="200"
              v-model.trim="ruleForm.proxyPort"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button size="small" type="primary" @click="submitForm('ruleForm')">保存</el-button>
            <el-button size="small" @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from "vue";
import Grid from "@/components/Grid";
import _ from "lodash";
const defaultSearchForm = {
  sourceName: "",
  sourceType: "",
};
const defaultForm = {
  agentId: "",
  alipayPublicKey: "",
  authServerId: "",
  clientId: "",
  clientOsType: 0,
  clientSecret: "",
  createTime: "",
  creator: 0,
  delFlag: 0,
  deviceId: "",
  domainPrefix: "",
  ignoreCheckRedirectUri: true,
  ignoreCheckState: true,
  operator: 0,
  packId: "",
  pkce: true,
  proxyHostName: "",
  proxyPort: 0,
  proxyType: "",
  redirectUri: "",
  remark: "",
  requestClass: "",
  scopes: "",
  sourceName: "",
  sourceType: "",
  stackOverflowKey: "",
  status: 0,
  tenantId: 0,
  unionId: true,
  updateTime: "",
  userType: "",
};
export default {
  name: "thirdLogin",
  components: {Grid},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      dialogVisible: false,
      status: true,
      sourceTypeLists: [],
      sourceNameLists: [],
      proxyTypeLists: [],
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        sourceType: [
          {required: true, message: "请输入登录类型", trigger: "blur"},
        ],
        sourceName: [
          {required: true, message: "请输入名称", trigger: ",blur"}
        ],
        clientId: [
          {required: true, message: "请输入客户端id", trigger: ",blur"}
        ],
        clientSecret: [
          {required: true, message: "请输入客户端Secret", trigger: ",blur"}
        ],
        redirectUri: [
          {required: true, message: "请输入回调地址", trigger: ",blur"}
        ],
      },
      drawerName: "添加",
      drawer: false,
      direction: "rtl",
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: "名称",
          key: "sourceName",
          tooltip: true,
          minWidth: 100,
        },
        {
          title: "登录类型",
          key: "sourceType",
          slot: "sourceType",
          tooltip: true,
          minWidth: 80,
        },
        {
          title: "客户端id",
          key: "clientId",
          tooltip: true,
          minWidth: 180,
        },
        {
          title: "客户端Secret",
          key: "clientSecret",
          tooltip: true,
          minWidth: 180,
        },
        {
          title: "回调地址",
          key: "redirectUri",
          tooltip: true,
          minWidth: 180,
        },
        {
          title: "状态",
          slot: "status",
          key: "status",
          tooltip: true,
          minWidth: 100,
          render: (h, params) => {
            if (!params.row.status === 0) {
              return h('div', '禁用');
            }
            return h('div', '启用');
          }
        },
        {
          title: "备注",
          key: "remark",
          sortType: "desc",
          tooltip: true,
          minWidth: 170,
        },
      ],
      treedata: [],
      tableData: [],
      selectedResouce: {},
    };
  },
  mounted() {
    this.getParamDatas();
  },

  methods: {
    handleAdd() {
      this.drawer = true;
      this.drawerName = "添加";
      this.ruleForm = _.cloneDeep(defaultForm);
    },
    handleRdit(row) {
      this.ruleForm = Object.assign({}, row);
      this.drawer = true;
      this.drawerName = "编辑";
    },
    batchDelete() {
      let arr = [];
      this.$refs.tables.selection.forEach(e => {
        arr.push(e.id);
      });
      if (!arr.length) {
        this.$message.error('请选择要删除的项');
        return;
      }
      this.$alert('您确定要删除选中的项吗？', '删除', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            this.$api["thirdLogin/justauth-delete"]({ids: arr}).then(data => {
              this.$refs.grid.query();
              this.$message({
                message: "删除成功",
                type: 'success',
              });
            });
          }
        }
      });
    },
    changeStatus(e) {
      this.$alert('您确定要改变该项的状态吗？', '切换状态', {
        confirmButtonText: '确定',
        callback: action => {
          this.$api["thirdLogin/switch-justauth"]({id: e.id}).then(data => {
            this.$refs.grid.query();
            this.$message({
              message: "修改成功",
              type: 'success',
            });
          });
        }
      });
      console.log(e);
    },
    handleDelete(e) {
      this.$api["thirdLogin/justauth-delete"]({ids: [e]}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: "删除成功",
          type: 'success',
        });
      });
    },
    getSelectTreeNode(e) {
      this.selectedResouce = e;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // let isport = /^(?:[1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/.test(this.ruleForm.proxyPort);
          // if (!isport) {
          //   this.$message.error('请输入正确的Http代理Port');
          //   return;
          // }
          this.$api["thirdLogin/justauth-save"](this.ruleForm).then(data => {
            this.$message({
              message: "保存成功",
              type: 'success',
            });
            this.drawer = false;
            this.$refs.grid.query();
          });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.ruleForm = _.cloneDeep(defaultForm);
      this.$refs[formName].resetFields();
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.searchForm.parentId = null;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    getClickTreeNode(e) {
      this.searchForm.parentId = this.searchForm.parentId == e.id ? null : e.id;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    clearParent() {
      this.ruleForm.parentId = null;
      this.ruleForm.parentName = null;
    },
    getParamDatas() {
      this.$api['sysDict/getParam']({code: 'JUST_AUTH_TYPE'}).then(data => {
        this.sourceTypeLists = data;
      });
      this.$api['sysDict/getParam']({code: 'JUST_AUTH_SOURCE'}).then(data => {
        this.sourceNameLists = data;
      });
      this.$api['sysDict/getParam']({code: 'HTTP_PROXY_TYPE'}).then(data => {
        this.proxyTypeLists = data;
      });
    }
  },
};
</script>
<style lang="less" scoped></style>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\choose\\icon-choose.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\choose\\icon-choose.vue", "mtime": 1745205562744}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./icon-choose.vue?vue&type=template&id=11b832fa\"\nimport script from \"./icon-choose.vue?vue&type=script&lang=js\"\nexport * from \"./icon-choose.vue?vue&type=script&lang=js\"\nimport style0 from \"./icon-choose.vue?vue&type=style&index=0&id=11b832fa&lang=less\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('11b832fa')) {\n      api.createRecord('11b832fa', component.options)\n    } else {\n      api.reload('11b832fa', component.options)\n    }\n    module.hot.accept(\"./icon-choose.vue?vue&type=template&id=11b832fa\", function () {\n      api.rerender('11b832fa', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/choose/icon-choose.vue\"\nexport default component.exports"]}
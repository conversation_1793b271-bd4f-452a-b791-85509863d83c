import axios from 'axios';
import {AXIOS_DEFAULT_CONFIG} from '@/config';
import createAuthRefreshInterceptor from 'axios-auth-refresh';
import store from '@/plugins/store';
import api from '@/plugins/api';
import localCache from '@/utils/storage';
import {Message} from 'element-ui';
import {getCurrentVud} from '@/utils/vud';

// 避免其他接口同时请求
let isRefreshToken = false;

let timers = null;
let isUseing = false;


function requestSuccessFunc(requestObj) {
  // 判断当前是否租户模式，如果是就带上租户Id
  if (store.state.common.isTenantMode) {
    // 租户id后面由后端获取
    requestObj.headers.TenantId = 0;
  }

  // 确保所有请求都带上 Vud 请求头
  const vud = getCurrentVud();
  console.log(vud, 'vud');

  if (vud) {
    requestObj.headers.Vud = vud;
  }

  // 在发起请求前做一次拦截，当一个浏览器同时登录两个账号的时候前一个账号会被踢出
  if (window.location.pathname !== '/adminLogin' && window.location.pathname !== '/' && window.location.pathname !== '/errorPage' && window.location.pathname !== '/nofound') {
    if ((localCache.getLocal('nowUserName') != localCache.getSession('username')) && localCache.getSession('username')) {
      // localStorage.clear();
      location.reload();
    }
  }
  return requestObj;
}

function requestFailFunc(requestError) {
  // 发送请求失败处理

  return Promise.reject(requestError);
}

// 响应成功
function responseSuccessFunc(responseObj) {
  if (!responseObj || !responseObj.data) {
    return responseObj;
  }
  if (responseObj.data.code != 'S00000') {
    Message.error(responseObj.data.msg);
    return Promise.reject(responseObj.data.msg);
  }
  return responseObj.data.data;
}
const sleep = (delaytime = 1000) => {
  return new Promise(resolve => setTimeout(resolve, delaytime));
};

// 本文件是实际使用的axios
function responseFailFunc(responseError) {
  if (responseError.response) {
    switch (responseError.response.status) {
      // case 403:
      //   location.reload()
      //   break
      case 201:
      case 202:
        break;
      case 400:
        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '参数错误');
        break;
      case 401:
        console.log(responseError.response, '401');
        if (responseError.response.config.url == '/api/oauth/oauth/token' || responseError.response.config.url == '/api/system/account/user/info') {
          toLogin();
          return;
        }

        return sleep(1000).then(() => {
          if (isRefreshToken) {
            responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');
            return axios(responseError.response.config).then(res => res.data.data);
          }
          return sleep(2000).then(() => {
            if (isRefreshToken) {
              responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');
              return axios(responseError.response.config).then(res => res.data.data);
            }
            return sleep(4000).then(() => {
              if (isRefreshToken) {
                responseError.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');
                return axios(responseError.response.config).then(res => res.data.data);
              }
            });
          });
        });
      case 403:
        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '暂无权限');
        // toLogin();
        alert('您无该接口权限');
        window.history.go(-1);
        throw '无权限';
      case 404:
        Message({
          message: responseError.response.data.msg ? responseError.response.data.msg : '接口错误',
          type: 'error'
        });
        break;
      case 503:
        Message.error(responseError.response.data.msg ? responseError.response.data.msg : '服务挂了');
        throw '服务器异常';
      case 500:
        Message({
          message: responseError.response.data.msg ? responseError.response.data.msg : '服务错误',
          type: 'error'
        });
        break;
      default:
    }
  } else {
    Message.error('接口请求失败，请刷新重试');
    // localStorage.clear();
    // MessageBox.alert('登录超时，请重新登录！', '登录超时', {
    //   confirmButtonText: '确定',
    //   callback: action => {
    //     toLogin();
    //   }
    // });
  }
  // 自定义错误码，弹窗显示错误内容
  return Promise.reject(responseError);
}

/**
 * 跳转登录页面
 */
function toLogin() {
  if (!isUseing) {
    isUseing = true;
    alert('登录超时', "提示");
    timers = setTimeout(() => {
      isUseing = false;
      clearTimeout(timers);
    }, 3000);
    localStorage.getItem('loginPageUrl') && (location.href = localStorage.getItem('loginPageUrl'));
    if (!localStorage.getItem('loginPageUrl')) {
      window.close();
    }
  }
  // if (localStorage.getItem('userToken')) {
  //   store.dispatch('handleLogOut');
  // }
  // localCache.removeLocal('userToken');
  // localCache.removeLocal('refreshToken');
  // sessionStorage.removeItem(process.env.VUE_APP_LOCAL_PREFIX + 'username');
  // location.href = '/adminLogin';
}

let axiosInstance = axios.create(AXIOS_DEFAULT_CONFIG);
// 当token失效时，需要调用的刷新token的方法
const refreshAuthLogic = failedRequest => api['account/getToken']({grant_type: 'refresh_token', refresh_token: $cookies.get('refreshToken')}).then(async data => {
  localCache.setLocal('userToken', data.token);
  localCache.setLocal('refreshToken', data.refreshToken);
  localCache.setLocal('userExpir', data.exp);
  isRefreshToken = true;
  setTimeout(() => {
    isRefreshToken = false;
  }, 30000);
  failedRequest.response.config.headers.Authorization = 'Bearer ' + $cookies.get('userToken');
  return Promise.resolve();
}).catch(e => {
  toLogin();
});

// 初始化刷新token拦截器;
createAuthRefreshInterceptor(axiosInstance, refreshAuthLogic, {pauseInstanceWhileRefreshing: true});
// 注入请求拦截
axiosInstance
  .interceptors.request.use(requestSuccessFunc, requestFailFunc);
// 注入失败拦截
axiosInstance
  .interceptors.response.use(responseSuccessFunc, responseFailFunc);

export default axiosInstance;

<template>
  <div
    style="width:100vw;height:100vh"
    v-loading="loading"
    element-loading-text="页面加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.5)">
    <dv-loading> </dv-loading>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      code: '',
      socialType: ''
    };
  },
  created() {
    if (this.$route.query.tk) {
      this.$localCache.setLocal('userToken', this.$route.query.tk);
      this.$store.dispatch('checkToken', this.$route.query.path);
      return false;
    }
    let {code, stype} = this.$route.query;
    this.code = code;
    this.socialType = stype.replace(/\//g, '');
    this.socialcallback();
  },
  methods: {
    // 第三方登录后回调
    socialLogin(key) {
      // 执行登录操作
      const loginParams = {
        grant_type: 'social',
        social_key: key
      };
      console.log('进来了吗', loginParams);
      this.$store.dispatch('maxkeylogin', loginParams).then(data => {
        // console.log(data, '=========','这是哈哈哈哈');
      }).catch(err => {
        // window.close()
      });

    },
    socialcallback() {
      this.$api['account/socialcallback']({code: this.code, socialType: this.socialType}).then(data => {
        const bindResult = data;
        if (bindResult && bindResult !== '') {
          if (bindResult.bound) {
            this.socialLogin(bindResult.key);
          }
        } else {
          console.log('示获取第三方登录失败');
          // 提示获取第三方登录失败
          // this.$notification['error']({
          //   message: '错误',
          //   description: '第三方登录失败，请稍后再试',
          //   duration: 4
          // })
        }
      }).catch(err => {
        setTimeout(() => {
          // window.close()
        }, 1000);
      });
    }
  }
};
</script>

<style lang="less" scoped>

</style>
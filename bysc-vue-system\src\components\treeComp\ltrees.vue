<template>
  <div style="position:relative;height:480px;width:100%;overflow-y:scroll">
    <div style="position:absolute;top:0px;z-index:999;width:100%">
      <el-input
        size="small"
        style="width: 100%;margin-bottom:8px"
        placeholder="请输入关键字搜索"
        v-model="orgName"
        @input="onSearch"
        @clear="onSearch"
        clearable
      >
      </el-input>
    </div>
    <div style="padding-top:45px;height:460px;overflow-y:scroll">
      <el-tree
      :props="props"
      :data="deptAndUserData"
      :show-checkbox="showCheckbox"
      :load="loadNode"
      lazy
      @node-click="handleNodeClick"
      @check="getTreeDatas"
      :highlight-current="highlightCurrent"
      node-key="primaryCode"
      :key="setTimer"
      :check-strictly="false"
      ref="tree"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>
          <i :class="getIcon(data.primaryCode)"></i>
          <span style="margin-left: 2px">{{ node.label }}</span>
        </span>
      </span>
    </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      props: {
        label: "label",
        children: "zones",
        isLeaf: "isLeaf",
      },
      orgName: "",
      treeData: [],
      selectedNode: null,
      deptAndUserData: [],
      nodes: null,
      resolves: null,
      setTimer: null,
      timer: null,
      selectedNodes: [],
      selectedUserList: []
    };
  },
  props: {
    highlightCurrent: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showCheckbox: {
      type: Boolean,
      default() {
        return true;
      },
    },
  },
  mounted() {
    this.setTimer = new Date().getTime();
  },
  methods: {
    setCheckedKeys(ids) {
      this.$refs.tree.setCheckedKeys(ids);
    },
    // 搜索树节点
    onSearch() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (this.orgName.length) {
          this.$api["systems/lazyorgusertree"]({orgName: this.orgName}).then(
            res => {
              this.deptAndUserData = res;
              console.log(res, '========');
              if (this.showCheckbox) {
                setTimeout(() => {
                  console.log('已经选的', this.selectedNodes);
                  this.selectedNodes.length && this.$refs.tree.setCheckedNodes(this.selectedNodes);
                }, 500);
              }
              // else{
              //   this.$refs.tree.setCurrentKey(this.selectedNode);
              // }
            }
          );
        } else {
          this.setTimer = new Date().getTime();
          this.deptAndUserData = [];
          this.nodes.data.primaryId = 0;
          this.loadNode(this.nodes, this.resolves);
        }
      }, 500);
    },
    getIcon(primaryCode) {
      let isdept = primaryCode.indexOf("org");
      if (isdept == -1) {
        return "el-icon-user";
      }
      return "el-icon-tickets";
    },
    getTreeDatas(checked, data) {
      this.selectedNodes = data.checkedNodes.filter(e => {
        return e.primaryCode.indexOf('user') !== -1;
      });
      this.$emit("treeNode", this.selectedNodes);
    },
    handleNodeClick(data) {
      if (this.showCheckbox) {
        return;
      }
      console.log(data, "单选点击节点");
      if (data.primaryCode.indexOf("org") !== -1) {
        setTimeout(() => {
          this.$refs.tree.setCurrentKey(this.selectedNode);
        }, 0);
      } else {
        this.selectedNodes = [data];
        this.selectedNode = data.primaryCode;
        this.$emit("treeNode", data);
      }
    },
    loadNode(node, resolve) {
      this.nodes = node;
      this.resolves = resolve;
      this.$api["systems/lazyorgusertree"]({
        parentId: node.data ? node.data.primaryId : 0
      }).then(res => {
        // console.log(res, '这是获取到的子节点');
        resolve(res);
        if (this.showCheckbox) {
          setTimeout(() => {
            let ids = [];
            this.selectedNodes.forEach(e => {
              ids.push(e.primaryCode);
            });
            // console.log(this.selectedNodes, ids, '多选回显');
            this.$refs.tree.setCheckedKeys(ids);
            // this.$refs.tree.setCheckedNodes(this.selectedNodes);
          }, 100);
        } else {
          // console.log(this.selectedNode, '单选回显');
          setTimeout(() => {
            this.$refs.tree.setCurrentKey(this.selectedNode);
          }, 0);
        }
      });
    },
  },
};
</script>
//@import '~iview/src/styles/index.less';
// 升级4.0.0
.classic(){
  .header-con {
    background-color: #fff !important;
  }
  //@menu-dark-title: #001529;
  //@menu-dark-active-bg: #000c17;
  //@layout-sider-background: #001529;
  /*
  menu样式
   */
   .ivu-menu{
    z-index: 300!important;
  }
  .el-message {
    z-index: 9999999999!important;
  }
   .demo-drawer-footer{
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
}
  .ivu-layout-content{
    background-color: #fff;
  }
  .ivu-modal-close{
    background-color: #2D8CF0!important;
    right: -13px!important;
    top: -13px!important;
    .ivu-icon-ios-close{
      color: #fff;
    }
  }
  .ivu-drawer-close{
    background-color: #2D8CF0!important;
    right: 100%!important;
    top: 10%!important;
    .ivu-icon-ios-close {
      color: #fff !important;
    }
  }
  // .ivu-menu .ivu-menu-dark.ivu-menu-vertical .ivu-submenu .ivu-menu-item:hover{
  //   background: #2b85e4 !important;
  // }
  // .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover{
  //   background: #2b85e4 !important;
  // }
  // .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover{
  //   background: #001D30 !important;
  // }
  // .ivu-layout-sider{
  //   background: #fff !important;
  // }
  // .ivu-menu-dark{
  //   background: #001529 !important;
  // }
  // .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
  //   background: #001529 !important;
  // }
  // .side-menu-wrapper .ivu-menu-item{
  //   background: #fff;
  // }
  .iconActive{
    color: #2b85e4 !important;
  }
  .loginBg{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
    background: url("../../assets/whiteLoginBg.png");
    background-size: 100% 100%;
  }
  .loginBg .ivu-input:hover{
    border:2px solid rgba(0,192,255,1);
    background: rgba(0,0,0,0);
  }
  .loginBg .ivu-input{
    border:1px solid #0056AE;
    background: rgba(0,0,0,0);
    color: #FFFFFF;
  }
  .loginBg .login-btn{
    background:linear-gradient(0deg,rgba(0,96,255,1),rgba(0,150,255,1));
    border-radius:6px;
  }
  .systemTitle h1{
    color:rgba(255,255,255,1);
  }
  // .ivu-notice-notice{
  //   margin-top: 120% !important;
  // }
  .ivu-table th{
    background-color: #CFD7E1!important;
  }
}
// Here are the variables to cover, such as:
// @primary-color:#8c0776;
//@background-color:#000000;

// Base
/*@body-background        : #001529;
@component-background   : #001529;
@input-bg                    : #001529;

@body-background        : #001529;
@component-background   : #001529;

@layout-body-background      : #001529;

@head-bg                      : #001529;
@table-thead-bg               : #001529;
@table-td-stripe-bg           : #001529;*/

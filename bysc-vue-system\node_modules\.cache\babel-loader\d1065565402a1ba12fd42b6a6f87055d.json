{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\api\\index-system.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\api\\index-system.js", "mtime": 1745205562803}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import systemApiMap from '@/bysc_system/service/api/index';\nvar allApiMap = {}; // 定义一个API接口总集\nObject.assign(allApiMap, systemApiMap); // 合并system模块\nexport default allApiMap;", {"version": 3, "names": ["systemApiMap", "allApiMap", "Object", "assign"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/api/index-system.js"], "sourcesContent": ["import systemApiMap from '@/bysc_system/service/api/index';\r\nlet allApiMap = {}; // 定义一个API接口总集\r\nObject.assign(allApiMap, systemApiMap); // 合并system模块\r\nexport default allApiMap;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iCAAiC;AAC1D,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACpBC,MAAM,CAACC,MAAM,CAACF,SAAS,EAAED,YAAY,CAAC,CAAC,CAAC;AACxC,eAAeC,SAAS", "ignoreList": []}]}
<!--模板-->
<template>
  <div id="app">
    <div v-if="loadings" id="Loading">
      <div class="loader-inner ball-beat">
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <router-view v-if="isRouterAlive" />
    <!-- <keep-alive include="keepAlive">
      <router-view v-if="$router.meta.keepAlive"></router-view>
    </keep-alive>
    <router-view v-if="!$router.meta.keepAlive"></router-view> -->
  </div>
</template>

<!--js-->
<script>
export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload,
      showLoading: this.showLoadings,
      hideLoading: this.hideLoadings,
    };
  },
  data() {
    return {
      isRouterAlive: true,
      loadings: false,
    };
  },
  methods: {
    attachEvents() {
      global.vbus.$on('global.$Message.show', msg => {
        this.$message.error({
          message: msg,
          duration: 4,
        });
      });
    },
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    showLoadings() {
      this.loadings = true;
    },
    hideLoadings() {
      this.loadings = false;
    },

  },
  created() {
    this.attachEvents();
    this.$localCache.setLocal('app', 'classic');
    // this.$store.dispatch('updateUserInfo')
  },
  mounted() {
    document.body.className = this.$localCache.getLocal('app');
    setTimeout(() => {}, 0);
    if (this.$localCache.getLocal('app') === null) {
      document.body.className = 'classic';
    }
  },
};
</script>

<!--样式-->
<style lang="less">
@import "./static/css/app.less";
body {
  touch-action: none;
  font-family: Syht !important;
  font-style: normal;
  font-weight: 400;
}
#Loading {
  top: 50%;
  left: 50%;
  position: absolute;
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  z-index: 100;
}
@-webkit-keyframes ball-beat {
  50% {
    opacity: 0.2;
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes ball-beat {
  50% {
    opacity: 0.2;
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.ball-beat > div {
  background-color: #5d9bff;
  width: 15px;
  height: 15px;
  border-radius: 100% !important;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block;
  -webkit-animation: ball-beat 0.7s 0s infinite linear;
  animation: ball-beat 0.7s 0s infinite linear;
}
.ball-beat > div:nth-child(2n-1) {
  -webkit-animation-delay: 0.35s !important;
  animation-delay: 0.35s !important;
}
</style>

{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue?vue&type=template&id=2b551c4b&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\treeComp\\commonTree.vue", "mtime": 1753782569058}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n  <div>\n    <el-input\n  placeholder=\"输入关键字进行过滤\"\n  v-model=\"filterText\"\n  size=\"small\" style=\"width:90%;margin-bottom: 10px;\">\n</el-input>\n    <el-tree\n      :default-expand-all=\"defaultexpandall\"\n      :data=\"treeData\"\n      :props=\"treeProps\"\n      :highlight-current=\"highlightcurrent\"\n      accordion\n      @node-click=\"handleNodeClick\"\n      :filter-node-method=\"filterNode\"\n      ref=\"tree\"\n      node-key=\"id\"\n    >\n    </el-tree>\n  </div>\n", null]}
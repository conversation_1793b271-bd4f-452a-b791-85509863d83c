{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\config.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'baseSystemSetting-get',\n  // 查询系统设置表详情\n  method: 'OTHERGET',\n  path: '/base/baseSystemSetting/get'\n}, {\n  name: 'baseSystemSetting-save',\n  // 保存系统设置表\n  method: 'POST',\n  path: '/base/baseSystemSetting/save'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/config.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'baseSystemSetting-get', // 查询系统设置表详情\r\n    method: 'OTHERGET',\r\n    path: '/base/baseSystemSetting/get'\r\n  },\r\n  {\r\n    name: 'baseSystemSetting-save', // 保存系统设置表\r\n    method: 'POST',\r\n    path: '/base/baseSystemSetting/save'\r\n  },\r\n];"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,uBAAuB;EAAE;EAC/BC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
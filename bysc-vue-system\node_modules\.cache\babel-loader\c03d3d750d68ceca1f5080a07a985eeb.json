{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\permission.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\directives\\permission.js", "mtime": 1753782634053}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1745221301271}], "contextDependencies": [], "result": ["import localCache from '@/utils/storage';\nfunction checkArray(key) {\n  var arr = localCache.getLocal('btnPermissions') || [];\n  var index = arr.indexOf(key);\n  if (index > -1) {\n    return true; // 有权限\n  }\n  return false; // 无权限\n}\nvar permission = {\n  inserted: function inserted(el, binding) {\n    var permission = binding.value; // 获取到 v-permission的值\n    if (permission) {\n      var hasPermission = checkArray(permission);\n      if (!hasPermission) {\n        // 没有权限 移除Dom元素\n        el.parentNode && el.parentNode.removeChild(el);\n        //将移除dom改为隐藏dom\n        // if (el.parentNode) {\n        //   el.style.visibility= \"hidden\"\n        // }\n      }\n    }\n  }\n};\nexport default permission;", {"version": 3, "names": ["localCache", "checkArray", "key", "arr", "getLocal", "index", "indexOf", "permission", "inserted", "el", "binding", "value", "hasPermission", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/directives/permission.js"], "sourcesContent": ["import localCache from '@/utils/storage';\r\nfunction checkArray(key) {\r\n  let arr = localCache.getLocal('btnPermissions') || [];\r\n  let index = arr.indexOf(key);\r\n  if (index > -1) {\r\n    return true; // 有权限\r\n  }\r\n  return false; // 无权限\r\n\r\n}\r\nconst permission = {\r\n  inserted: function (el, binding) {\r\n    let permission = binding.value; // 获取到 v-permission的值\r\n    if (permission) {\r\n      let hasPermission = checkArray(permission);\r\n      if (!hasPermission) {\r\n        // 没有权限 移除Dom元素\r\n        el.parentNode && el.parentNode.removeChild(el);\r\n        //将移除dom改为隐藏dom\r\n        // if (el.parentNode) {\r\n        //   el.style.visibility= \"hidden\"\r\n        // }\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\nexport default permission;\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,SAASC,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,GAAG,GAAGH,UAAU,CAACI,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE;EACrD,IAAIC,KAAK,GAAGF,GAAG,CAACG,OAAO,CAACJ,GAAG,CAAC;EAC5B,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;IACd,OAAO,IAAI,CAAC,CAAC;EACf;EACA,OAAO,KAAK,CAAC,CAAC;AAEhB;AACA,IAAME,UAAU,GAAG;EACjBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,EAAE,EAAEC,OAAO,EAAE;IAC/B,IAAIH,UAAU,GAAGG,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC,IAAIJ,UAAU,EAAE;MACd,IAAIK,aAAa,GAAGX,UAAU,CAACM,UAAU,CAAC;MAC1C,IAAI,CAACK,aAAa,EAAE;QAClB;QACAH,EAAE,CAACI,UAAU,IAAIJ,EAAE,CAACI,UAAU,CAACC,WAAW,CAACL,EAAE,CAAC;QAC9C;QACA;QACA;QACA;MACF;IACF;EACF;AACF,CAAC;AAED,eAAeF,UAAU", "ignoreList": []}]}
<!--
 * @Author: czw
 * @Date: 2022-11-04 09:34:34
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 09:54:34
 * @FilePath: \kdsp_vue_clear\src\components\treeComp\commonTree.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-input
  placeholder="输入关键字进行过滤"
  v-model="filterText"
  size="small" style="width:90%;margin-bottom: 10px;">
</el-input>
    <el-tree
      :default-expand-all="defaultexpandall"
      :data="treeData"
      :props="treeProps"
      :highlight-current="highlightcurrent"
      accordion
      @node-click="handleNodeClick"
      :filter-node-method="filterNode"
      ref="tree"
      node-key="id"
    >
    </el-tree>
  </div>
</template>
<script>
export default {
  name: 'commonTree',
  props: {
    treeData: {
      type: Array,
      default() {
        return [];
      },
    },
    highlightcurrent: {
      type: Boolean,
      default() {
        return true;
      },
    },
    defaultexpandall: {
      type: Boolean,
      default() {
        return false;
      },
    },
    treeProps: {
      type: Object,
      default() {
        return {
          children: 'children',
          label: 'label',
        };
      },
    },
  },
  data() {
    return {
      filterText: '',
      selectedNode:null,
    };
  },
  mounted(){
    this.selectedNode = null
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data[this.treeProps.label].indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      if(data.id===this.selectedNode){
        this.$refs.tree.setCurrentKey(null);
      }else{
        this.selectedNode = data.id
      }
      this.$emit('treeNode', data);
    },
  },
};
</script>
<style lang="less" scoped>
</style>

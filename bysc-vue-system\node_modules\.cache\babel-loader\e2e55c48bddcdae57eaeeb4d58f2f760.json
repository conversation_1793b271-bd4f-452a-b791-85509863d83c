{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\actions.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\service\\store\\common\\actions.js", "mtime": 1745205562806}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.symbol.async-iterator\";\nimport \"core-js/modules/es6.symbol\";\nimport \"core-js/modules/es6.string.iterator\";\nimport \"core-js/modules/es6.array.from\";\nimport \"core-js/modules/es6.function.name\";\nimport _Loading2 from \"element-ui/lib/loading\";\nimport _Loading from \"element-ui/lib/loading\";\nimport \"core-js/modules/es6.array.find-index\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.regexp.search\";\nimport _toConsumableArray from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.split\";\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\n/* eslint-disable max-nested-callbacks */\nimport api from '@/plugins/api';\nimport router from '@/plugins/router';\n// eslint-disable-next-line no-duplicate-imports\nimport { routerInstance, createNewRoute } from '@/plugins/router';\n// import permission from '@/routes/permission-parking'\n// 引入系统路由表\nimport { ROUTERS_PERMISSION } from '@/global/module-config';\n// import qs from 'qs'\n// import axios from 'axios'\nimport md5 from 'js-md5';\nimport _ from 'lodash';\nimport axios from 'axios';\nimport store from '@/plugins/store';\nimport jsFileDownLoad from 'js-file-download';\nimport localCache from '@/utils/storage';\nimport CryptoJS from 'crypto-js';\nvar flags = true;\n\n// 解密函数 - 用于解密密钥\nfunction decryptKey(encryptedKey) {\n  try {\n    return atob(encryptedKey);\n  } catch (error) {\n    console.error('Key decryption failed:', error);\n    return '';\n  }\n}\n\n// 加密函数\nfunction encryptVud(username) {\n  var timestamp = new Date().getTime();\n  var random = Math.floor(Math.random() * 1000000); // 添加6位随机数\n  var str = \"\".concat(username, \"+\").concat(timestamp).concat(random);\n\n  // 使用正确的加密后的key\n  var encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\n  var cryptoKey = decryptKey(encryptedKey);\n  try {\n    var key = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var iv = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var strUtf8 = CryptoJS.enc.Utf8.parse(str);\n    var encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\n      iv: iv,\n      mode: CryptoJS.mode.CBC,\n      padding: CryptoJS.pad.Pkcs7\n    });\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\n  } catch (error) {\n    console.error('Vud encryption failed:', error);\n    return '';\n  }\n}\n\n// 添加 JWT token 解析函数\nfunction parseJwt(token) {\n  try {\n    if (!token) {\n      return null;\n    }\n    var base64Url = token.split('.')[1];\n    var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    var jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Token parsing failed:', error);\n    return null;\n  }\n}\nexport default {\n  login: function login(_ref, loginInfo) {\n    var commit = _ref.commit;\n    var _flattenTree = function flattenTree(tree) {\n      var result = [];\n      var _iterator = _createForOfIteratorHelper(tree),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var node = _step.value;\n          result.push(node);\n          if (node.children) {\n            result.push.apply(result, _toConsumableArray(_flattenTree(node.children)));\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      return result;\n    };\n    commit('clearStore');\n    var data = {};\n    if (loginInfo) {\n      data = {\n        username: loginInfo.username,\n        password: md5(loginInfo.password),\n        grant_type: 'password',\n        fromPage: 'noDesktop'\n      };\n    }\n    var username = localCache.getLocal('nowUserName');\n    if (!username) {\n      username = loginInfo.username;\n    }\n    // if (location.pathname === '/social/max_key/callback') {\n    //   return;\n    // }\n    var stype = location.pathname.split('/')[2];\n    var code = location.search.split('=')[1];\n    var path = location.pathname.split('/')[1];\n    if (path === 'social') {\n      console.log(path, '----------', location);\n      if (!flags) {\n        return;\n      }\n      flags = false;\n      api['account/socialcallback']({\n        code: code,\n        socialType: stype\n      }).then(function (data) {\n        var bindResult = data;\n        if (bindResult && bindResult !== '') {\n          if (bindResult.bound) {\n            var loginParams = {\n              grant_type: 'social',\n              social_key: bindResult.key\n            };\n            var _promise = api['account/getToken'](loginParams).then(function (data) {\n              localCache.setLocal('userToken', data.token);\n              localCache.setLocal('refreshToken', data.refreshToken);\n              localCache.setLocal('userExpir', data.exp);\n              api['account/getUsers']().then(function (e) {\n                var btnarr = [];\n                e.buttonList.forEach(function (per) {\n                  btnarr.push(per.resourcePageName);\n                });\n                localCache.setLocal('btnPermissions', btnarr);\n                localCache.setLocal('nowUserName', e.account);\n                localCache.setSession('username', e.account); // 存储用户信息\n                commit('setPermissionList', _.cloneDeep(e.permissions));\n                commit('setUserInfo', e);\n                var newpermissions = [];\n                if (e.menuTree) {\n                  newpermissions = _flattenTree(e.menuTree);\n                }\n                commit('setPermissions', newpermissions);\n                // route升级，讲vue2的addRoutes换为addRoute\n                ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach(function (e, index) {\n                  routerInstance.addRoute(index, e);\n                });\n                // routerInstance.addRoutes(routes)\n                setTimeout(function () {\n                  var index = e.menuTree.findIndex(function (r) {\n                    return r.resourcePageName === 'system/cfg';\n                  });\n                  if (e.menuTree[index].children[0].children) {\n                    if (!e.menuTree[index].children[0].children[0].children) {\n                      router.push({\n                        path: e.menuTree[index].children[0].children[0].resourcePath\n                      });\n                    }\n                  } else {\n                    router.push({\n                      path: e.menuTree[index].children[0].resourcePath\n                    });\n                  }\n                }, 200);\n                commit('setMenuList', e.menuTree);\n              });\n            });\n            return _promise;\n          }\n        }\n      });\n      throw '';\n      // return promises;\n    }\n    var promise = loginInfo ? api['account/getToken'](data) : api['account/getUsers']();\n    promise.then(function (data) {\n      if (loginInfo) {\n        loginInfo && localCache.setLocal('userToken', data.token);\n        loginInfo && localCache.setLocal('refreshToken', data.refreshToken);\n        localCache.setLocal('userExpir', data.exp);\n        api['account/getUsers']().then(function (e) {\n          var btnarr = [];\n          e.buttonList.forEach(function (per) {\n            btnarr.push(per.resourcePageName);\n          });\n          localCache.setLocal('btnPermissions', btnarr);\n          localCache.setLocal('nowUserName', e.account);\n          localCache.setSession('username', e.account); // 存储用户信息\n          commit('setPermissionList', _.cloneDeep(e.permissions));\n          commit('setUserInfo', e);\n          var newpermissions = [];\n          if (e.menuTree) {\n            newpermissions = _flattenTree(e.menuTree);\n          }\n          commit('setPermissions', newpermissions);\n          // route升级，讲vue2的addRoutes换为addRoute\n          ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach(function (e, index) {\n            routerInstance.addRoute(index, e);\n          });\n          // routerInstance.addRoutes(routes)\n          setTimeout(function () {\n            var index = e.menuTree.findIndex(function (r) {\n              return r.resourcePageName === 'system/cfg';\n            });\n            if (e.menuTree[index].children[0].children) {\n              if (!e.menuTree[index].children[0].children[0].children) {\n                router.push({\n                  path: e.menuTree[index].children[0].children[0].resourcePath\n                });\n              }\n            } else {\n              router.push({\n                path: e.menuTree[index].children[0].resourcePath\n              });\n            }\n          }, 200);\n          commit('setMenuList', e.menuTree);\n        });\n      } else {\n        if (!data) {\n          return;\n        }\n        var btnarr = [];\n        data.buttonList.forEach(function (per) {\n          btnarr.push(per.resourcePageName);\n        });\n        localCache.setLocal('btnPermissions', btnarr);\n        localCache.setLocal('nowUserName', data.account);\n        localCache.setSession('username', data.account); // 存储用户信息\n        commit('setPermissionList', _.cloneDeep(data.permissions));\n        commit('setUserInfo', data);\n        var newpermissions = [];\n        if (data.menuTree) {\n          data.menuTree.forEach(function (element) {\n            newpermissions.push(element);\n            if (element.children) {\n              element.children.forEach(function (k) {\n                newpermissions.push(k);\n                if (k.children) {\n                  k.children.forEach(function (i) {\n                    newpermissions.push(i);\n                    if (i.children) {\n                      i.children.forEach(function (j) {\n                        newpermissions.push(j);\n                      });\n                    }\n                  });\n                }\n              });\n            }\n          });\n        }\n        // route升级，讲vue2的addRoutes换为addRoute\n        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach(function (e, index) {\n          routerInstance.addRoute(index, e);\n        });\n        // routerInstance.addRoutes(routes)\n        commit('setPermissions', newpermissions);\n        commit('setMenuList', data.menuTree);\n      }\n    });\n    return promise;\n  },\n  checkToken: function checkToken(_ref2, path) {\n    var commit = _ref2.commit;\n    var _flattenTree2 = function flattenTree(tree) {\n      var result = [];\n      var _iterator2 = _createForOfIteratorHelper(tree),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var node = _step2.value;\n          result.push(node);\n          if (node.children) {\n            result.push.apply(result, _toConsumableArray(_flattenTree2(node.children)));\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n      return result;\n    };\n    api['account/getUsers']().then(function (e) {\n      var btnarr = [];\n      e.buttonList.forEach(function (per) {\n        btnarr.push(per.resourcePageName);\n      });\n      localCache.setLocal('btnPermissions', btnarr);\n      localCache.setLocal('nowUserName', e.account);\n      localCache.setSession('username', e.account); // 存储用户信息\n      commit('setPermissionList', _.cloneDeep(e.permissions));\n      commit('setUserInfo', e);\n      var newpermissions = [];\n      if (e.menuTree) {\n        newpermissions = _flattenTree2(e.menuTree);\n      }\n      commit('setPermissions', newpermissions);\n      // route升级，讲vue2的addRoutes换为addRoute\n      ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach(function (e, index) {\n        routerInstance.addRoute(index, e);\n      });\n      // routerInstance.addRoutes(routes)\n      setTimeout(function () {\n        var index = e.menuTree.findIndex(function (r) {\n          return r.resourcePageName === 'system/cfg';\n        });\n        if (e.menuTree[index].children[0].children) {\n          if (!e.menuTree[index].children[0].children[0].children) {\n            router.push({\n              path: e.menuTree[index].children[0].children[0].resourcePath\n            });\n          }\n        } else {\n          router.push({\n            path: e.menuTree[index].children[0].resourcePath\n          });\n        }\n      }, 200);\n      // setTimeout(() => {\n      //   router.push({\n      //     path: path\n      //   });\n      // }, 200);\n\n      commit('setMenuList', e.menuTree);\n    });\n  },\n  maxkeylogin: function maxkeylogin(_ref3, loginInfo) {\n    var commit = _ref3.commit;\n    var _flattenTree3 = function flattenTree(tree) {\n      var result = [];\n      var _iterator3 = _createForOfIteratorHelper(tree),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var node = _step3.value;\n          result.push(node);\n          if (node.children) {\n            result.push.apply(result, _toConsumableArray(_flattenTree3(node.children)));\n          }\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      return result;\n    };\n    api['account/getToken'](loginInfo).then(function (data) {\n      localCache.setLocal('userToken', data.token);\n      localCache.setLocal('refreshToken', data.refreshToken);\n      localCache.setLocal('userExpir', data.exp);\n      api['account/getUsers']().then(function (e) {\n        var btnarr = [];\n        e.buttonList.forEach(function (per) {\n          btnarr.push(per.resourcePageName);\n        });\n        localCache.setLocal('btnPermissions', btnarr);\n        localCache.setLocal('nowUserName', e.account);\n        localCache.setSession('username', e.account); // 存储用户信息\n        commit('setPermissionList', _.cloneDeep(e.permissions));\n        commit('setUserInfo', e);\n        var newpermissions = [];\n        if (e.menuTree) {\n          newpermissions = _flattenTree3(e.menuTree);\n        }\n        commit('setPermissions', newpermissions);\n        // route升级，讲vue2的addRoutes换为addRoute\n        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach(function (e, index) {\n          routerInstance.addRoute(index, e);\n        });\n        // routerInstance.addRoutes(routes)\n        setTimeout(function () {\n          var index = e.menuTree.findIndex(function (r) {\n            return r.resourcePageName === 'system/cfg';\n          });\n          if (e.menuTree[index].children[0].children) {\n            if (!e.menuTree[index].children[0].children[0].children) {\n              router.push({\n                path: e.menuTree[index].children[0].children[0].resourcePath\n              });\n            }\n          } else {\n            router.push({\n              path: e.menuTree[index].children[0].resourcePath\n            });\n          }\n        }, 200);\n        commit('setMenuList', e.menuTree);\n      });\n    });\n    return promise;\n  },\n  handleLogOut: function handleLogOut(_ref4) {\n    var commit = _ref4.commit;\n    var header = {\n      headers: {\n        'X-Requested-With': 'XMLHttpRequest',\n        'content-type': 'application/x-www-form-urlencoded'\n      }\n    };\n    var newRouter = createNewRoute();\n    routerInstance.matcher = newRouter.matcher;\n    api['account/logout']({\n      refreshToken: $cookies.get('refreshToken')\n    }, header).then(function (data) {});\n    commit('clearStore');\n    // 执行退出登录\n  },\n  setProgress: function setProgress(_ref5, progressObj) {\n    var commit = _ref5.commit;\n    commit('setProgress', progressObj);\n  },\n  delProgress: function delProgress(_ref6, props) {\n    var commit = _ref6.commit;\n    commit('delProgress', props);\n  },\n  download: function download(_ref7, data) {\n    var _this = this;\n    var commit = _ref7.commit;\n    var downProgress = {};\n    var uniSign = new Date().getTime() + '';\n    var loading = _Loading.service({\n      lock: true,\n      text: '导出文件中，请稍候...',\n      background: 'rgba(0, 0, 0, 0.7)',\n      customClass: 'download-loading-class' // 添加自定义class\n    });\n\n    // 获取用户名和生成Vud\n    var token = $cookies.get('userToken');\n    var tokenData = parseJwt(token);\n    var username = tokenData ? tokenData.account : '';\n    var finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\n    var Vud = encryptVud(finalUsername);\n    var handleError = function handleError(e) {\n      loading.close();\n      _this.$message.error('该文件无法下载');\n      console.error('Download failed:', e);\n    };\n    if (data.form) {\n      axios.post(data.url, data.form, {\n        responseType: 'blob',\n        headers: {\n          'Authorization': 'Bearer ' + $cookies.get('userToken'),\n          TenantId: 0,\n          Vud: Vud\n        },\n        onDownloadProgress: function onDownloadProgress(progress) {\n          downProgress = Math.round(100 * progress.loaded / progress.total);\n          store.dispatch('setProgress', {\n            path: uniSign,\n            'progress': downProgress\n          });\n        }\n      }).then(function (res) {\n        loading.close();\n        if (data.downLoad) {\n          jsFileDownLoad(res.data, data.downLoad);\n        } else {\n          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);\n        }\n      }).catch(handleError);\n    } else {\n      axios.get(data.url, {\n        responseType: 'blob',\n        headers: {\n          'Authorization': 'Bearer ' + $cookies.get('userToken'),\n          TenantId: 0,\n          Vud: Vud\n        },\n        onDownloadProgress: function onDownloadProgress(progress) {\n          downProgress = Math.round(100 * progress.loaded / progress.total);\n          store.dispatch('setProgress', {\n            path: uniSign,\n            'progress': downProgress\n          });\n        }\n      }).then(function (res) {\n        loading.close();\n        if (data.downLoad) {\n          jsFileDownLoad(res.data, data.downLoad);\n        } else {\n          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);\n        }\n      }).catch(handleError);\n    }\n  },\n  getUnreadMessageCount: function getUnreadMessageCount() {\n    // 获取未读信息数量\n  },\n  loginFun: function loginFun() {} // updateUserInfo ({ commit }) {\n  //   api['common/userInfo']().then(data => {\n  //     commit('setUserName', data.userName)\n  //   })\n  // }\n};", {"version": 3, "names": ["api", "router", "routerInstance", "createNewRoute", "ROUTERS_PERMISSION", "md5", "_", "axios", "store", "jsFileDownLoad", "localCache", "CryptoJS", "flags", "decrypt<PERSON>ey", "encrypted<PERSON>ey", "atob", "error", "console", "encryptVud", "username", "timestamp", "Date", "getTime", "random", "Math", "floor", "str", "concat", "cryptoKey", "key", "enc", "Utf8", "parse", "iv", "strUtf8", "encrypted", "AES", "encrypt", "mode", "CBC", "padding", "pad", "Pkcs7", "Base64", "stringify", "ciphertext", "parseJwt", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "map", "c", "charCodeAt", "toString", "slice", "join", "JSON", "login", "_ref", "loginInfo", "commit", "flattenTree", "tree", "result", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "node", "value", "push", "children", "apply", "_toConsumableArray", "err", "e", "f", "data", "password", "grant_type", "fromPage", "getLocal", "stype", "location", "pathname", "code", "search", "path", "log", "socialType", "then", "bindResult", "bound", "loginParams", "social_key", "promise", "setLocal", "refreshToken", "exp", "btnarr", "buttonList", "for<PERSON>ach", "per", "resourcePageName", "account", "setSession", "cloneDeep", "permissions", "newpermissions", "menuTree", "length", "index", "addRoute", "setTimeout", "findIndex", "r", "resourcePath", "element", "k", "i", "j", "checkToken", "_ref2", "_iterator2", "_step2", "maxkeylogin", "_ref3", "_iterator3", "_step3", "handleLogOut", "_ref4", "header", "headers", "newRouter", "matcher", "$cookies", "get", "setProgress", "_ref5", "progressObj", "delProgress", "_ref6", "props", "download", "_ref7", "_this", "downProgress", "uniSign", "loading", "_Loading", "service", "lock", "text", "background", "customClass", "tokenData", "finalUsername", "<PERSON><PERSON>", "handleError", "close", "$message", "form", "post", "url", "responseType", "TenantId", "onDownloadProgress", "progress", "round", "loaded", "total", "dispatch", "res", "downLoad", "catch", "getUnreadMessageCount", "loginFun"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/service/store/common/actions.js"], "sourcesContent": ["/* eslint-disable max-nested-callbacks */\r\nimport api from '@/plugins/api';\r\nimport router from '@/plugins/router';\r\nimport {Loading} from 'element-ui';\r\n// eslint-disable-next-line no-duplicate-imports\r\nimport {\r\n  routerInstance,\r\n  createNewRoute\r\n} from '@/plugins/router';\r\n// import permission from '@/routes/permission-parking'\r\n// 引入系统路由表\r\nimport {\r\n  ROUTERS_PERMISSION\r\n} from '@/global/module-config';\r\n// import qs from 'qs'\r\n// import axios from 'axios'\r\nimport md5 from 'js-md5';\r\nimport _ from 'lodash';\r\nimport axios from 'axios';\r\nimport store from '@/plugins/store';\r\nimport jsFileDownLoad from 'js-file-download';\r\nimport localCache from '@/utils/storage';\r\nimport CryptoJS from 'crypto-js';\r\nvar flags = true;\r\n\r\n// 解密函数 - 用于解密密钥\r\nfunction decryptKey(encryptedKey) {\r\n  try {\r\n    return atob(encryptedKey);\r\n  } catch (error) {\r\n    console.error('Key decryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 加密函数\r\nfunction encryptVud(username) {\r\n  const timestamp = new Date().getTime();\r\n  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数\r\n  const str = `${username}+${timestamp}${random}`;\r\n\r\n  // 使用正确的加密后的key\r\n  const encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\r\n  const cryptoKey = decryptKey(encryptedKey);\r\n\r\n  try {\r\n    const key = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const strUtf8 = CryptoJS.enc.Utf8.parse(str);\r\n    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\r\n      iv: iv,\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7\r\n    });\r\n\r\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\r\n  } catch (error) {\r\n    console.error('Vud encryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 添加 JWT token 解析函数\r\nfunction parseJwt(token) {\r\n  try {\r\n    if (!token) {\r\n      return null;\r\n    }\r\n    const base64Url = token.split('.')[1];\r\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {\r\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\r\n    }).join(''));\r\n    return JSON.parse(jsonPayload);\r\n  } catch (error) {\r\n    console.error('Token parsing failed:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport default {\r\n  login({\r\n    commit\r\n  }, loginInfo) {\r\n    let flattenTree = tree => {\r\n      const result = [];\r\n      for (const node of tree) {\r\n        result.push(node);\r\n        if (node.children) {\r\n          result.push(...flattenTree(node.children));\r\n        }\r\n      }\r\n      return result;\r\n    };\r\n    commit('clearStore');\r\n    let data = {};\r\n    if (loginInfo) {\r\n      data = {\r\n        username: loginInfo.username,\r\n        password: md5(loginInfo.password),\r\n        grant_type: 'password',\r\n        fromPage: 'noDesktop'\r\n      };\r\n    }\r\n    let username = localCache.getLocal('nowUserName');\r\n    if (!username) {\r\n      username = loginInfo.username;\r\n    }\r\n    // if (location.pathname === '/social/max_key/callback') {\r\n    //   return;\r\n    // }\r\n    var stype = location.pathname.split('/')[2];\r\n    var code = location.search.split('=')[1];\r\n    let path = location.pathname.split('/')[1];\r\n    if (path === 'social') {\r\n      console.log(path, '----------', location);\r\n      if (!flags) {\r\n        return;\r\n      }\r\n      flags = false;\r\n      api['account/socialcallback']({\r\n        code: code,\r\n        socialType: stype\r\n      }).then(data => {\r\n        const bindResult = data;\r\n        if (bindResult && bindResult !== '') {\r\n          if (bindResult.bound) {\r\n            const loginParams = {\r\n              grant_type: 'social',\r\n              social_key: bindResult.key\r\n            };\r\n            let promise = api['account/getToken'](loginParams).then(data => {\r\n              localCache.setLocal('userToken', data.token);\r\n              localCache.setLocal('refreshToken', data.refreshToken);\r\n              localCache.setLocal('userExpir', data.exp);\r\n              api['account/getUsers']().then(e => {\r\n                let btnarr = [];\r\n                e.buttonList.forEach(per => {\r\n                  btnarr.push(per.resourcePageName);\r\n                });\r\n                localCache.setLocal('btnPermissions', btnarr);\r\n                localCache.setLocal('nowUserName', e.account);\r\n                localCache.setSession('username', e.account); // 存储用户信息\r\n                commit('setPermissionList', _.cloneDeep(e.permissions));\r\n                commit('setUserInfo', e);\r\n                let newpermissions = [];\r\n                if (e.menuTree) {\r\n                  newpermissions = flattenTree(e.menuTree);\r\n                }\r\n\r\n                commit('setPermissions', newpermissions);\r\n                // route升级，讲vue2的addRoutes换为addRoute\r\n                ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e,\r\n                  index) => {\r\n                  routerInstance.addRoute(index, e);\r\n                });\r\n                // routerInstance.addRoutes(routes)\r\n                setTimeout(() => {\r\n                  let index = e.menuTree.findIndex(r => {\r\n                    return r.resourcePageName === 'system/cfg';\r\n                  });\r\n                  if (e.menuTree[index].children[0].children) {\r\n                    if (!e.menuTree[index].children[0].children[0].children) {\r\n                      router.push({\r\n                        path: e.menuTree[index].children[0].children[0].resourcePath\r\n                      });\r\n                    }\r\n                  } else {\r\n                    router.push({\r\n                      path: e.menuTree[index].children[0].resourcePath\r\n                    });\r\n                  }\r\n                }, 200);\r\n                commit('setMenuList', e.menuTree);\r\n              });\r\n            });\r\n            return promise;\r\n          }\r\n        }\r\n      });\r\n      throw '';\r\n      // return promises;\r\n    }\r\n    let promise = loginInfo ? api['account/getToken'](data) : api['account/getUsers']();\r\n    promise.then(data => {\r\n      if (loginInfo) {\r\n        loginInfo && localCache.setLocal('userToken', data.token);\r\n        loginInfo && localCache.setLocal('refreshToken', data.refreshToken);\r\n        localCache.setLocal('userExpir', data.exp);\r\n        api['account/getUsers']().then(e => {\r\n          let btnarr = [];\r\n          e.buttonList.forEach(per => {\r\n            btnarr.push(per.resourcePageName);\r\n          });\r\n          localCache.setLocal('btnPermissions', btnarr);\r\n          localCache.setLocal('nowUserName', e.account);\r\n          localCache.setSession('username', e.account); // 存储用户信息\r\n          commit('setPermissionList', _.cloneDeep(e.permissions));\r\n          commit('setUserInfo', e);\r\n          let newpermissions = [];\r\n          if (e.menuTree) {\r\n            newpermissions = flattenTree(e.menuTree);\r\n          }\r\n\r\n          commit('setPermissions', newpermissions);\r\n          // route升级，讲vue2的addRoutes换为addRoute\r\n          ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e,\r\n            index) => {\r\n            routerInstance.addRoute(index, e);\r\n          });\r\n          // routerInstance.addRoutes(routes)\r\n          setTimeout(() => {\r\n            let index = e.menuTree.findIndex(r => {\r\n              return r.resourcePageName === 'system/cfg';\r\n            });\r\n            if (e.menuTree[index].children[0].children) {\r\n              if (!e.menuTree[index].children[0].children[0].children) {\r\n                router.push({\r\n                  path: e.menuTree[index].children[0].children[0].resourcePath\r\n                });\r\n              }\r\n            } else {\r\n              router.push({\r\n                path: e.menuTree[index].children[0].resourcePath\r\n              });\r\n            }\r\n          }, 200);\r\n\r\n          commit('setMenuList', e.menuTree);\r\n        });\r\n      } else {\r\n        if (!data) {\r\n          return;\r\n        }\r\n        let btnarr = [];\r\n        data.buttonList.forEach(per => {\r\n          btnarr.push(per.resourcePageName);\r\n        });\r\n        localCache.setLocal('btnPermissions', btnarr);\r\n        localCache.setLocal('nowUserName', data.account);\r\n        localCache.setSession('username', data.account); // 存储用户信息\r\n        commit('setPermissionList', _.cloneDeep(data.permissions));\r\n        commit('setUserInfo', data);\r\n        let newpermissions = [];\r\n        if (data.menuTree) {\r\n          data.menuTree.forEach(element => {\r\n            newpermissions.push(element);\r\n            if (element.children) {\r\n              element.children.forEach(k => {\r\n                newpermissions.push(k);\r\n                if (k.children) {\r\n                  k.children.forEach(i => {\r\n                    newpermissions.push(i);\r\n                    if (i.children) {\r\n                      i.children.forEach(j => {\r\n                        newpermissions.push(j);\r\n                      });\r\n                    }\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          });\r\n        }\r\n        // route升级，讲vue2的addRoutes换为addRoute\r\n        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {\r\n          routerInstance.addRoute(index, e);\r\n        });\r\n        // routerInstance.addRoutes(routes)\r\n        commit('setPermissions', newpermissions);\r\n        commit('setMenuList', data.menuTree);\r\n      }\r\n    });\r\n    return promise;\r\n  },\r\n  checkToken({\r\n    commit\r\n  }, path) {\r\n    let flattenTree = tree => {\r\n      const result = [];\r\n      for (const node of tree) {\r\n        result.push(node);\r\n        if (node.children) {\r\n          result.push(...flattenTree(node.children));\r\n        }\r\n      }\r\n      return result;\r\n    };\r\n    api['account/getUsers']().then(e => {\r\n      let btnarr = [];\r\n      e.buttonList.forEach(per => {\r\n        btnarr.push(per.resourcePageName);\r\n      });\r\n      localCache.setLocal('btnPermissions', btnarr);\r\n      localCache.setLocal('nowUserName', e.account);\r\n      localCache.setSession('username', e.account); // 存储用户信息\r\n      commit('setPermissionList', _.cloneDeep(e.permissions));\r\n      commit('setUserInfo', e);\r\n      let newpermissions = [];\r\n      if (e.menuTree) {\r\n        newpermissions = flattenTree(e.menuTree);\r\n      }\r\n\r\n      commit('setPermissions', newpermissions);\r\n      // route升级，讲vue2的addRoutes换为addRoute\r\n      ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {\r\n        routerInstance.addRoute(index, e);\r\n      });\r\n      // routerInstance.addRoutes(routes)\r\n      setTimeout(() => {\r\n        let index = e.menuTree.findIndex(r => {\r\n          return r.resourcePageName === 'system/cfg';\r\n        });\r\n        if (e.menuTree[index].children[0].children) {\r\n          if (!e.menuTree[index].children[0].children[0].children) {\r\n            router.push({\r\n              path: e.menuTree[index].children[0].children[0].resourcePath\r\n            });\r\n          }\r\n        } else {\r\n          router.push({\r\n            path: e.menuTree[index].children[0].resourcePath\r\n          });\r\n        }\r\n      }, 200);\r\n      // setTimeout(() => {\r\n      //   router.push({\r\n      //     path: path\r\n      //   });\r\n      // }, 200);\r\n\r\n      commit('setMenuList', e.menuTree);\r\n    });\r\n  },\r\n  maxkeylogin({\r\n    commit\r\n  }, loginInfo) {\r\n    let flattenTree = tree => {\r\n      const result = [];\r\n      for (const node of tree) {\r\n        result.push(node);\r\n        if (node.children) {\r\n          result.push(...flattenTree(node.children));\r\n        }\r\n      }\r\n      return result;\r\n    };\r\n    api['account/getToken'](loginInfo).then(data => {\r\n      localCache.setLocal('userToken', data.token);\r\n      localCache.setLocal('refreshToken', data.refreshToken);\r\n      localCache.setLocal('userExpir', data.exp);\r\n      api['account/getUsers']().then(e => {\r\n        let btnarr = [];\r\n        e.buttonList.forEach(per => {\r\n          btnarr.push(per.resourcePageName);\r\n        });\r\n        localCache.setLocal('btnPermissions', btnarr);\r\n        localCache.setLocal('nowUserName', e.account);\r\n        localCache.setSession('username', e.account); // 存储用户信息\r\n        commit('setPermissionList', _.cloneDeep(e.permissions));\r\n        commit('setUserInfo', e);\r\n        let newpermissions = [];\r\n        if (e.menuTree) {\r\n          newpermissions = flattenTree(e.menuTree);\r\n        }\r\n        commit('setPermissions', newpermissions);\r\n        // route升级，讲vue2的addRoutes换为addRoute\r\n        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {\r\n          routerInstance.addRoute(index, e);\r\n        });\r\n        // routerInstance.addRoutes(routes)\r\n        setTimeout(() => {\r\n          let index = e.menuTree.findIndex(r => {\r\n            return r.resourcePageName === 'system/cfg';\r\n          });\r\n          if (e.menuTree[index].children[0].children) {\r\n            if (!e.menuTree[index].children[0].children[0].children) {\r\n              router.push({\r\n                path: e.menuTree[index].children[0].children[0].resourcePath\r\n              });\r\n            }\r\n          } else {\r\n            router.push({\r\n              path: e.menuTree[index].children[0].resourcePath\r\n            });\r\n          }\r\n        }, 200);\r\n\r\n        commit('setMenuList', e.menuTree);\r\n      });\r\n    });\r\n    return promise;\r\n  },\r\n  handleLogOut({\r\n    commit\r\n  }) {\r\n    let header = {\r\n      headers: {\r\n        'X-Requested-With': 'XMLHttpRequest',\r\n        'content-type': 'application/x-www-form-urlencoded'\r\n      }\r\n    };\r\n    let newRouter = createNewRoute();\r\n    routerInstance.matcher = newRouter.matcher;\r\n    api['account/logout']({\r\n      refreshToken: $cookies.get('refreshToken')\r\n    }, header).then(data => {});\r\n    commit('clearStore');\r\n    // 执行退出登录\r\n\r\n  },\r\n  setProgress({\r\n    commit\r\n  }, progressObj) {\r\n    commit('setProgress', progressObj);\r\n  },\r\n  delProgress({\r\n    commit\r\n  }, props) {\r\n    commit('delProgress', props);\r\n  },\r\n  download({\r\n    commit\r\n  }, data) {\r\n    let downProgress = {};\r\n    let uniSign = new Date().getTime() + '';\r\n    const loading = Loading.service({\r\n      lock: true,\r\n      text: '导出文件中，请稍候...',\r\n      background: 'rgba(0, 0, 0, 0.7)',\r\n      customClass: 'download-loading-class' // 添加自定义class\r\n    });\r\n\r\n    // 获取用户名和生成Vud\r\n    const token = $cookies.get('userToken');\r\n    const tokenData = parseJwt(token);\r\n    const username = tokenData ? tokenData.account : '';\r\n    const finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\r\n    const Vud = encryptVud(finalUsername);\r\n\r\n    const handleError = e => {\r\n      loading.close();\r\n      this.$message.error('该文件无法下载');\r\n      console.error('Download failed:', e);\r\n    };\r\n\r\n    if (data.form) {\r\n      axios.post(\r\n        data.url, data.form, {\r\n          responseType: 'blob',\r\n          headers: {\r\n            'Authorization': 'Bearer ' + $cookies.get('userToken'),\r\n            TenantId: 0,\r\n            Vud: Vud\r\n          },\r\n          onDownloadProgress(progress) {\r\n            downProgress = Math.round(100 * progress.loaded / progress.total);\r\n            store.dispatch('setProgress', {\r\n              path: uniSign,\r\n              'progress': downProgress\r\n            });\r\n          }\r\n        }).then(res => {\r\n        loading.close();\r\n        if (data.downLoad) {\r\n          jsFileDownLoad(res.data, data.downLoad);\r\n        } else {\r\n          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);\r\n        }\r\n      }).catch(handleError);\r\n    } else {\r\n      axios.get(\r\n        data.url, {\r\n          responseType: 'blob',\r\n          headers: {\r\n            'Authorization': 'Bearer ' + $cookies.get('userToken'),\r\n            TenantId: 0,\r\n            Vud: Vud\r\n          },\r\n          onDownloadProgress(progress) {\r\n            downProgress = Math.round(100 * progress.loaded / progress.total);\r\n            store.dispatch('setProgress', {\r\n              path: uniSign,\r\n              'progress': downProgress\r\n            });\r\n          }\r\n        }).then(res => {\r\n        loading.close();\r\n        if (data.downLoad) {\r\n          jsFileDownLoad(res.data, data.downLoad);\r\n        } else {\r\n          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);\r\n        }\r\n      }).catch(handleError);\r\n    }\r\n  },\r\n  getUnreadMessageCount() {\r\n    // 获取未读信息数量\r\n  },\r\n  loginFun() {}\r\n\r\n  // updateUserInfo ({ commit }) {\r\n  //   api['common/userInfo']().then(data => {\r\n  //     commit('setUserName', data.userName)\r\n  //   })\r\n  // }\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA,OAAOA,GAAG,MAAM,eAAe;AAC/B,OAAOC,MAAM,MAAM,kBAAkB;AAErC;AACA,SACEC,cAAc,EACdC,cAAc,QACT,kBAAkB;AACzB;AACA;AACA,SACEC,kBAAkB,QACb,wBAAwB;AAC/B;AACA;AACA,OAAOC,GAAG,MAAM,QAAQ;AACxB,OAAOC,CAAC,MAAM,QAAQ;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,WAAW;AAChC,IAAIC,KAAK,GAAG,IAAI;;AAEhB;AACA,SAASC,UAAUA,CAACC,YAAY,EAAE;EAChC,IAAI;IACF,OAAOC,IAAI,CAACD,YAAY,CAAC;EAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,SAASE,UAAUA,CAACC,QAAQ,EAAE;EAC5B,IAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACtC,IAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EACpD,IAAMG,GAAG,MAAAC,MAAA,CAAMR,QAAQ,OAAAQ,MAAA,CAAIP,SAAS,EAAAO,MAAA,CAAGJ,MAAM,CAAE;;EAE/C;EACA,IAAMT,YAAY,GAAG,0BAA0B;EAC/C,IAAMc,SAAS,GAAGf,UAAU,CAACC,YAAY,CAAC;EAE1C,IAAI;IACF,IAAMe,GAAG,GAAGlB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC9C,IAAMK,EAAE,GAAGtB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC7C,IAAMM,OAAO,GAAGvB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;IAC5C,IAAMS,SAAS,GAAGxB,QAAQ,CAACyB,GAAG,CAACC,OAAO,CAACH,OAAO,EAAEL,GAAG,EAAE;MACnDI,EAAE,EAAEA,EAAE;MACNK,IAAI,EAAE3B,QAAQ,CAAC2B,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAE7B,QAAQ,CAAC8B,GAAG,CAACC;IACxB,CAAC,CAAC;IAEF,OAAO/B,QAAQ,CAACmB,GAAG,CAACa,MAAM,CAACC,SAAS,CAACT,SAAS,CAACU,UAAU,CAAC;EAC5D,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,SAAS8B,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACF,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;IACb;IACA,IAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,IAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC9D,IAAMC,WAAW,GAAGC,kBAAkB,CAACtC,IAAI,CAACmC,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACK,GAAG,CAAC,UAAUC,CAAC,EAAE;MAC7E,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;IACZ,OAAOC,IAAI,CAAC5B,KAAK,CAACoB,WAAW,CAAC;EAChC,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,IAAI;EACb;AACF;AAEA,eAAe;EACb6C,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAEFC,SAAS,EAAE;IAAA,IADZC,MAAM,GAAAF,IAAA,CAANE,MAAM;IAEN,IAAIC,YAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAI,EAAI;MACxB,IAAMC,MAAM,GAAG,EAAE;MAAC,IAAAC,SAAA,GAAAC,0BAAA,CACCH,IAAI;QAAAI,KAAA;MAAA;QAAvB,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAdC,IAAI,GAAAJ,KAAA,CAAAK,KAAA;UACbR,MAAM,CAACS,IAAI,CAACF,IAAI,CAAC;UACjB,IAAIA,IAAI,CAACG,QAAQ,EAAE;YACjBV,MAAM,CAACS,IAAI,CAAAE,KAAA,CAAXX,MAAM,EAAAY,kBAAA,CAASd,YAAW,CAACS,IAAI,CAACG,QAAQ,CAAC,EAAC;UAC5C;QACF;MAAC,SAAAG,GAAA;QAAAZ,SAAA,CAAAa,CAAA,CAAAD,GAAA;MAAA;QAAAZ,SAAA,CAAAc,CAAA;MAAA;MACD,OAAOf,MAAM;IACf,CAAC;IACDH,MAAM,CAAC,YAAY,CAAC;IACpB,IAAImB,IAAI,GAAG,CAAC,CAAC;IACb,IAAIpB,SAAS,EAAE;MACboB,IAAI,GAAG;QACLhE,QAAQ,EAAE4C,SAAS,CAAC5C,QAAQ;QAC5BiE,QAAQ,EAAE/E,GAAG,CAAC0D,SAAS,CAACqB,QAAQ,CAAC;QACjCC,UAAU,EAAE,UAAU;QACtBC,QAAQ,EAAE;MACZ,CAAC;IACH;IACA,IAAInE,QAAQ,GAAGT,UAAU,CAAC6E,QAAQ,CAAC,aAAa,CAAC;IACjD,IAAI,CAACpE,QAAQ,EAAE;MACbA,QAAQ,GAAG4C,SAAS,CAAC5C,QAAQ;IAC/B;IACA;IACA;IACA;IACA,IAAIqE,KAAK,GAAGC,QAAQ,CAACC,QAAQ,CAACzC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI0C,IAAI,GAAGF,QAAQ,CAACG,MAAM,CAAC3C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI4C,IAAI,GAAGJ,QAAQ,CAACC,QAAQ,CAACzC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI4C,IAAI,KAAK,QAAQ,EAAE;MACrB5E,OAAO,CAAC6E,GAAG,CAACD,IAAI,EAAE,YAAY,EAAEJ,QAAQ,CAAC;MACzC,IAAI,CAAC7E,KAAK,EAAE;QACV;MACF;MACAA,KAAK,GAAG,KAAK;MACbZ,GAAG,CAAC,wBAAwB,CAAC,CAAC;QAC5B2F,IAAI,EAAEA,IAAI;QACVI,UAAU,EAAEP;MACd,CAAC,CAAC,CAACQ,IAAI,CAAC,UAAAb,IAAI,EAAI;QACd,IAAMc,UAAU,GAAGd,IAAI;QACvB,IAAIc,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;UACnC,IAAIA,UAAU,CAACC,KAAK,EAAE;YACpB,IAAMC,WAAW,GAAG;cAClBd,UAAU,EAAE,QAAQ;cACpBe,UAAU,EAAEH,UAAU,CAACpE;YACzB,CAAC;YACD,IAAIwE,QAAO,GAAGrG,GAAG,CAAC,kBAAkB,CAAC,CAACmG,WAAW,CAAC,CAACH,IAAI,CAAC,UAAAb,IAAI,EAAI;cAC9DzE,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACpC,KAAK,CAAC;cAC5CrC,UAAU,CAAC4F,QAAQ,CAAC,cAAc,EAAEnB,IAAI,CAACoB,YAAY,CAAC;cACtD7F,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACqB,GAAG,CAAC;cAC1CxG,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAACgG,IAAI,CAAC,UAAAf,CAAC,EAAI;gBAClC,IAAIwB,MAAM,GAAG,EAAE;gBACfxB,CAAC,CAACyB,UAAU,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;kBAC1BH,MAAM,CAAC7B,IAAI,CAACgC,GAAG,CAACC,gBAAgB,CAAC;gBACnC,CAAC,CAAC;gBACFnG,UAAU,CAAC4F,QAAQ,CAAC,gBAAgB,EAAEG,MAAM,CAAC;gBAC7C/F,UAAU,CAAC4F,QAAQ,CAAC,aAAa,EAAErB,CAAC,CAAC6B,OAAO,CAAC;gBAC7CpG,UAAU,CAACqG,UAAU,CAAC,UAAU,EAAE9B,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;gBAC9C9C,MAAM,CAAC,mBAAmB,EAAE1D,CAAC,CAAC0G,SAAS,CAAC/B,CAAC,CAACgC,WAAW,CAAC,CAAC;gBACvDjD,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAAC;gBACxB,IAAIiC,cAAc,GAAG,EAAE;gBACvB,IAAIjC,CAAC,CAACkC,QAAQ,EAAE;kBACdD,cAAc,GAAGjD,YAAW,CAACgB,CAAC,CAACkC,QAAQ,CAAC;gBAC1C;gBAEAnD,MAAM,CAAC,gBAAgB,EAAEkD,cAAc,CAAC;gBACxC;gBACA9G,kBAAkB,CAAC8G,cAAc,CAAC,CAACE,MAAM,IAAIhH,kBAAkB,CAAC8G,cAAc,CAAC,CAACP,OAAO,CAAC,UAAC1B,CAAC,EACxFoC,KAAK,EAAK;kBACVnH,cAAc,CAACoH,QAAQ,CAACD,KAAK,EAAEpC,CAAC,CAAC;gBACnC,CAAC,CAAC;gBACF;gBACAsC,UAAU,CAAC,YAAM;kBACf,IAAIF,KAAK,GAAGpC,CAAC,CAACkC,QAAQ,CAACK,SAAS,CAAC,UAAAC,CAAC,EAAI;oBACpC,OAAOA,CAAC,CAACZ,gBAAgB,KAAK,YAAY;kBAC5C,CAAC,CAAC;kBACF,IAAI5B,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;oBAC1C,IAAI,CAACI,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;sBACvD5E,MAAM,CAAC2E,IAAI,CAAC;wBACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC6C;sBAClD,CAAC,CAAC;oBACJ;kBACF,CAAC,MAAM;oBACLzH,MAAM,CAAC2E,IAAI,CAAC;sBACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC6C;oBACtC,CAAC,CAAC;kBACJ;gBACF,CAAC,EAAE,GAAG,CAAC;gBACP1D,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAACkC,QAAQ,CAAC;cACnC,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,OAAOd,QAAO;UAChB;QACF;MACF,CAAC,CAAC;MACF,MAAM,EAAE;MACR;IACF;IACA,IAAIA,OAAO,GAAGtC,SAAS,GAAG/D,GAAG,CAAC,kBAAkB,CAAC,CAACmF,IAAI,CAAC,GAAGnF,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACnFqG,OAAO,CAACL,IAAI,CAAC,UAAAb,IAAI,EAAI;MACnB,IAAIpB,SAAS,EAAE;QACbA,SAAS,IAAIrD,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACpC,KAAK,CAAC;QACzDgB,SAAS,IAAIrD,UAAU,CAAC4F,QAAQ,CAAC,cAAc,EAAEnB,IAAI,CAACoB,YAAY,CAAC;QACnE7F,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACqB,GAAG,CAAC;QAC1CxG,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAACgG,IAAI,CAAC,UAAAf,CAAC,EAAI;UAClC,IAAIwB,MAAM,GAAG,EAAE;UACfxB,CAAC,CAACyB,UAAU,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;YAC1BH,MAAM,CAAC7B,IAAI,CAACgC,GAAG,CAACC,gBAAgB,CAAC;UACnC,CAAC,CAAC;UACFnG,UAAU,CAAC4F,QAAQ,CAAC,gBAAgB,EAAEG,MAAM,CAAC;UAC7C/F,UAAU,CAAC4F,QAAQ,CAAC,aAAa,EAAErB,CAAC,CAAC6B,OAAO,CAAC;UAC7CpG,UAAU,CAACqG,UAAU,CAAC,UAAU,EAAE9B,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;UAC9C9C,MAAM,CAAC,mBAAmB,EAAE1D,CAAC,CAAC0G,SAAS,CAAC/B,CAAC,CAACgC,WAAW,CAAC,CAAC;UACvDjD,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAAC;UACxB,IAAIiC,cAAc,GAAG,EAAE;UACvB,IAAIjC,CAAC,CAACkC,QAAQ,EAAE;YACdD,cAAc,GAAGjD,YAAW,CAACgB,CAAC,CAACkC,QAAQ,CAAC;UAC1C;UAEAnD,MAAM,CAAC,gBAAgB,EAAEkD,cAAc,CAAC;UACxC;UACA9G,kBAAkB,CAAC8G,cAAc,CAAC,CAACE,MAAM,IAAIhH,kBAAkB,CAAC8G,cAAc,CAAC,CAACP,OAAO,CAAC,UAAC1B,CAAC,EACxFoC,KAAK,EAAK;YACVnH,cAAc,CAACoH,QAAQ,CAACD,KAAK,EAAEpC,CAAC,CAAC;UACnC,CAAC,CAAC;UACF;UACAsC,UAAU,CAAC,YAAM;YACf,IAAIF,KAAK,GAAGpC,CAAC,CAACkC,QAAQ,CAACK,SAAS,CAAC,UAAAC,CAAC,EAAI;cACpC,OAAOA,CAAC,CAACZ,gBAAgB,KAAK,YAAY;YAC5C,CAAC,CAAC;YACF,IAAI5B,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;cAC1C,IAAI,CAACI,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;gBACvD5E,MAAM,CAAC2E,IAAI,CAAC;kBACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC6C;gBAClD,CAAC,CAAC;cACJ;YACF,CAAC,MAAM;cACLzH,MAAM,CAAC2E,IAAI,CAAC;gBACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC6C;cACtC,CAAC,CAAC;YACJ;UACF,CAAC,EAAE,GAAG,CAAC;UAEP1D,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAACkC,QAAQ,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAChC,IAAI,EAAE;UACT;QACF;QACA,IAAIsB,MAAM,GAAG,EAAE;QACftB,IAAI,CAACuB,UAAU,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;UAC7BH,MAAM,CAAC7B,IAAI,CAACgC,GAAG,CAACC,gBAAgB,CAAC;QACnC,CAAC,CAAC;QACFnG,UAAU,CAAC4F,QAAQ,CAAC,gBAAgB,EAAEG,MAAM,CAAC;QAC7C/F,UAAU,CAAC4F,QAAQ,CAAC,aAAa,EAAEnB,IAAI,CAAC2B,OAAO,CAAC;QAChDpG,UAAU,CAACqG,UAAU,CAAC,UAAU,EAAE5B,IAAI,CAAC2B,OAAO,CAAC,CAAC,CAAC;QACjD9C,MAAM,CAAC,mBAAmB,EAAE1D,CAAC,CAAC0G,SAAS,CAAC7B,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAC1DjD,MAAM,CAAC,aAAa,EAAEmB,IAAI,CAAC;QAC3B,IAAI+B,cAAc,GAAG,EAAE;QACvB,IAAI/B,IAAI,CAACgC,QAAQ,EAAE;UACjBhC,IAAI,CAACgC,QAAQ,CAACR,OAAO,CAAC,UAAAgB,OAAO,EAAI;YAC/BT,cAAc,CAACtC,IAAI,CAAC+C,OAAO,CAAC;YAC5B,IAAIA,OAAO,CAAC9C,QAAQ,EAAE;cACpB8C,OAAO,CAAC9C,QAAQ,CAAC8B,OAAO,CAAC,UAAAiB,CAAC,EAAI;gBAC5BV,cAAc,CAACtC,IAAI,CAACgD,CAAC,CAAC;gBACtB,IAAIA,CAAC,CAAC/C,QAAQ,EAAE;kBACd+C,CAAC,CAAC/C,QAAQ,CAAC8B,OAAO,CAAC,UAAAkB,CAAC,EAAI;oBACtBX,cAAc,CAACtC,IAAI,CAACiD,CAAC,CAAC;oBACtB,IAAIA,CAAC,CAAChD,QAAQ,EAAE;sBACdgD,CAAC,CAAChD,QAAQ,CAAC8B,OAAO,CAAC,UAAAmB,CAAC,EAAI;wBACtBZ,cAAc,CAACtC,IAAI,CAACkD,CAAC,CAAC;sBACxB,CAAC,CAAC;oBACJ;kBACF,CAAC,CAAC;gBACJ;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;QACA;QACA1H,kBAAkB,CAAC8G,cAAc,CAAC,CAACE,MAAM,IAAIhH,kBAAkB,CAAC8G,cAAc,CAAC,CAACP,OAAO,CAAC,UAAC1B,CAAC,EAAEoC,KAAK,EAAK;UACpGnH,cAAc,CAACoH,QAAQ,CAACD,KAAK,EAAEpC,CAAC,CAAC;QACnC,CAAC,CAAC;QACF;QACAjB,MAAM,CAAC,gBAAgB,EAAEkD,cAAc,CAAC;QACxClD,MAAM,CAAC,aAAa,EAAEmB,IAAI,CAACgC,QAAQ,CAAC;MACtC;IACF,CAAC,CAAC;IACF,OAAOd,OAAO;EAChB,CAAC;EACD0B,UAAU,WAAVA,UAAUA,CAAAC,KAAA,EAEPnC,IAAI,EAAE;IAAA,IADP7B,MAAM,GAAAgE,KAAA,CAANhE,MAAM;IAEN,IAAIC,aAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAI,EAAI;MACxB,IAAMC,MAAM,GAAG,EAAE;MAAC,IAAA8D,UAAA,GAAA5D,0BAAA,CACCH,IAAI;QAAAgE,MAAA;MAAA;QAAvB,KAAAD,UAAA,CAAA1D,CAAA,MAAA2D,MAAA,GAAAD,UAAA,CAAAzD,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAdC,IAAI,GAAAwD,MAAA,CAAAvD,KAAA;UACbR,MAAM,CAACS,IAAI,CAACF,IAAI,CAAC;UACjB,IAAIA,IAAI,CAACG,QAAQ,EAAE;YACjBV,MAAM,CAACS,IAAI,CAAAE,KAAA,CAAXX,MAAM,EAAAY,kBAAA,CAASd,aAAW,CAACS,IAAI,CAACG,QAAQ,CAAC,EAAC;UAC5C;QACF;MAAC,SAAAG,GAAA;QAAAiD,UAAA,CAAAhD,CAAA,CAAAD,GAAA;MAAA;QAAAiD,UAAA,CAAA/C,CAAA;MAAA;MACD,OAAOf,MAAM;IACf,CAAC;IACDnE,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAACgG,IAAI,CAAC,UAAAf,CAAC,EAAI;MAClC,IAAIwB,MAAM,GAAG,EAAE;MACfxB,CAAC,CAACyB,UAAU,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;QAC1BH,MAAM,CAAC7B,IAAI,CAACgC,GAAG,CAACC,gBAAgB,CAAC;MACnC,CAAC,CAAC;MACFnG,UAAU,CAAC4F,QAAQ,CAAC,gBAAgB,EAAEG,MAAM,CAAC;MAC7C/F,UAAU,CAAC4F,QAAQ,CAAC,aAAa,EAAErB,CAAC,CAAC6B,OAAO,CAAC;MAC7CpG,UAAU,CAACqG,UAAU,CAAC,UAAU,EAAE9B,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;MAC9C9C,MAAM,CAAC,mBAAmB,EAAE1D,CAAC,CAAC0G,SAAS,CAAC/B,CAAC,CAACgC,WAAW,CAAC,CAAC;MACvDjD,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAAC;MACxB,IAAIiC,cAAc,GAAG,EAAE;MACvB,IAAIjC,CAAC,CAACkC,QAAQ,EAAE;QACdD,cAAc,GAAGjD,aAAW,CAACgB,CAAC,CAACkC,QAAQ,CAAC;MAC1C;MAEAnD,MAAM,CAAC,gBAAgB,EAAEkD,cAAc,CAAC;MACxC;MACA9G,kBAAkB,CAAC8G,cAAc,CAAC,CAACE,MAAM,IAAIhH,kBAAkB,CAAC8G,cAAc,CAAC,CAACP,OAAO,CAAC,UAAC1B,CAAC,EAAEoC,KAAK,EAAK;QACpGnH,cAAc,CAACoH,QAAQ,CAACD,KAAK,EAAEpC,CAAC,CAAC;MACnC,CAAC,CAAC;MACF;MACAsC,UAAU,CAAC,YAAM;QACf,IAAIF,KAAK,GAAGpC,CAAC,CAACkC,QAAQ,CAACK,SAAS,CAAC,UAAAC,CAAC,EAAI;UACpC,OAAOA,CAAC,CAACZ,gBAAgB,KAAK,YAAY;QAC5C,CAAC,CAAC;QACF,IAAI5B,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;UAC1C,IAAI,CAACI,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;YACvD5E,MAAM,CAAC2E,IAAI,CAAC;cACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC6C;YAClD,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACLzH,MAAM,CAAC2E,IAAI,CAAC;YACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC6C;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,GAAG,CAAC;MACP;MACA;MACA;MACA;MACA;;MAEA1D,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAACkC,QAAQ,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EACDgB,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAERrE,SAAS,EAAE;IAAA,IADZC,MAAM,GAAAoE,KAAA,CAANpE,MAAM;IAEN,IAAIC,aAAW,GAAG,SAAdA,WAAWA,CAAGC,IAAI,EAAI;MACxB,IAAMC,MAAM,GAAG,EAAE;MAAC,IAAAkE,UAAA,GAAAhE,0BAAA,CACCH,IAAI;QAAAoE,MAAA;MAAA;QAAvB,KAAAD,UAAA,CAAA9D,CAAA,MAAA+D,MAAA,GAAAD,UAAA,CAAA7D,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAdC,IAAI,GAAA4D,MAAA,CAAA3D,KAAA;UACbR,MAAM,CAACS,IAAI,CAACF,IAAI,CAAC;UACjB,IAAIA,IAAI,CAACG,QAAQ,EAAE;YACjBV,MAAM,CAACS,IAAI,CAAAE,KAAA,CAAXX,MAAM,EAAAY,kBAAA,CAASd,aAAW,CAACS,IAAI,CAACG,QAAQ,CAAC,EAAC;UAC5C;QACF;MAAC,SAAAG,GAAA;QAAAqD,UAAA,CAAApD,CAAA,CAAAD,GAAA;MAAA;QAAAqD,UAAA,CAAAnD,CAAA;MAAA;MACD,OAAOf,MAAM;IACf,CAAC;IACDnE,GAAG,CAAC,kBAAkB,CAAC,CAAC+D,SAAS,CAAC,CAACiC,IAAI,CAAC,UAAAb,IAAI,EAAI;MAC9CzE,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACpC,KAAK,CAAC;MAC5CrC,UAAU,CAAC4F,QAAQ,CAAC,cAAc,EAAEnB,IAAI,CAACoB,YAAY,CAAC;MACtD7F,UAAU,CAAC4F,QAAQ,CAAC,WAAW,EAAEnB,IAAI,CAACqB,GAAG,CAAC;MAC1CxG,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAACgG,IAAI,CAAC,UAAAf,CAAC,EAAI;QAClC,IAAIwB,MAAM,GAAG,EAAE;QACfxB,CAAC,CAACyB,UAAU,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;UAC1BH,MAAM,CAAC7B,IAAI,CAACgC,GAAG,CAACC,gBAAgB,CAAC;QACnC,CAAC,CAAC;QACFnG,UAAU,CAAC4F,QAAQ,CAAC,gBAAgB,EAAEG,MAAM,CAAC;QAC7C/F,UAAU,CAAC4F,QAAQ,CAAC,aAAa,EAAErB,CAAC,CAAC6B,OAAO,CAAC;QAC7CpG,UAAU,CAACqG,UAAU,CAAC,UAAU,EAAE9B,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;QAC9C9C,MAAM,CAAC,mBAAmB,EAAE1D,CAAC,CAAC0G,SAAS,CAAC/B,CAAC,CAACgC,WAAW,CAAC,CAAC;QACvDjD,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAAC;QACxB,IAAIiC,cAAc,GAAG,EAAE;QACvB,IAAIjC,CAAC,CAACkC,QAAQ,EAAE;UACdD,cAAc,GAAGjD,aAAW,CAACgB,CAAC,CAACkC,QAAQ,CAAC;QAC1C;QACAnD,MAAM,CAAC,gBAAgB,EAAEkD,cAAc,CAAC;QACxC;QACA9G,kBAAkB,CAAC8G,cAAc,CAAC,CAACE,MAAM,IAAIhH,kBAAkB,CAAC8G,cAAc,CAAC,CAACP,OAAO,CAAC,UAAC1B,CAAC,EAAEoC,KAAK,EAAK;UACpGnH,cAAc,CAACoH,QAAQ,CAACD,KAAK,EAAEpC,CAAC,CAAC;QACnC,CAAC,CAAC;QACF;QACAsC,UAAU,CAAC,YAAM;UACf,IAAIF,KAAK,GAAGpC,CAAC,CAACkC,QAAQ,CAACK,SAAS,CAAC,UAAAC,CAAC,EAAI;YACpC,OAAOA,CAAC,CAACZ,gBAAgB,KAAK,YAAY;UAC5C,CAAC,CAAC;UACF,IAAI5B,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;YAC1C,IAAI,CAACI,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,EAAE;cACvD5E,MAAM,CAAC2E,IAAI,CAAC;gBACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC6C;cAClD,CAAC,CAAC;YACJ;UACF,CAAC,MAAM;YACLzH,MAAM,CAAC2E,IAAI,CAAC;cACViB,IAAI,EAAEZ,CAAC,CAACkC,QAAQ,CAACE,KAAK,CAAC,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC6C;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;QAEP1D,MAAM,CAAC,aAAa,EAAEiB,CAAC,CAACkC,QAAQ,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOd,OAAO;EAChB,CAAC;EACDkC,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAET;IAAA,IADDxE,MAAM,GAAAwE,KAAA,CAANxE,MAAM;IAEN,IAAIyE,MAAM,GAAG;MACXC,OAAO,EAAE;QACP,kBAAkB,EAAE,gBAAgB;QACpC,cAAc,EAAE;MAClB;IACF,CAAC;IACD,IAAIC,SAAS,GAAGxI,cAAc,CAAC,CAAC;IAChCD,cAAc,CAAC0I,OAAO,GAAGD,SAAS,CAACC,OAAO;IAC1C5I,GAAG,CAAC,gBAAgB,CAAC,CAAC;MACpBuG,YAAY,EAAEsC,QAAQ,CAACC,GAAG,CAAC,cAAc;IAC3C,CAAC,EAAEL,MAAM,CAAC,CAACzC,IAAI,CAAC,UAAAb,IAAI,EAAI,CAAC,CAAC,CAAC;IAC3BnB,MAAM,CAAC,YAAY,CAAC;IACpB;EAEF,CAAC;EACD+E,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAERC,WAAW,EAAE;IAAA,IADdjF,MAAM,GAAAgF,KAAA,CAANhF,MAAM;IAENA,MAAM,CAAC,aAAa,EAAEiF,WAAW,CAAC;EACpC,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAERC,KAAK,EAAE;IAAA,IADRpF,MAAM,GAAAmF,KAAA,CAANnF,MAAM;IAENA,MAAM,CAAC,aAAa,EAAEoF,KAAK,CAAC;EAC9B,CAAC;EACDC,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAELnE,IAAI,EAAE;IAAA,IAAAoE,KAAA;IAAA,IADPvF,MAAM,GAAAsF,KAAA,CAANtF,MAAM;IAEN,IAAIwF,YAAY,GAAG,CAAC,CAAC;IACrB,IAAIC,OAAO,GAAG,IAAIpI,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,EAAE;IACvC,IAAMoI,OAAO,GAAGC,QAAA,CAAQC,OAAO,CAAC;MAC9BC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,oBAAoB;MAChCC,WAAW,EAAE,wBAAwB,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,IAAMjH,KAAK,GAAG8F,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;IACvC,IAAMmB,SAAS,GAAGnH,QAAQ,CAACC,KAAK,CAAC;IACjC,IAAM5B,QAAQ,GAAG8I,SAAS,GAAGA,SAAS,CAACnD,OAAO,GAAG,EAAE;IACnD,IAAMoD,aAAa,GAAG/I,QAAQ,IAAI0H,QAAQ,CAACC,GAAG,CAAC,aAAa,CAAC,IAAIpI,UAAU,CAAC6E,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;IACzG,IAAM4E,GAAG,GAAGjJ,UAAU,CAACgJ,aAAa,CAAC;IAErC,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAGnF,CAAC,EAAI;MACvByE,OAAO,CAACW,KAAK,CAAC,CAAC;MACfd,KAAI,CAACe,QAAQ,CAACtJ,KAAK,CAAC,SAAS,CAAC;MAC9BC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEiE,CAAC,CAAC;IACtC,CAAC;IAED,IAAIE,IAAI,CAACoF,IAAI,EAAE;MACbhK,KAAK,CAACiK,IAAI,CACRrF,IAAI,CAACsF,GAAG,EAAEtF,IAAI,CAACoF,IAAI,EAAE;QACnBG,YAAY,EAAE,MAAM;QACpBhC,OAAO,EAAE;UACP,eAAe,EAAE,SAAS,GAAGG,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;UACtD6B,QAAQ,EAAE,CAAC;UACXR,GAAG,EAAEA;QACP,CAAC;QACDS,kBAAkB,WAAlBA,kBAAkBA,CAACC,QAAQ,EAAE;UAC3BrB,YAAY,GAAGhI,IAAI,CAACsJ,KAAK,CAAC,GAAG,GAAGD,QAAQ,CAACE,MAAM,GAAGF,QAAQ,CAACG,KAAK,CAAC;UACjExK,KAAK,CAACyK,QAAQ,CAAC,aAAa,EAAE;YAC5BpF,IAAI,EAAE4D,OAAO;YACb,UAAU,EAAED;UACd,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACxD,IAAI,CAAC,UAAAkF,GAAG,EAAI;QACfxB,OAAO,CAACW,KAAK,CAAC,CAAC;QACf,IAAIlF,IAAI,CAACgG,QAAQ,EAAE;UACjB1K,cAAc,CAACyK,GAAG,CAAC/F,IAAI,EAAEA,IAAI,CAACgG,QAAQ,CAAC;QACzC,CAAC,MAAM;UACL1K,cAAc,CAACyK,GAAG,CAAC/F,IAAI,EAAEA,IAAI,CAACsF,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC,CAACkC,IAAI,CAACsF,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC,CAACmE,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/E;MACF,CAAC,CAAC,CAACgE,KAAK,CAAChB,WAAW,CAAC;IACvB,CAAC,MAAM;MACL7J,KAAK,CAACuI,GAAG,CACP3D,IAAI,CAACsF,GAAG,EAAE;QACRC,YAAY,EAAE,MAAM;QACpBhC,OAAO,EAAE;UACP,eAAe,EAAE,SAAS,GAAGG,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;UACtD6B,QAAQ,EAAE,CAAC;UACXR,GAAG,EAAEA;QACP,CAAC;QACDS,kBAAkB,WAAlBA,kBAAkBA,CAACC,QAAQ,EAAE;UAC3BrB,YAAY,GAAGhI,IAAI,CAACsJ,KAAK,CAAC,GAAG,GAAGD,QAAQ,CAACE,MAAM,GAAGF,QAAQ,CAACG,KAAK,CAAC;UACjExK,KAAK,CAACyK,QAAQ,CAAC,aAAa,EAAE;YAC5BpF,IAAI,EAAE4D,OAAO;YACb,UAAU,EAAED;UACd,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACxD,IAAI,CAAC,UAAAkF,GAAG,EAAI;QACfxB,OAAO,CAACW,KAAK,CAAC,CAAC;QACf,IAAIlF,IAAI,CAACgG,QAAQ,EAAE;UACjB1K,cAAc,CAACyK,GAAG,CAAC/F,IAAI,EAAEA,IAAI,CAACgG,QAAQ,CAAC;QACzC,CAAC,MAAM;UACL1K,cAAc,CAACyK,GAAG,CAAC/F,IAAI,EAAEA,IAAI,CAACsF,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC,CAACkC,IAAI,CAACsF,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC,CAACmE,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/E;MACF,CAAC,CAAC,CAACgE,KAAK,CAAChB,WAAW,CAAC;IACvB;EACF,CAAC;EACDiB,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB;EAAA,CACD;EACDC,QAAQ,WAARA,QAAQA,CAAA,EAAG,CAAC,CAAC,CAEb;EACA;EACA;EACA;EACA;AACF,CAAC", "ignoreList": []}]}
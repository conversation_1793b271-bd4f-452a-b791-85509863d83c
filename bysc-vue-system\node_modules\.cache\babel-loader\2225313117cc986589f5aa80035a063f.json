{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\account.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\account.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'login',\n  method: 'POST_FORM',\n  // 此类型为自定义提交类型，其实是post form类型\n  path: '/ics/account/login',\n  desc: '登录接口'\n}, {\n  name: 'getToken',\n  method: 'POST_TOKEN',\n  path: '/oauth/oauth/token',\n  desc: '登录接口'\n}, {\n  name: 'switchUser',\n  method: 'POST_FORM',\n  path: '/oauth/oauth/switch-user'\n}, {\n  name: 'getUsers',\n  method: 'GET',\n  path: '/system/account/user/info',\n  desc: '用户信息接口'\n}, {\n  name: 'logout',\n  method: 'POST_FORM',\n  path: '/oauth/oauth/logout',\n  desc: '退出接口'\n}, {\n  name: 'update-password-for-expired',\n  // 修改密码\n  method: 'POST_FORM',\n  path: '/ics/account/update-password-for-expired'\n}, {\n  name: 'getUserInfo',\n  method: 'GET',\n  path: '/ics/account/principal-get' // ?username=fanqz\n}, {\n  name: 'lock',\n  // 锁定\n  method: 'POST_FORM',\n  path: '/ics/account/lock'\n}, {\n  name: 'unLock',\n  // 解锁\n  method: 'POST_FORM',\n  path: '/ics/account/unLock'\n}, {\n  name: 'disabled',\n  // 停用\n  method: 'POST_FORM',\n  path: '/ics/account/disabled'\n}, {\n  name: 'enable',\n  // 启用\n  method: 'POST_FORM',\n  path: '/ics/account/enable'\n}, {\n  name: 'getUser',\n  method: 'GET',\n  path: '/app/account/account-get'\n}, {\n  name: 'findById',\n  method: 'GET',\n  path: '/app/account/account-find-by-id'\n}, {\n  name: 'getAppPassword',\n  method: 'GET',\n  path: '/app/account/account-generate-login-code' // 获取临时密码\n}, {\n  name: 'getUserPage',\n  method: 'POST',\n  path: '/app/account/account-page',\n  postForm: true\n}, {\n  name: 'getUserList',\n  method: 'POST',\n  path: '/app/account/account-list'\n}, {\n  name: 'getPageWithRole',\n  method: 'POST',\n  path: '/app/account/account-page-with-role'\n}, {\n  name: 'socialcallback',\n  method: 'POST',\n  path: '/oauth/social/callback'\n}, {\n  name: 'addUser',\n  method: 'POST',\n  path: '/app/account/enhanced/account-save'\n}, {\n  name: 'deleteUser',\n  method: 'POST',\n  path: '/app/account/account-delete'\n}, {\n  name: 'updateRole',\n  method: 'POST',\n  path: '/app/account/account-updateRole'\n}, {\n  name: 'updateRoleList',\n  method: 'POST',\n  path: '/app/account/account-update-role-list'\n}, {\n  name: 'updateRoleListV2',\n  method: 'POST',\n  path: '/app/xbw/account/updateRoles'\n}, {\n  name: 'accountAuto',\n  method: 'GET',\n  path: '/app/account/account-auto' // ?username=fanqz\n},\n// {\n//   name: 'updatePasswordByToken',\n//   method: 'POST',\n//   path: '/app/account/update-password-by-token'\n// },\n{\n  name: 'updatePasswordByToken',\n  method: 'POST',\n  path: '/app/account/enhanced/update-password-by-token'\n}, {\n  name: 'resetPassword',\n  method: 'GET',\n  path: '/app/account/reset-password'\n}, {\n  name: 'saveUserAreaRel',\n  // 保存账户区域关联关系\n  method: 'POST',\n  path: '/app/ibuilding/userAreaRel/saveUserAreaRel'\n}, {\n  name: 'myAreaList',\n  // 查询我的区域列表\n  method: 'GET',\n  path: '/app/ibuilding/userAreaRel/myAreaList'\n}, {\n  name: 'syncDeptUserInfo',\n  // 同步某一部门账户\n  method: 'POST',\n  path: '/app/ibuilding/syncDeptUserInfo'\n}, {\n  name: 'pageWithFaceImg',\n  // 查询账户分页数据(人脸)\n  method: 'POST',\n  path: '/app/ibuilding/account/pageWithFaceImg'\n},\n// {\n//   name: 'reset-password', // 重置账户密码\n//   method: 'GET',\n//   path: '/app/account/reset-password'\n// },\n{\n  name: 'reset-password',\n  // 重置账户密码\n  method: 'GET',\n  path: '/app/account/enhanced/reset-password'\n}, {\n  name: 'saveAccountApprove',\n  // 设置审批员\n  method: 'POST',\n  path: '/ics/account/saveAccountApprove'\n}];\n\n// 权限接口\n[{\n  name: 'getSysPermission',\n  method: 'GET',\n  path: '/app/auth/permission-get'\n}, {\n  name: 'getPermissionTree',\n  method: 'GET',\n  path: '/app/auth/permission-getPermissionTree'\n}, {\n  name: 'getSysPermissionList',\n  method: 'POST',\n  path: '/app/auth/permission-page'\n}, {\n  name: 'saveSysPermission',\n  method: 'POST',\n  path: '/app/auth/permission-save'\n}, {\n  name: 'deleteSysPermission',\n  method: 'POST',\n  path: '/app/auth/permission-delete'\n}, {\n  name: 'initPermission',\n  method: 'GET',\n  path: '/app/auth/permission-getByParentId'\n}, {\n  name: 'loadPermission',\n  method: 'GET',\n  path: '/app/auth/permission-getByParentId'\n}, {\n  name: 'searchPermission',\n  method: 'POST',\n  path: '/app/auth/permission-findList'\n}, {\n  name: 'getGrantedButtons',\n  method: 'GET',\n  path: '/app/auth/grantedButtons'\n}];", {"version": 3, "names": ["name", "method", "path", "desc", "postForm"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/account.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'login',\r\n    method: 'POST_FORM', // 此类型为自定义提交类型，其实是post form类型\r\n    path: '/ics/account/login',\r\n    desc: '登录接口'\r\n  },\r\n  {\r\n    name: 'getToken',\r\n    method: 'POST_TOKEN',\r\n    path: '/oauth/oauth/token',\r\n    desc: '登录接口'\r\n  },\r\n  {\r\n    name: 'switchUser',\r\n    method: 'POST_FORM',\r\n    path: '/oauth/oauth/switch-user',\r\n  },\r\n  {\r\n    name: 'getUsers',\r\n    method: 'GET',\r\n    path: '/system/account/user/info',\r\n    desc: '用户信息接口'\r\n  },\r\n  {\r\n    name: 'logout',\r\n    method: 'POST_FORM',\r\n    path: '/oauth/oauth/logout',\r\n    desc: '退出接口'\r\n  },\r\n  {\r\n    name: 'update-password-for-expired', // 修改密码\r\n    method: 'POST_FORM',\r\n    path: '/ics/account/update-password-for-expired'\r\n  },\r\n  {\r\n    name: 'getUserInfo',\r\n    method: 'GET',\r\n    path: '/ics/account/principal-get'// ?username=fanqz\r\n  },\r\n  {\r\n    name: 'lock', // 锁定\r\n    method: 'POST_FORM',\r\n    path: '/ics/account/lock'\r\n  },\r\n  {\r\n    name: 'unLock', // 解锁\r\n    method: 'POST_FORM',\r\n    path: '/ics/account/unLock'\r\n  },\r\n  {\r\n    name: 'disabled', // 停用\r\n    method: 'POST_FORM',\r\n    path: '/ics/account/disabled'\r\n  },\r\n  {\r\n    name: 'enable', // 启用\r\n    method: 'POST_FORM',\r\n    path: '/ics/account/enable'\r\n  },\r\n  {\r\n    name: 'getUser',\r\n    method: 'GET',\r\n    path: '/app/account/account-get'\r\n  },\r\n  {\r\n    name: 'findById',\r\n    method: 'GET',\r\n    path: '/app/account/account-find-by-id'\r\n  },\r\n  {\r\n    name: 'getAppPassword',\r\n    method: 'GET',\r\n    path: '/app/account/account-generate-login-code'// 获取临时密码\r\n  },\r\n  {\r\n    name: 'getUserPage',\r\n    method: 'POST',\r\n    path: '/app/account/account-page',\r\n    postForm: true\r\n  },\r\n  {\r\n    name: 'getUserList',\r\n    method: 'POST',\r\n    path: '/app/account/account-list'\r\n  },\r\n  {\r\n    name: 'getPageWithRole',\r\n    method: 'POST',\r\n    path: '/app/account/account-page-with-role'\r\n  },\r\n  {\r\n    name: 'socialcallback',\r\n    method: 'POST',\r\n    path: '/oauth/social/callback'\r\n  },\r\n  {\r\n    name: 'addUser',\r\n    method: 'POST',\r\n    path: '/app/account/enhanced/account-save'\r\n  },\r\n  {\r\n    name: 'deleteUser',\r\n    method: 'POST',\r\n    path: '/app/account/account-delete'\r\n  },\r\n  {\r\n    name: 'updateRole',\r\n    method: 'POST',\r\n    path: '/app/account/account-updateRole'\r\n  },\r\n  {\r\n    name: 'updateRoleList',\r\n    method: 'POST',\r\n    path: '/app/account/account-update-role-list'\r\n  },\r\n  {\r\n    name: 'updateRoleListV2',\r\n    method: 'POST',\r\n    path: '/app/xbw/account/updateRoles'\r\n  },\r\n\r\n  {\r\n    name: 'accountAuto',\r\n    method: 'GET',\r\n    path: '/app/account/account-auto'// ?username=fanqz\r\n  },\r\n  // {\r\n  //   name: 'updatePasswordByToken',\r\n  //   method: 'POST',\r\n  //   path: '/app/account/update-password-by-token'\r\n  // },\r\n  {\r\n    name: 'updatePasswordByToken',\r\n    method: 'POST',\r\n    path: '/app/account/enhanced/update-password-by-token'\r\n  },\r\n  {\r\n    name: 'resetPassword',\r\n    method: 'GET',\r\n    path: '/app/account/reset-password'\r\n  },\r\n  {\r\n    name: 'saveUserAreaRel', // 保存账户区域关联关系\r\n    method: 'POST',\r\n    path: '/app/ibuilding/userAreaRel/saveUserAreaRel'\r\n  },\r\n  {\r\n    name: 'myAreaList', // 查询我的区域列表\r\n    method: 'GET',\r\n    path: '/app/ibuilding/userAreaRel/myAreaList'\r\n  },\r\n  {\r\n    name: 'syncDeptUserInfo', // 同步某一部门账户\r\n    method: 'POST',\r\n    path: '/app/ibuilding/syncDeptUserInfo'\r\n  },\r\n  {\r\n    name: 'pageWithFaceImg', // 查询账户分页数据(人脸)\r\n    method: 'POST',\r\n    path: '/app/ibuilding/account/pageWithFaceImg'\r\n  },\r\n  // {\r\n  //   name: 'reset-password', // 重置账户密码\r\n  //   method: 'GET',\r\n  //   path: '/app/account/reset-password'\r\n  // },\r\n  {\r\n    name: 'reset-password', // 重置账户密码\r\n    method: 'GET',\r\n    path: '/app/account/enhanced/reset-password'\r\n  },\r\n  {\r\n    name: 'saveAccountApprove', // 设置审批员\r\n    method: 'POST',\r\n    path: '/ics/account/saveAccountApprove'\r\n  }\r\n];\r\n\r\n\r\n// 权限接口\r\n[\r\n  {\r\n    name: 'getSysPermission',\r\n    method: 'GET',\r\n    path: '/app/auth/permission-get'\r\n  },\r\n  {\r\n    name: 'getPermissionTree',\r\n    method: 'GET',\r\n    path: '/app/auth/permission-getPermissionTree'\r\n  },\r\n  {\r\n    name: 'getSysPermissionList',\r\n    method: 'POST',\r\n    path: '/app/auth/permission-page'\r\n  },\r\n  {\r\n    name: 'saveSysPermission',\r\n    method: 'POST',\r\n    path: '/app/auth/permission-save'\r\n  },\r\n  {\r\n    name: 'deleteSysPermission',\r\n    method: 'POST',\r\n    path: '/app/auth/permission-delete'\r\n  },\r\n  {\r\n    name: 'initPermission',\r\n    method: 'GET',\r\n    path: '/app/auth/permission-getByParentId'\r\n  },\r\n  {\r\n    name: 'loadPermission',\r\n    method: 'GET',\r\n    path: '/app/auth/permission-getByParentId'\r\n  },\r\n  {\r\n    name: 'searchPermission',\r\n    method: 'POST',\r\n    path: '/app/auth/permission-findList'\r\n  },\r\n  {\r\n    name: 'getGrantedButtons',\r\n    method: 'GET',\r\n    path: '/app/auth/grantedButtons'\r\n  },\r\n];\r\n\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,WAAW;EAAE;EACrBC,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,YAAY;EACpBC,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,IAAI,EAAE,6BAA6B;EAAE;EACrCC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,4BAA4B;AACpC,CAAC,EACD;EACEF,IAAI,EAAE,MAAM;EAAE;EACdC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAAE;EAClBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,0CAA0C;AAClD,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,2BAA2B;EACjCE,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,2BAA2B;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;EACEF,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAAE;EACpBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;EACEF,IAAI,EAAE,gBAAgB;EAAE;EACxBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF;;AAGD;AACA,CACE;EACEF,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAC5BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}
{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\vud.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\vud.js", "mtime": 1745205562828}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}], "contextDependencies": [], "result": ["import CryptoJS from 'crypto-js';\nimport localCache from '@/utils/storage';\n\n// 解密函数 - 用于解密密钥\nfunction decryptKey(encryptedKey) {\n  try {\n    return atob(encryptedKey);\n  } catch (error) {\n    console.error('Key decryption failed:', error);\n    return '';\n  }\n}\n\n// 加密函数\nexport function encryptVud(username) {\n  var timestamp = new Date().getTime();\n  var random = Math.floor(Math.random() * 1000000); // 添加6位随机数\n  var str = \"\".concat(username, \"+\").concat(timestamp).concat(random);\n\n  // 使用正确的加密后的key\n  var encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\n  var cryptoKey = decryptKey(encryptedKey);\n  try {\n    var key = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var iv = CryptoJS.enc.Utf8.parse(cryptoKey);\n    var strUtf8 = CryptoJS.enc.Utf8.parse(str);\n    var encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\n      iv: iv,\n      mode: CryptoJS.mode.CBC,\n      padding: CryptoJS.pad.Pkcs7\n    });\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\n  } catch (error) {\n    console.error('Vud encryption failed:', error);\n    return '';\n  }\n}\n\n// 获取当前用户的 vud\nexport function getCurrentVud() {\n  var username = window.$cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\n  return encryptVud(username);\n}", {"version": 3, "names": ["CryptoJS", "localCache", "decrypt<PERSON>ey", "encrypted<PERSON>ey", "atob", "error", "console", "encryptVud", "username", "timestamp", "Date", "getTime", "random", "Math", "floor", "str", "concat", "cryptoKey", "key", "enc", "Utf8", "parse", "iv", "strUtf8", "encrypted", "AES", "encrypt", "mode", "CBC", "padding", "pad", "Pkcs7", "Base64", "stringify", "ciphertext", "getCurrentVud", "window", "$cookies", "get", "getLocal"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/utils/vud.js"], "sourcesContent": ["import CryptoJS from 'crypto-js';\r\nimport localCache from '@/utils/storage';\r\n\r\n// 解密函数 - 用于解密密钥\r\nfunction decryptKey(encryptedKey) {\r\n  try {\r\n    return atob(encryptedKey);\r\n  } catch (error) {\r\n    console.error('Key decryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 加密函数\r\nexport function encryptVud(username) {\r\n  const timestamp = new Date().getTime();\r\n  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数\r\n  const str = `${username}+${timestamp}${random}`;\r\n\r\n  // 使用正确的加密后的key\r\n  const encryptedKey = \"JEJvd2VpPUAyMDI1Li44OA==\";\r\n  const cryptoKey = decryptKey(encryptedKey);\r\n\r\n  try {\r\n    const key = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);\r\n    const strUtf8 = CryptoJS.enc.Utf8.parse(str);\r\n    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {\r\n      iv: iv,\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7\r\n    });\r\n\r\n    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);\r\n  } catch (error) {\r\n    console.error('Vud encryption failed:', error);\r\n    return '';\r\n  }\r\n}\r\n\r\n// 获取当前用户的 vud\r\nexport function getCurrentVud() {\r\n  const username = window.$cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';\r\n  return encryptVud(username);\r\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,OAAOC,UAAU,MAAM,iBAAiB;;AAExC;AACA,SAASC,UAAUA,CAACC,YAAY,EAAE;EAChC,IAAI;IACF,OAAOC,IAAI,CAACD,YAAY,CAAC;EAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,OAAO,SAASE,UAAUA,CAACC,QAAQ,EAAE;EACnC,IAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACtC,IAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EACpD,IAAMG,GAAG,MAAAC,MAAA,CAAMR,QAAQ,OAAAQ,MAAA,CAAIP,SAAS,EAAAO,MAAA,CAAGJ,MAAM,CAAE;;EAE/C;EACA,IAAMT,YAAY,GAAG,0BAA0B;EAC/C,IAAMc,SAAS,GAAGf,UAAU,CAACC,YAAY,CAAC;EAE1C,IAAI;IACF,IAAMe,GAAG,GAAGlB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC9C,IAAMK,EAAE,GAAGtB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IAC7C,IAAMM,OAAO,GAAGvB,QAAQ,CAACmB,GAAG,CAACC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;IAC5C,IAAMS,SAAS,GAAGxB,QAAQ,CAACyB,GAAG,CAACC,OAAO,CAACH,OAAO,EAAEL,GAAG,EAAE;MACnDI,EAAE,EAAEA,EAAE;MACNK,IAAI,EAAE3B,QAAQ,CAAC2B,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAE7B,QAAQ,CAAC8B,GAAG,CAACC;IACxB,CAAC,CAAC;IAEF,OAAO/B,QAAQ,CAACmB,GAAG,CAACa,MAAM,CAACC,SAAS,CAACT,SAAS,CAACU,UAAU,CAAC;EAC5D,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF;;AAEA;AACA,OAAO,SAAS8B,aAAaA,CAAA,EAAG;EAC9B,IAAM3B,QAAQ,GAAG4B,MAAM,CAACC,QAAQ,CAACC,GAAG,CAAC,aAAa,CAAC,IAAIrC,UAAU,CAACsC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;EAC/F,OAAOhC,UAAU,CAACC,QAAQ,CAAC;AAC7B", "ignoreList": []}]}